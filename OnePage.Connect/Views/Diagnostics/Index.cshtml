@{
    ViewData["Title"] = "System Diagnostics";
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1e1e1e;
            color: #d4d4d4;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .diagnostics {
            background-color: #2d2d30;
            border: 1px solid #3e3e42;
            border-radius: 4px;
            padding: 20px;
            white-space: pre-wrap;
            font-size: 14px;
            line-height: 1.4;
            overflow-x: auto;
        }
        h1 {
            color: #569cd6;
            margin-bottom: 20px;
        }
        .refresh-btn {
            background-color: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        .refresh-btn:hover {
            background-color: #1177bb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>OnePage Connect - System Diagnostics</h1>
        <button class="refresh-btn" onclick="location.reload()">Refresh</button>
        <div class="diagnostics">@ViewBag.Diagnostics</div>
    </div>
</body>
</html>
