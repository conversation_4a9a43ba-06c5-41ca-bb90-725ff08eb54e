﻿<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1Page | Search Less, Sell More!</title>
    <link href="~/css/normalize.css" rel="stylesheet" type="text/css">
    <link href="~/css/webflow.css" rel="stylesheet" type="text/css">
    <link href="~/css/whatelse.webflow.css" rel="stylesheet" type="text/css">
    <link rel="icon" href="https://i1.wp.com/www.get1page.com/wp-content/uploads/2020/07/cropped-logo.2x-1.png?fit=32%2C32&#038;ssl=1" sizes="32x32" />
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300&display=swap" rel="stylesheet">


    @*<script type="text/javascript">
            WebFont.load({
                google: {
                    families: ["Montserrat"]
                }
            });
        </script>*@
    @* @Styles.Render("~/Content/css") *@
    @* @Scripts.Render("~/bundles/modernizr") *@
    <link rel="stylesheet" href="~/css/bootstrap.css" />
    <link rel="stylesheet" href="~/css/bootstrap-theme.css" />
    <link rel="stylesheet" href="~/css/normalize.css" />
    <link rel="stylesheet" href="~/css/Site.css" />
    <link rel="stylesheet" href="~/css/webflow.css" />
    <link rel="stylesheet" href="~/css/whatelse.webflow.css" />
    
    
    <script src="~/js/modernizr-2.6.2.js"></script>
    <script src="~/js/modernizr-2.8.3.js"></script>
    
    <script type='text/javascript'>
        var appInsights = window.appInsights || function (config) {
            function r(config) { t[config] = function () { var i = arguments; t.queue.push(function () { t[config].apply(t, i) }) } }
            var t = { config: config }, u = document, e = window, o = 'script', s = u.createElement(o), i, f; for (s.src = config.url || '//az416426.vo.msecnd.net/scripts/a/ai.0.js', u.getElementsByTagName(o)[0].parentNode.appendChild(s), t.cookie = u.cookie, t.queue = [], i = ['Event', 'Exception', 'Metric', 'PageView', 'Trace', 'Ajax']; i.length;)r('track' + i.pop()); return r('setAuthenticatedUserContext'), r('clearAuthenticatedUserContext'), config.disableExceptionTracking || (i = 'onerror', r('_' + i), f = e[i], e[i] = function (config, r, u, e, o) { var s = f && f(config, r, u, e, o); return s !== !0 && t['_' + i](config, r, u, e, o), s }), t
        }({
            instrumentationKey: 'd987853c-75f3-4f90-8938-ed7cf0d254e3'
        });

        window.appInsights = appInsights;
        appInsights.trackPageView();
    </script>
</head>
<body class="body">
    <div class="grey-section">
        <div class="center w-container wrapper">
            <img style="align-self:center;height:100px; width:100px;" src="~/images/onepagelogo.png" >
            <div class="grey-section">
                <div class="center w-container wrapper">
                    <div class="feature-cards-v4">
                        <div data-ix="fade-up-1" class="feature-card-v4">
                            <div class="feature-card-v4-heading">
                                @RenderBody()
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr />
            <footer style="font-size:smaller">
                <div class="author-copyright">
                    @*<div>Patent Pending Flow</div>*@
                    <div>@ViewBag.AllRightsRes</div>
                    <div>
                        <a href="https://www.get1page.com/privacy-policy/">@ViewBag.PrivacyPolicy</a> &nbsp; &nbsp; &nbsp;
                        <a href="https://www.get1page.com/terms-of-use/">@ViewBag.Terms</a> &nbsp; &nbsp; &nbsp;
                        <a href="https://www.get1page.com/contact/">@ViewBag.Contact</a> &nbsp; &nbsp; &nbsp;
                    </div>
                </div>
            </footer>
        </div>
    </div>
    @* @Scripts.Render("~/bundles/jquery") *@
    @* @Scripts.Render("~/bundles/bootstrap") *@
    @* <link rel="stylesheet" href="~/Content/css" /> *@
    <script src="~/lib/jquery/dist/jquery.js"></script>
    <script src="~/css/bootstrap.css"></script>
    <script src="~/css/bootstrap-theme.css"></script>
    
    @RenderSection("scripts", required: false)
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-110469189-1"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'UA-110469189-1');
    </script>

</body>
</html>