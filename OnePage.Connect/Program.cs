using Microsoft.EntityFrameworkCore;
using OnePage.Connect.Context;
using OnePage.Connect.Extensions;
using OnePage.Connect.Services;
using Serilog;
using Serilog.Events;
using Serilog.Sinks.MSSqlServer;
using System.Collections.ObjectModel;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();

builder.Services.AddScoped<UserProviderCachingAndDbServices>();
builder.Services.AddHttpClient();
builder.WebHost.ConfigureKestrel(options => options.AddServerHeader = false);



builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddSingleton<TranslationService>();
var environment = builder.Environment;
// if (builder.Environment.IsDevelopment())
// {
//     builder.Environment.EnvironmentName = "Production";
// }

var appSettingsFile = environment.IsProduction() ? "appsettings.Production.json" : "appsettings.Development.json";
builder.Configuration.AddJsonFile(appSettingsFile, optional: false, reloadOnChange: true);



//ASPNETCORE_ENVIRONMENT
// var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");

var connectionStrings = builder.Configuration.GetSection("AzureKeyVaultConnectionStrings");


foreach (var connectionString in connectionStrings.GetChildren())
{

    var dbName = connectionString.Key;
    var fullConnectionString = connectionString.Value;


    //if (builder.Environment.IsProduction())
    //{
    //    var vaultName = fullConnectionString;
    //    fullConnectionString = await DbContextExtensions.GetAzureKeyVaultConnectionString(vaultName);
    //}


    switch (dbName)
    {
        case "WEAdminConnectionString":
            builder.Services.AddDbContext<WEAdminEntitiesDBContext>(options =>
            {
                options.UseSqlServer(fullConnectionString);
            });
            break;
        case "WEDataConnectionString":
            builder.Services.AddDbContext<WEDataEntitiesDBContext>(options =>
            {
                options.UseSqlServer(fullConnectionString);
            });
            break;
        case "WEOrgConnectionString":
            builder.Services.AddDbContext<WEOrgEntitiesDBContext>(options =>
            {
                options.UseSqlServer(fullConnectionString);
            });
            break;
        case "WEPeopleConnectionString":
            builder.Services.AddDbContext<WEPeopleEntitiesDBContext>(options =>
            {
                options.UseSqlServer(fullConnectionString);
            });
            break;
        case "WETokenConnectionString":
            builder.Services.AddDbContext<WETokenEntitiesDBContext>(options =>
            {
                options.UseSqlServer(fullConnectionString);
            });
            break;
        default:
            throw new ArgumentException($"Unknown database connection string name: {dbName}");
    }
}
builder.Services.AddScoped<ProviderService>();
var cs = builder.Configuration.GetConnectionString("WEDataConnectionString");

var columnOptions = new ColumnOptions
{
    AdditionalColumns = new Collection<SqlColumn>
    {
        new SqlColumn("UserEmailId", System.Data.SqlDbType.NVarChar),
        new SqlColumn("UserId",System.Data.SqlDbType.UniqueIdentifier)
    }
};

Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Debug()
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .WriteTo.MSSqlServer(
    connectionString: cs,
    tableName: "Logs",
    autoCreateSqlTable: true,
    restrictedToMinimumLevel: LogEventLevel.Error,
    columnOptions: columnOptions).
    Filter.ByIncludingOnly(logEvent =>
    logEvent.Level == LogEventLevel.Information && logEvent.Properties.ContainsKey("UserId") || 
    logEvent.Level == LogEventLevel.Error)
    .CreateLogger();

builder.Host.UseSerilog();




// var connectionString = await DbContextExtensions.GetAzureKeyVaultConnectionString("eu-fc-ss-ad");
// builder.Services.AddDbContext<WEAdminEntitiesDBContext>(options =>
// {
//     options.UseSqlServer(connectionString);
// });
//
// connectionString = await DbContextExtensions.GetAzureKeyVaultConnectionString("eu-fc-ss-dt");
//
// builder.Services.AddDbContext<WEDataEntitiesDBContext>(options =>
// {
//     options.UseSqlServer(connectionString);
// });
//  connectionString = await  DbContextExtensions.GetAzureKeyVaultConnectionString("eu-fc-ss-or");
// builder.Services.AddDbContext<WEOrgEntitiesDBContext>(options =>
// {
//     options.UseSqlServer(connectionString);
// });
// connectionString = await DbContextExtensions.GetAzureKeyVaultConnectionString("eu-fc-ss-pp");
// builder.Services.AddDbContext<WEPeopleEntitiesDBContext>(options =>
// {
//     options.UseSqlServer(connectionString);
// });
// connectionString = await DbContextExtensions.GetAzureKeyVaultConnectionString("eu-fc-ss-tk");
//
// builder.Services.AddDbContext<WETokenEntitiesDBContext>(options =>
// {
//     options.UseSqlServer(connectionString);
// });









var app = builder.Build();

// Add startup error logging
try
{
    Log.Information("Application starting up...");
    Log.Information("Environment: {Environment}", app.Environment.EnvironmentName);
    Log.Information("Content Root: {ContentRoot}", app.Environment.ContentRootPath);

    // Test database connections early - but don't fail startup if they fail
    try
    {
        using (var scope = app.Services.CreateScope())
        {
            var adminContext = scope.ServiceProvider.GetRequiredService<WEAdminEntitiesDBContext>();
            var dataContext = scope.ServiceProvider.GetRequiredService<WEDataEntitiesDBContext>();

            Log.Information("Testing database connections...");
            await adminContext.Database.CanConnectAsync();
            await dataContext.Database.CanConnectAsync();
            Log.Information("Database connections successful");
        }
    }
    catch (Exception dbEx)
    {
        Log.Warning(dbEx, "Database connection test failed, but continuing startup");
    }
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application failed to start due to an exception");
    // Don't throw - let the app try to start anyway
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    // Temporarily enable developer exception page for debugging
    if (app.Configuration.GetValue<bool>("ShowDetailedErrors", false))
    {
        app.UseDeveloperExceptionPage();
    }
    else
    {
        app.UseExceptionHandler("/Home/Error");
    }
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
else
{
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// Add a simple health check endpoint
app.MapGet("/health", () =>
{
    return Results.Ok(new {
        Status = "Healthy",
        Timestamp = DateTime.UtcNow,
        Environment = app.Environment.EnvironmentName
    });
});

app.Run();