{"AzureKeyVaultConnectionStrings": {"WEAdminConnectionString": "Server=eu-ne-ss-st.database.windows.net;Database=eu-fc-ss-ad-st;User=eu-ne-sd-st;Password=***$Y57lsKOh$;MultipleActiveResultSets=true;", "WEDataConnectionString": "Server=eu-ne-ss-st.database.windows.net;Database=eu-fc-ss-dt-st;User=eu-ne-sd-st;Password=***$Y57lsKOh$;MultipleActiveResultSets=true;", "WEOrgConnectionString": "Server=eu-ne-ss-st.database.windows.net;Database=eu-fc-ss-or-st;User=eu-ne-sd-st;Password=***$Y57lsKOh$;MultipleActiveResultSets=true;", "WEPeopleConnectionString": "Server=eu-ne-ss-st.database.windows.net;Database=eu-fc-ss-pp-st;User=eu-ne-sd-st;Password=***$Y57lsKOh$;MultipleActiveResultSets=true;", "WETokenConnectionString": "Server=eu-ne-ss-st.database.windows.net;Database=eu-fc-ss-tk-st;User=eu-ne-sd-st;Password=***$Y57lsKOh$;MultipleActiveResultSets=true;"}, "ConnectionStrings": {"WEDataConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-dt;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}, "BaseUrl": "https://eu-ne-app-st.azurewebsites.net", "ServerSettings": {"Url": "https://eu-ne-st-sz.azurewebsites.net/", "ServerType": 18}, "WebproxyUrl": "https://eu-ne-proxy-st.azurewebsites.net", "MsMailIdentifier": "89C7D17D-58A3-4E45-B152-1ADBDC47B268", "GoogleWebhook": "", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}}