using Azure.Identity;
using Azure.Security.KeyVault.Secrets;

namespace OnePage.Connect.Extensions;

public static class DbContextExtensions
{
    public  static async Task<string> GetAzureKeyVaultConnectionString( string secretName)
    {
        try
        {
            var credential = new DefaultAzureCredential();
            var client = new SecretClient(new Uri($"https://eu-ne-cn-secrets.vault.azure.net/"), credential);
            var secret = await client.GetSecretAsync(secretName);
            return secret.Value.Value;
        }
        catch (Exception ex)
        {
          
            Console.WriteLine($"Error retrieving secret: {ex.Message}");
            
            throw; 
        }
    }
}