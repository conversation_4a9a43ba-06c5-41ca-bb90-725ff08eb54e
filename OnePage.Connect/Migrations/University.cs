﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class University
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public string Aka { get; set; } = null!;

    public bool IsDataKnown { get; set; }

    public string? Founded { get; set; }

    public string? Country { get; set; }

    public string? Locality { get; set; }

    public string? Address { get; set; }

    public string? Description { get; set; }

    public string? RefId { get; set; }

    public string? Employees { get; set; }

    public virtual ICollection<PersonUniversity> PersonUniversities { get; set; } = new List<PersonUniversity>();

    public virtual ICollection<UniversityDomain> UniversityDomains { get; set; } = new List<UniversityDomain>();

    public virtual ICollection<UniversityLocation> UniversityLocations { get; set; } = new List<UniversityLocation>();

    public virtual ICollection<UniversityPhone> UniversityPhones { get; set; } = new List<UniversityPhone>();

    public virtual ICollection<UniversitySocial> UniversitySocials { get; set; } = new List<UniversitySocial>();

    public virtual ICollection<UniversityStory> UniversityStories { get; set; } = new List<UniversityStory>();

    public virtual ICollection<UniversityTag> UniversityTags { get; set; } = new List<UniversityTag>();
}
