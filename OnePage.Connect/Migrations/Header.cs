﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Header
{
    public Guid Id { get; set; }

    public Guid Urlid { get; set; }

    public string Name { get; set; } = null!;

    public string? Prefix { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public virtual Url Url { get; set; } = null!;
}
