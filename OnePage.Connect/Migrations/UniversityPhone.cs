﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UniversityPhone
{
    public Guid Id { get; set; }

    public Guid UniversityId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public Guid? CreatedBy { get; set; }

    public Guid? ModifiedBy { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsVerified { get; set; }

    public string? ProvidedNumber { get; set; }

    public string? SanitizedNumber { get; set; }

    public string? Extn { get; set; }

    public string? CountryId { get; set; }

    public bool? IsPastPhone { get; set; }

    public bool? IsPreferred { get; set; }

    public int? PhoneType { get; set; }

    public virtual University University { get; set; } = null!;
}
