﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Product
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public bool IsAcquired { get; set; }

    public bool IsActive { get; set; }

    public string? EarlierName { get; set; }

    public string? Aka { get; set; }

    public virtual ICollection<CompanyProduct> CompanyProducts { get; set; } = new List<CompanyProduct>();

    public virtual ICollection<ProductCategory> ProductCategories { get; set; } = new List<ProductCategory>();

    public virtual ICollection<Stack> Stacks { get; set; } = new List<Stack>();
}
