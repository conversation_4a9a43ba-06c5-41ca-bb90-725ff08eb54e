﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ContactHistory
{
    public int? Type { get; set; }

    public string? Title { get; set; }

    public string? SubTitle { get; set; }

    public DateTime? StartTime { get; set; }

    public DateTime? EndTime { get; set; }

    public string? ReferenceId { get; set; }

    public int? CallType { get; set; }

    public string? DeepLink { get; set; }

    public Guid? ContactId { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public DateTime? UserId { get; set; }

    public bool? IsActive { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }
}
