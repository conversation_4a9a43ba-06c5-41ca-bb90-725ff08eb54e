﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Tag
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public long RefCount { get; set; }

    public long ReadCount { get; set; }

    public bool IsActive { get; set; }

    public int TagTypeId { get; set; }

    public virtual ICollection<CompanyTag> CompanyTags { get; set; } = new List<CompanyTag>();

    public virtual ICollection<PersonTag> PersonTags { get; set; } = new List<PersonTag>();

    public virtual ICollection<StoryTag> StoryTags { get; set; } = new List<StoryTag>();

    public virtual ICollection<UniversityTag> UniversityTags { get; set; } = new List<UniversityTag>();
}
