﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ActivityLog
{
    public string? Reference { get; set; }

    public int? ActivityTypeId { get; set; }

    public string? PhoneNumber { get; set; }

    public bool? IsIdentified { get; set; }

    public bool? IsContact { get; set; }

    public bool? IsCcall { get; set; }

    public string? Notes { get; set; }

    public bool? IsIncoming { get; set; }

    public string? ContactName { get; set; }

    public DateTime? CallStartTime { get; set; }

    public DateTime? CallEndTime { get; set; }

    public string? CallId { get; set; }

    public string? ConvId { get; set; }

    public string? Sthree { get; set; }

    public string? Sfour { get; set; }

    public bool? Bthree { get; set; }

    public bool? Bfour { get; set; }

    public string? Transcription { get; set; }

    public string? TranscriptionData { get; set; }

    public byte? TranscriptionSource { get; set; }

    public Guid? ContactId { get; set; }

    public Guid? ProviderId { get; set; }

    public string? Url { get; set; }

    public bool? IsEdited { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public Guid? UserId { get; set; }

    public bool? IsActive { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }
}
