﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Provision
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public Guid? OrgDirectoryId { get; set; }

    public string? OrgCustomerId { get; set; }

    public Guid? ProviderTypeId { get; set; }

    public Guid? ProviderId { get; set; }

    public Guid? AppId { get; set; }

    public Guid? IdpproviderId { get; set; }

    public Guid? UserId { get; set; }

    public Guid? UserProviderId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public bool IsConverted { get; set; }

    public bool IsEnterpriseConverted { get; set; }

    public DateTime? DisabledDate { get; set; }

    public bool IsRedeemed { get; set; }

    public string? EmailAddress { get; set; }

    public string? PhoneNumber { get; set; }

    public string? Salutation { get; set; }

    public string? Comment { get; set; }

    public string? SanitizedNumber { get; set; }

    public string FirstName { get; set; } = null!;

    public string? MiddleName { get; set; }

    public string LastName { get; set; } = null!;

    public string? EnterpriseUserId { get; set; }

    public string? IdpappId { get; set; }

    public bool IsProvisioned { get; set; }

    public bool IsPayed { get; set; }

    public bool IsFree { get; set; }

    public bool IsRequested { get; set; }

    public bool IsAccepted { get; set; }

    public string? UserCustomerId { get; set; }

    public int? Duration { get; set; }

    public string? CountryId { get; set; }

    public int? Ptype { get; set; }

    public bool? IsPurchasedByUser { get; set; }

    public string? SubscriptionId { get; set; }

    public DateTime? PurchasedDate { get; set; }

    public DateTime? DueDate { get; set; }

    public DateTime? LastPaymentDate { get; set; }

    public bool? IsPurchasedOnIos { get; set; }

    public bool? IsPurchasedOnAndroid { get; set; }

    public int? Source { get; set; }

    public string? ProductId { get; set; }

    public string? PurchaseId { get; set; }

    public bool? IsMonthly { get; set; }

    public int? PurchaseState { get; set; }

    public bool? IsTrial { get; set; }

    public bool? IsClaimed { get; set; }

    public bool? IsTrialCompleted { get; set; }

    public bool? IsPaymentStoped { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual OrgDirectory? OrgDirectory { get; set; }
}
