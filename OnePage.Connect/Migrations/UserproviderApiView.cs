﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserproviderApiView
{
    public Guid Id { get; set; }

    public string? Email { get; set; }

    public bool IsActive { get; set; }

    public Guid AppId { get; set; }

    public int? LanguageId { get; set; }

    public Guid UserStatusId { get; set; }

    public bool ShouldShowAds { get; set; }

    public Guid UpId { get; set; }

    public Guid? OrgId { get; set; }

    public Guid ProviderId { get; set; }

    public Guid? ProvisionId { get; set; }

    public bool IsProvisioned { get; set; }

    public Guid UserEmailId { get; set; }

    public Guid UsId { get; set; }

    public Guid SettingId { get; set; }

    public string? Value { get; set; }
}
