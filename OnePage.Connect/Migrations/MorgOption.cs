﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class MorgOption
{
    public Guid Id { get; set; }

    public Guid? OrgId { get; set; }

    public Guid? NatureId { get; set; }

    public Guid? QuestionId { get; set; }

    public Guid? OptionId { get; set; }

    public string? Value { get; set; }

    public bool? IsLocked { get; set; }

    public bool? IsActive { get; set; }

    public virtual Minature? Nature { get; set; }

    public virtual Option? Option { get; set; }

    public virtual Miorg? Org { get; set; }

    public virtual Question? Question { get; set; }
}
