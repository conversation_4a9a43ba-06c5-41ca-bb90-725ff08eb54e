﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Contact
{
    public Guid? OtherUserId { get; set; }

    public string? Salutation { get; set; }

    public string? FirstName { get; set; }

    public string? MiddleName { get; set; }

    public string? LastName { get; set; }

    public Guid? ProviderId { get; set; }

    public Guid? ContactTypeId { get; set; }

    public int? Sequence { get; set; }

    public bool? IsActive { get; set; }

    public string? Reference { get; set; }

    public string? EmailDisplay { get; set; }

    public string? PhoneNumbers { get; set; }

    public bool? IsEnabled { get; set; }

    public string? Company { get; set; }

    public string? Desig { get; set; }

    public string? Nick { get; set; }

    public bool? IsAggregate { get; set; }

    public string? Display { get; set; }

    public string? Prefix { get; set; }

    public string? Suffix { get; set; }

    public string? Websites { get; set; }

    public Guid? UserProviderId { get; set; }

    public bool? IsUnknown { get; set; }

    public string? OtherIds { get; set; }

    public bool? IsContact { get; set; }

    public bool? IsDir { get; set; }

    public bool? IsCcontact { get; set; }

    public bool? IsClead { get; set; }

    public string? Crmid { get; set; }

    public bool? IsCustom { get; set; }

    public string? Alias { get; set; }

    public bool? BlockAlert { get; set; }

    public string? DirIds { get; set; }

    public string? Linked { get; set; }

    public bool? Show { get; set; }

    public DateTime? LastProcessed { get; set; }

    public bool? IsPhone { get; set; }

    public bool? IsProvider { get; set; }

    public bool? IsLinked { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }

    public bool? Bthree { get; set; }

    public bool? Bfour { get; set; }

    public string? Sthree { get; set; }

    public string? Sfour { get; set; }

    public bool? Ignored { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public Guid? UserId { get; set; }
}
