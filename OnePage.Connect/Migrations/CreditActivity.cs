﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class CreditActivity
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public int? VanishingCredits { get; set; }

    public int? Credit { get; set; }

    public DateTime? Redeemdate { get; set; }

    public string? ReceiptId { get; set; }

    public DateTime? TransactionDateUtc { get; set; }

    public string? ProductId { get; set; }

    public bool? AutoRenewing { get; set; }

    public string? PurchaseToken { get; set; }

    public bool? IsAcknowledged { get; set; }

    public string? Payload { get; set; }

    public int? PurchaseState { get; set; }

    public byte? ConsumptionState { get; set; }

    public bool IsActive { get; set; }

    public DateTime ModifiedDate { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? CreditRedeemedDate { get; set; }

    public Guid? ContactId { get; set; }

    public Guid? UserDeviceId { get; set; }

    public int? RedeemedVanishngCredit { get; set; }

    public int? RedeemedNonVanishngCredit { get; set; }

    public Guid? OrgId { get; set; }

    public bool? IsCredited { get; set; }

    public bool? IsDebited { get; set; }
}
