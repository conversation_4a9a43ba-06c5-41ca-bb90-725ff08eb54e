﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class CompanyTag
{
    public Guid Id { get; set; }

    public Guid CompanyId { get; set; }

    public Guid TagId { get; set; }

    public Guid? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public virtual Company Company { get; set; } = null!;

    public virtual Tag Tag { get; set; } = null!;
}
