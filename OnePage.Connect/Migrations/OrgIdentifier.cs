﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class OrgIdentifier
{
    public Guid Id { get; set; }

    public Guid OrgProviderId { get; set; }

    public Guid IdentifierId { get; set; }

    public string Value { get; set; } = null!;

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int ShowSequence { get; set; }

    public virtual OrgProvider OrgProvider { get; set; } = null!;
}
