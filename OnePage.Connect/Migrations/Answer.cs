﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Answer
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public Guid QuestionId { get; set; }

    public string? Answer1 { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int ShowSequence { get; set; }

    public virtual Question1 Question { get; set; } = null!;

    public virtual User User { get; set; } = null!;
}
