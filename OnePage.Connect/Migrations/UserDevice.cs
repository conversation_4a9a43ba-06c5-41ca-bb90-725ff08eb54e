﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserDevice
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public Guid DeviceTypeId { get; set; }

    public string? Number { get; set; }

    public string? SanitizedNumber { get; set; }

    public string? PhoneNumber { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public bool IsPhoneValidated { get; set; }

    public bool IsAccessible { get; set; }

    public bool IsPersonal { get; set; }

    public bool IsPrimary { get; set; }

    public string? CountryId { get; set; }

    public int? Ptype { get; set; }

    public string? DeviceData { get; set; }

    public bool ImportContact { get; set; }

    public bool PuchRequired { get; set; }

    public bool ShouldRefesh { get; set; }

    public bool UnknownPush { get; set; }

    public bool ContactPush { get; set; }

    public bool Crmpush { get; set; }

    public bool DirectoryPush { get; set; }

    public bool CallNotifications { get; set; }

    public bool CalendarNotifications { get; set; }
}
