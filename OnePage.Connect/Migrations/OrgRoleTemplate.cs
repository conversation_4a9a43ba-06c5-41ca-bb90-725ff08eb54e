﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class OrgRoleTemplate
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public Guid ProviderId { get; set; }

    public string Name { get; set; } = null!;

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int? ShowSequence { get; set; }

    public string? Cards { get; set; }

    public string? Actions { get; set; }

    public string? Triggers { get; set; }

    public virtual Org Org { get; set; } = null!;
}
