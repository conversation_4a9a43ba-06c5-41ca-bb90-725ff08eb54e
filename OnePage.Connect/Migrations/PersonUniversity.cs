﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PersonUniversity
{
    public Guid Id { get; set; }

    public Guid PersonId { get; set; }

    public Guid UniverisityId { get; set; }

    public bool IsCurrent { get; set; }

    public string? Title { get; set; }

    public string? Notes { get; set; }

    public bool IsEmployed { get; set; }

    public bool IsVistingFaculty { get; set; }

    public bool IsExchangeStudent { get; set; }

    public string? StartDate { get; set; }

    public string? EndDate { get; set; }

    public string? Degrees { get; set; }

    public string? Majors { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsVerified { get; set; }

    public string? Summary { get; set; }

    public bool IsActive { get; set; }

    public bool IsMarkedIncorrect { get; set; }

    public virtual Person Person { get; set; } = null!;

    public virtual University Univerisity { get; set; } = null!;
}
