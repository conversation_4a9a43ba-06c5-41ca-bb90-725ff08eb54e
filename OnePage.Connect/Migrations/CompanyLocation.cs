﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class CompanyLocation
{
    public Guid Id { get; set; }

    public Guid CompanyId { get; set; }

    public Guid LocationId { get; set; }

    public Guid? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public virtual Company Company { get; set; } = null!;

    public virtual Location Location { get; set; } = null!;
}
