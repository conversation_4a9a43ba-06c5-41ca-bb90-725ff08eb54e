﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ProviderIdentifier
{
    public Guid Id { get; set; }

    public Guid ProviderId { get; set; }

    public Guid IdentifierId { get; set; }

    public string Value { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public bool IsActive { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public virtual Provider Provider { get; set; } = null!;
}
