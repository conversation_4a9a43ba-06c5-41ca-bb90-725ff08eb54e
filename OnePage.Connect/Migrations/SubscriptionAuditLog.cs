﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class SubscriptionAuditLog
{
    public int Id { get; set; }

    public int? SubscriptionId { get; set; }

    public string? Attribute { get; set; }

    public string? OldValue { get; set; }

    public string? NewValue { get; set; }

    public DateTime? CreateDate { get; set; }

    public int? CreateBy { get; set; }

    public virtual Subscription? Subscription { get; set; }
}
