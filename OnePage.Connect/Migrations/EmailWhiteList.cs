﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class EmailWhiteList
{
    public Guid Id { get; set; }

    public string Email { get; set; } = null!;

    public bool IsActive { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public Guid? Coupon { get; set; }

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public string? Phone { get; set; }

    public bool IsClaimed { get; set; }
}
