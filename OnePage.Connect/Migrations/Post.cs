﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Post
{
    public Guid Id { get; set; }

    public Guid Urlid { get; set; }

    public string Name { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public bool IgnoreFormat { get; set; }

    public virtual Url Url { get; set; } = null!;
}
