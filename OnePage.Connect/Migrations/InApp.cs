﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class InApp
{
    public Guid Id { get; set; }

    public string ProductId { get; set; } = null!;

    public bool IsSubscription { get; set; }

    public int SubscriptionType { get; set; }

    public string Name { get; set; } = null!;

    public string Description { get; set; } = null!;

    public int CreditsToAdd { get; set; }

    public string? IconName { get; set; }

    public bool IsActive { get; set; }

    public int ShowSequence { get; set; }
}
