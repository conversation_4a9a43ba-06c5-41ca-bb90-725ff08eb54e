﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Url
{
    public Guid Id { get; set; }

    public Guid UrltypeId { get; set; }

    public string Name { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public bool IsApi { get; set; }

    public byte Httptype { get; set; }

    public bool HasHeaders { get; set; }

    public bool NeedsData { get; set; }

    public bool IsMobile { get; set; }

    public bool IsApp { get; set; }

    public string? Identifier { get; set; }

    public int ShowOrder { get; set; }

    public string? SuccessUrl { get; set; }

    public string? FailureUrl { get; set; }

    public bool? IsBeforeAuth { get; set; }

    public Guid? CardId { get; set; }

    public bool IsWeb { get; set; }

    public string? FieldsTopick { get; set; }

    public string? DataPath { get; set; }

    public bool IsMultiUrl { get; set; }

    public virtual ICollection<ActionUrl> ActionUrls { get; set; } = new List<ActionUrl>();

    public virtual ICollection<CardUrl> CardUrls { get; set; } = new List<CardUrl>();

    public virtual ICollection<Header> Headers { get; set; } = new List<Header>();

    public virtual ICollection<Post> Posts { get; set; } = new List<Post>();

    public virtual ICollection<ProviderUrl> ProviderUrls { get; set; } = new List<ProviderUrl>();

    public virtual Urltype Urltype { get; set; } = null!;
}
