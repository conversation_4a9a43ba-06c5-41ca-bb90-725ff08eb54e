﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class AppLanguage
{
    public Guid Id { get; set; }

    public Guid AppId { get; set; }

    public int LanguageId { get; set; }

    public string SendGridKey { get; set; } = null!;

    public bool IsActive { get; set; }

    public DateTime Createddate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public virtual App App { get; set; } = null!;

    public virtual ICollection<AppLanguageTemplate> AppLanguageTemplates { get; set; } = new List<AppLanguageTemplate>();
}
