﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Identifier
{
    public Guid Id { get; set; }

    public Guid ProviderId { get; set; }

    public string Name { get; set; } = null!;

    public byte Type { get; set; }

    public Guid? Urlid { get; set; }

    public bool IsBeforeAuth { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int ShowSequence { get; set; }

    public string? Value { get; set; }

    public bool IsAdmin { get; set; }

    public bool IsUserPostLoginSettings { get; set; }

    public bool IsCustomer { get; set; }

    public bool OnTheFly { get; set; }

    public virtual Provider Provider { get; set; } = null!;
}
