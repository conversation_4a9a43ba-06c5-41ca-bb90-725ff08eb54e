﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class CompanyStory
{
    public Guid Id { get; set; }

    public Guid CompanyId { get; set; }

    public Guid StoryId { get; set; }

    public Guid? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public virtual Company Company { get; set; } = null!;

    public virtual Story Story { get; set; } = null!;
}
