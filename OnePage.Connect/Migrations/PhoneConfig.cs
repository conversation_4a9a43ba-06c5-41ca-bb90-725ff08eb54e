﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PhoneConfig
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public string? Did { get; set; }

    public string? PhoneNumber { get; set; }

    public string? Extn { get; set; }

    public Guid? ProviderId { get; set; }

    public string? Sipaddress { get; set; }

    public bool IsActive { get; set; }

    public string Prefix { get; set; } = null!;

    public string InstanceUrl { get; set; } = null!;

    public string ApiKey { get; set; } = null!;

    public string Name { get; set; } = null!;
}
