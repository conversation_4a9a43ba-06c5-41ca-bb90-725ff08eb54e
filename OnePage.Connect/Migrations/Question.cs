﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Question
{
    public Guid Id { get; set; }

    public Guid? NatureId { get; set; }

    public Guid? TypeId { get; set; }

    public string? Name { get; set; }

    public bool? IsActive { get; set; }

    public virtual ICollection<MorgOption> MorgOptions { get; set; } = new List<MorgOption>();

    public virtual Minature? Nature { get; set; }

    public virtual ICollection<Option> Options { get; set; } = new List<Option>();

    public virtual QuestionType? Type { get; set; }
}
