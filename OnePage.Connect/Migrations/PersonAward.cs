﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PersonAward
{
    public Guid Id { get; set; }

    public Guid PersonId { get; set; }

    public string Title { get; set; } = null!;

    public string? Awarder { get; set; }

    public bool IsActive { get; set; }

    public DateTime? AwardDate { get; set; }

    public string? Summary { get; set; }

    public virtual Person Person { get; set; } = null!;
}
