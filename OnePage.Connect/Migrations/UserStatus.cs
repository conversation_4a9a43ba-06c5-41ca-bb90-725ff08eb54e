﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserStatus
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public bool Walkthrough { get; set; }

    public bool MobileVerification { get; set; }

    public bool Otp { get; set; }

    public bool Registration { get; set; }

    public bool AndroidContacts { get; set; }

    public bool IOscontacts { get; set; }

    public bool FacebookContacts { get; set; }

    public bool LinkedInContacts { get; set; }

    public bool TwitterContacts { get; set; }

    public bool FreeEmail { get; set; }

    public bool FreeStorage { get; set; }

    public bool FreeFacebook { get; set; }

    public bool FreeTwitter { get; set; }

    public bool FreeLinkedIn { get; set; }

    public bool PurchaseEmail { get; set; }

    public bool PurchaseStorage { get; set; }

    public bool PurchaseEnterpriseEmail { get; set; }

    public bool PurchaseEnterpriseStorage { get; set; }

    public bool PurchaseEnterpriseApp { get; set; }

    public bool ProvisionApp { get; set; }

    public bool IsDrakTheme { get; set; }

    public bool ShouldShowAds { get; set; }
}
