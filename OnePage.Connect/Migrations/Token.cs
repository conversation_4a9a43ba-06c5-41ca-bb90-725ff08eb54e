﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Token
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public Guid? UserDeviceId { get; set; }

    public Guid AppId { get; set; }

    public string? Details { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public DateTime? Ttl { get; set; }

    public string? Ip { get; set; }

    public string? BrowserData { get; set; }

    public string? UserAgent { get; set; }

    public bool IsWeb { get; set; }

    public virtual ICollection<Abuse> Abuses { get; set; } = new List<Abuse>();
}
