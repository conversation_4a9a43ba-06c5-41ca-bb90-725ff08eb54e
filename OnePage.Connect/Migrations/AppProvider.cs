﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class AppProvider
{
    public Guid Id { get; set; }

    public Guid AppId { get; set; }

    public Guid ProviderId { get; set; }

    public string? OrgRoleId { get; set; }

    public string? UserId { get; set; }

    public bool IsActive { get; set; }

    public DateTime Createddate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int ShowSequence { get; set; }

    public string? ShowForOrg { get; set; }

    public virtual Provider Provider { get; set; } = null!;
}
