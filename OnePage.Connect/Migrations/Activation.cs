﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Activation
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public Guid? SubscriptionId { get; set; }

    public string? ActivationStatus { get; set; }

    public bool IsActive { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public DateTime? ActivatedDate { get; set; }

    public DateTime? DeActivatedDate { get; set; }
}
