﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Provider
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public Guid ProviderTypeId { get; set; }

    public string Name { get; set; } = null!;

    public string? InShort { get; set; }

    public string? Details { get; set; }

    public bool IsActive { get; set; }

    public int ShowSequence { get; set; }

    public string? IOsapp { get; set; }

    public string? AndroidApp { get; set; }

    public string? WindowsPhone { get; set; }

    public string? Windows10 { get; set; }

    public string? WebSite { get; set; }

    public string? Reference { get; set; }

    public bool IsProfessional { get; set; }

    public double? YearlyPrice { get; set; }

    public double? Price { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string? RequestMonthlyRange { get; set; }

    public string? RequestYearlyRange { get; set; }

    public bool IsAdminRequired { get; set; }

    public string? GoogleIds { get; set; }

    public string? AppleIds { get; set; }

    public bool IsReady { get; set; }

    public bool IsCustomizationRequired { get; set; }

    public int TrialDays { get; set; }

    public byte LogInType { get; set; }

    public byte AuthType { get; set; }

    public string? PageUrl { get; set; }

    public string? Prefix { get; set; }

    public string? AdvancedSearch { get; set; }

    public string? Domains { get; set; }

    public string? RelatedProviders { get; set; }

    public bool IsVisible { get; set; }

    public bool IsCustomLoginButton { get; set; }

    public bool IsLocal { get; set; }

    public virtual ICollection<AppProvider> AppProviders { get; set; } = new List<AppProvider>();

    public virtual ICollection<Identifier> Identifiers { get; set; } = new List<Identifier>();

    public virtual ICollection<Module> Modules { get; set; } = new List<Module>();

    public virtual ICollection<ProviderIdentifier> ProviderIdentifiers { get; set; } = new List<ProviderIdentifier>();

    public virtual ProviderType ProviderType { get; set; } = null!;

    public virtual ICollection<ProviderUrl> ProviderUrls { get; set; } = new List<ProviderUrl>();

    public virtual ICollection<RoleTemplate> RoleTemplates { get; set; } = new List<RoleTemplate>();
}
