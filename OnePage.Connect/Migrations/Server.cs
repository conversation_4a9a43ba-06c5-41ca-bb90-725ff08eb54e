﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Server
{
    /// <summary>
    /// 
    /// servertype    url
    /// 1                   authapi
    /// 2                   dataapi
    /// 3                   callapi
    /// 4                   syncapi
    /// 5                   fileapi
    /// 6                   catchallapi
    /// 7                   Provider Img Download Url
    /// 
    /// </summary>
    public Guid Id { get; set; }

    public int ServerType { get; set; }

    public string Url { get; set; } = null!;

    public int ShowSequence { get; set; }

    public bool IsActive { get; set; }

    public int Zone { get; set; }

    public int? PreviousZone { get; set; }

    public Guid AppId { get; set; }

    public string? Notes { get; set; }

    public string? OrgId { get; set; }

    public virtual App App { get; set; } = null!;
}
