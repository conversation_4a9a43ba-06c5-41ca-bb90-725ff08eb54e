﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class SocialType
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public string Icon { get; set; } = null!;

    public string SearchUrl { get; set; } = null!;

    public string LookUpUrl { get; set; } = null!;

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int ShowSequence { get; set; }

    public string? SearchFor { get; set; }
}
