﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ContactEmail
{
    public string? Email { get; set; }

    public bool? IsValidated { get; set; }

    public bool? IsAccessible { get; set; }

    public bool? IsPersonal { get; set; }

    public bool? IsMultiUser { get; set; }

    public bool? IsGroupEmail { get; set; }

    public int? Status { get; set; }

    public int? EmailType { get; set; }

    public bool? IsCurrentlyOwned { get; set; }

    public string? DisplayName { get; set; }

    public bool? IsAddedByUser { get; set; }

    public bool? IsAccByUser { get; set; }

    public Guid? AddedBy { get; set; }

    public Guid? ProviderId { get; set; }

    public Guid? UserProviderId { get; set; }

    public Guid? ContactId { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public Guid? UserId { get; set; }

    public bool? IsActive { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }
}
