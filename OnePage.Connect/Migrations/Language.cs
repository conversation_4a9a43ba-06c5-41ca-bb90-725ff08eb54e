﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Language
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int ShowSequence { get; set; }

    public string Locale { get; set; } = null!;

    public bool Ltr { get; set; }

    public string? FileName { get; set; }

    public virtual ICollection<Translate> Translates { get; set; } = new List<Translate>();
}
