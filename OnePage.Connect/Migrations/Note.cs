﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Note
{
    public string? Details { get; set; }

    public int? Sequence { get; set; }

    public DateTime? NoteDate { get; set; }

    public Guid? ContactId { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public Guid? UserId { get; set; }

    public bool? IsActive { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }
}
