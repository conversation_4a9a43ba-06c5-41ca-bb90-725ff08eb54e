﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class RelatedStory
{
    public Guid Id { get; set; }

    public Guid Story1Id { get; set; }

    public Guid Story2Id { get; set; }

    public Guid? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public string? ReportedDate { get; set; }

    public virtual Story Story1 { get; set; } = null!;
}
