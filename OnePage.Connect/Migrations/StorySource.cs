﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class StorySource
{
    public Guid Id { get; set; }

    public Guid SourceId { get; set; }

    public string Name { get; set; } = null!;

    public string? Url { get; set; }

    public bool IsActive { get; set; }

    public Guid AddedBy { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public virtual ICollection<Story> Stories { get; set; } = new List<Story>();
}
