﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ContactSocial
{
    public Guid? SocialId { get; set; }

    public string? Name { get; set; }

    public string? Url { get; set; }

    public bool? Request { get; set; }

    public bool? Show { get; set; }

    public bool? AskMe { get; set; }

    public bool? CanRequest { get; set; }

    public int? ShowOrder { get; set; }

    public Guid? ContactId { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public Guid? UserId { get; set; }

    public bool? IsActive { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }
}
