﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ProviderType
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public string Name { get; set; } = null!;

    public string? Details { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int ShowSequence { get; set; }

    public int FreeCount { get; set; }

    public int? Priority { get; set; }

    public virtual ICollection<Provider> Providers { get; set; } = new List<Provider>();
}
