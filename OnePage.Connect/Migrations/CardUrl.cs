﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class CardUrl
{
    public Guid Id { get; set; }

    public Guid CardId { get; set; }

    public Guid UrlId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public int? ShowSequence { get; set; }

    public bool? IsWeb { get; set; }

    public bool? IsMobile { get; set; }

    public virtual Card Card { get; set; } = null!;

    public virtual Url Url { get; set; } = null!;
}
