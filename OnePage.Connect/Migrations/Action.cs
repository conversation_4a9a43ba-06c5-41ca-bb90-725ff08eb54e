﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Action
{
    public Guid Id { get; set; }

    public string? ProviderTypeId { get; set; }

    public string? ProviderId { get; set; }

    public Guid? UserProviderId { get; set; }

    public Guid? OrgId { get; set; }

    public string? UserId { get; set; }

    public string? Note { get; set; }

    public string Title { get; set; } = null!;

    public string? Icon { get; set; }

    public bool IsAggregate { get; set; }

    public bool IsOrgSpecific { get; set; }

    public int ShowSequence { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public string? Command { get; set; }

    public bool? IsCardOnly { get; set; }

    public Guid? OrgRoleId { get; set; }

    public bool? IsPostCallAction { get; set; }

    public string? CommandParameter { get; set; }

    public Guid? ModuleId { get; set; }

    public bool IsVisible { get; set; }

    public Guid? FollowupUrl { get; set; }

    public byte Type { get; set; }

    public string? VisibilityConditions { get; set; }

    public bool IsDuringCall { get; set; }

    public virtual ICollection<ActionUrl> ActionUrls { get; set; } = new List<ActionUrl>();
}
