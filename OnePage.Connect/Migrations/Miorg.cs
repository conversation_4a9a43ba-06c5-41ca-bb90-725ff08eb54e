﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Miorg
{
    public Guid Id { get; set; }

    public Guid Mnature { get; set; }

    public string? Name { get; set; }

    public string? Domain { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public int Users { get; set; }

    public byte? MailServer { get; set; }

    public string? Note { get; set; }

    public string? Type { get; set; }

    public string? Model { get; set; }

    public string? Roles { get; set; }

    public string? Credits { get; set; }

    public string? Industries { get; set; }

    public string? CountryCodeWhereIamIn { get; set; }

    public virtual ICollection<Micontact> Micontacts { get; set; } = new List<Micontact>();

    public virtual Minature MnatureNavigation { get; set; } = null!;

    public virtual ICollection<MorgOption> MorgOptions { get; set; } = new List<MorgOption>();
}
