﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UniversityDomain
{
    public Guid Id { get; set; }

    public Guid UniversityId { get; set; }

    public Guid DomainId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public Guid? CreatedBy { get; set; }

    public Guid? ModifiedBy { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsVerified { get; set; }

    public virtual Domain Domain { get; set; } = null!;

    public virtual University University { get; set; } = null!;
}
