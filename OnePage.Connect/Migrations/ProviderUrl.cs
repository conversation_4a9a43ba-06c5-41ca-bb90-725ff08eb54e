﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ProviderUrl
{
    public Guid Id { get; set; }

    public Guid ProviderId { get; set; }

    public Guid UrlId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public virtual Provider Provider { get; set; } = null!;

    public virtual Url Url { get; set; } = null!;
}
