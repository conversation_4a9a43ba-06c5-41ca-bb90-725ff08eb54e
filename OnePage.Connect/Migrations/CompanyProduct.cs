﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class CompanyProduct
{
    public Guid Id { get; set; }

    public Guid CompanyId { get; set; }

    public Guid ProductId { get; set; }

    public bool IsActive { get; set; }

    public bool IsAcquired { get; set; }

    public Guid? AcquiredCompanyId { get; set; }

    public virtual Company Company { get; set; } = null!;

    public virtual Product Product { get; set; } = null!;
}
