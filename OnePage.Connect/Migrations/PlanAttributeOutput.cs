﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PlanAttributeOutput
{
    public int RowNumber { get; set; }

    public int PlanAttributeId { get; set; }

    public Guid PlanId { get; set; }

    public int OfferAttributeId { get; set; }

    public string DisplayName { get; set; } = null!;

    public bool IsEnabled { get; set; }

    public string? Type { get; set; }
}
