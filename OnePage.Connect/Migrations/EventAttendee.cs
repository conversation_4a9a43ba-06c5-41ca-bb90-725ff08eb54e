﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class EventAttendee
{
    public string? EventId { get; set; }

    public string? Name { get; set; }

    public string? Address { get; set; }

    public bool? IsRequired { get; set; }

    public bool? IsOrganizer { get; set; }

    public string? Status { get; set; }

    public string? Comment { get; set; }

    public bool? IsUser { get; set; }

    public Guid? ContactId { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public Guid? UserId { get; set; }

    public bool? IsActive { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }
}
