﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class OrgProvider
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public Guid ProviderId { get; set; }

    public string? ClientId { get; set; }

    public string? Secret { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int ShowSequence { get; set; }

    public string? Imapserver { get; set; }

    public string? Imapport { get; set; }

    public int? IsSsl { get; set; }

    public string? Crmlead { get; set; }

    public string? Crmcontact { get; set; }

    public bool CanSaveForOffline { get; set; }

    public bool ForceCallLog { get; set; }

    public bool? IsCustomized { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<OrgIdentifier> OrgIdentifiers { get; set; } = new List<OrgIdentifier>();

    public virtual ICollection<OrgProviderTigger> OrgProviderTiggers { get; set; } = new List<OrgProviderTigger>();
}
