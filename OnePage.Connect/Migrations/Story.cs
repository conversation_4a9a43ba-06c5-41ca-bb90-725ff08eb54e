﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Story
{
    public Guid Id { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public string? ReportedDate { get; set; }

    public string? ReportedBy { get; set; }

    public Guid? CreatedBy { get; set; }

    public Guid? StorySource { get; set; }

    public string? Title { get; set; }

    public string? Description { get; set; }

    public string? Summary { get; set; }

    public string? Pros { get; set; }

    public string? Cons { get; set; }

    public string? Author { get; set; }

    public virtual ICollection<CompanyStory> CompanyStories { get; set; } = new List<CompanyStory>();

    public virtual ICollection<PersonStory> PersonStories { get; set; } = new List<PersonStory>();

    public virtual ICollection<RelatedStory> RelatedStories { get; set; } = new List<RelatedStory>();

    public virtual StorySource? StorySourceNavigation { get; set; }

    public virtual ICollection<StoryTag> StoryTags { get; set; } = new List<StoryTag>();
}
