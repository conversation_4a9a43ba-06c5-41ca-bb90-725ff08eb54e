﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UniversitySocial
{
    public Guid Id { get; set; }

    public Guid UniversityId { get; set; }

    public Guid SocialId { get; set; }

    public Guid SourceId { get; set; }

    public string? Url { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public Guid? CreatedBy { get; set; }

    public Guid? ModifiedBy { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsValid { get; set; }

    public string? RefId { get; set; }

    public string? Username { get; set; }

    public string? UploadedData { get; set; }

    public virtual Social Social { get; set; } = null!;

    public virtual Source Source { get; set; } = null!;

    public virtual University University { get; set; } = null!;
}
