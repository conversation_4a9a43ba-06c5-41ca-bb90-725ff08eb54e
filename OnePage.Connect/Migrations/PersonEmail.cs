﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PersonEmail
{
    public Guid Id { get; set; }

    public Guid PersonId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public Guid? CreatedBy { get; set; }

    public Guid? ModifiedBy { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsActive { get; set; }

    public bool IsVerified { get; set; }

    public bool? IsGuessed { get; set; }

    public int? EmailType { get; set; }

    public string? Email { get; set; }

    public bool? IsPastEmail { get; set; }

    public bool? IsPreferred { get; set; }

    public bool IsMarkedIncorrect { get; set; }

    public virtual Person Person { get; set; } = null!;
}
