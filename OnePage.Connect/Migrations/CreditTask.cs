﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class CreditTask
{
    public Guid Id { get; set; }

    public string? Name { get; set; }

    public bool IsOneTime { get; set; }

    public bool IsActive { get; set; }

    public int? Credits { get; set; }

    public DateTime ModifiedDate { get; set; }

    public DateTime? CreatedDate { get; set; }

    public int ShowSequence { get; set; }

    public virtual ICollection<UserTask> UserTasks { get; set; } = new List<UserTask>();
}
