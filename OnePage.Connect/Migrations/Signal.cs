﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Signal
{
    public Guid Id { get; set; }

    public Guid SignalTypeId { get; set; }

    public Guid? UserId { get; set; }

    public bool? IsActive { get; set; }

    public DateTime TriggerOn { get; set; }

    public bool IsComplete { get; set; }

    public bool? IsProcessing { get; set; }

    public bool IsSeen { get; set; }

    public int Iteration { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public string? AdditionalData { get; set; }

    public virtual SignalType SignalType { get; set; } = null!;
}
