﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Card
{
    public Guid Id { get; set; }

    public string? ProviderTypeId { get; set; }

    public string? ProviderId { get; set; }

    public Guid? UserProviderId { get; set; }

    public string AppId { get; set; } = null!;

    public Guid? OrgId { get; set; }

    public string? UserId { get; set; }

    public Guid? OrgRoleId { get; set; }

    public string Title { get; set; } = null!;

    public string? Note { get; set; }

    public bool ShowCounter { get; set; }

    public string? Icon { get; set; }

    public bool ShowMore { get; set; }

    public bool IsAggregate { get; set; }

    public bool IsOrgSpecific { get; set; }

    public int? ChartType { get; set; }

    public bool HasActions { get; set; }

    public bool CanExpand { get; set; }

    public int ShowSequence { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public bool? ShowHeader { get; set; }

    public bool? ShowFooter { get; set; }

    public string? ActionName { get; set; }

    public string? ActionCommand { get; set; }

    public bool? IsGlobal { get; set; }

    public string? Description { get; set; }

    public string? Actions { get; set; }

    public byte CardType { get; set; }

    public int MaxHeight { get; set; }

    public bool IsHome { get; set; }

    public string? DataPath { get; set; }

    public string? DetailPath { get; set; }

    public string? CountPath { get; set; }

    public string? FieldsToShow { get; set; }

    public bool? IsList { get; set; }

    public Guid? ModuleId { get; set; }

    public string? Condition { get; set; }

    public string? Entity { get; set; }

    public string? Model { get; set; }

    public bool IsExtUrl { get; set; }

    public bool IsRepetitive { get; set; }

    public bool IsDemo { get; set; }

    public bool ShouldShowDetails { get; set; }

    public string? HelpUrl { get; set; }

    public int PageType { get; set; }

    public string? ListToBind { get; set; }

    public string? ModelToBind { get; set; }
    
    public bool IsCompany { get; set; }

    public virtual ICollection<CardUrl> CardUrls { get; set; } = new List<CardUrl>();

    public virtual ICollection<FieldsToDisplay> FieldsToDisplays { get; set; } = new List<FieldsToDisplay>();
}
