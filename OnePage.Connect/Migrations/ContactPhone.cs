﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ContactPhone
{
    public bool? IsPrimary { get; set; }

    public string? Pnumber { get; set; }

    public string? Snumber { get; set; }

    public bool? IsValidated { get; set; }

    public bool? IsAccessible { get; set; }

    public bool? IsPersonal { get; set; }

    public bool? IsMulti { get; set; }

    public string? Extn { get; set; }

    public int? PhoneType { get; set; }

    public bool? IsCowned { get; set; }

    public bool? IsAddedByUser { get; set; }

    public bool? IsAccByUser { get; set; }

    public Guid? AddedBy { get; set; }

    public string? CountryId { get; set; }

    public int? Ptype { get; set; }

    public string? Token { get; set; }

    public Guid? ProviderId { get; set; }

    public Guid? UserProviderId { get; set; }

    public Guid? ContactId { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public Guid? UserId { get; set; }

    public bool? IsActive { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }
}
