﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Trigger
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public Guid? ActionId { get; set; }

    public string? ProviderTypeId { get; set; }

    public string? ProviderId { get; set; }

    public Guid? UserProviderId { get; set; }

    public Guid? OrgId { get; set; }

    public Guid? UserId { get; set; }

    public Guid? OrgRoleId { get; set; }

    public byte TriggerType { get; set; }

    public string? DataUrl { get; set; }

    public int ShowSequence { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public bool ShouldTriggerCard { get; set; }

    public Guid? TriggerCardId { get; set; }

    public Guid? ModuleId { get; set; }

    public Guid? FollowUpAction { get; set; }
}
