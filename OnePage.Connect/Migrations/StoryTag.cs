﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class StoryTag
{
    public Guid Id { get; set; }

    public Guid StoryId { get; set; }

    public Guid TagId { get; set; }

    public Guid? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public virtual Story Story { get; set; } = null!;

    public virtual Tag Tag { get; set; } = null!;
}
