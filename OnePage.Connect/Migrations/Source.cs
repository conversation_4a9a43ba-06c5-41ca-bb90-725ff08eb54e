﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Source
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public virtual ICollection<CompanySocial> CompanySocials { get; set; } = new List<CompanySocial>();

    public virtual ICollection<PersonSocial> PersonSocials { get; set; } = new List<PersonSocial>();

    public virtual ICollection<UniversitySocial> UniversitySocials { get; set; } = new List<UniversitySocial>();
}
