﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class MeteredAuditLog
{
    public int Id { get; set; }

    public int? SubscriptionId { get; set; }

    public string? RequestJson { get; set; }

    public string? ResponseJson { get; set; }

    public string? StatusCode { get; set; }

    public DateTime? CreatedDate { get; set; }

    public int CreatedBy { get; set; }

    public DateTime? SubscriptionUsageDate { get; set; }

    public virtual Subscription? Subscription { get; set; }
}
