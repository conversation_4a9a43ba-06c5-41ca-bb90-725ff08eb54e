﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class SignalType
{
    public Guid Id { get; set; }

    public string Type { get; set; } = null!;

    public string Details { get; set; } = null!;

    public string? Condition { get; set; }

    public string MinutesLater { get; set; } = null!;

    public int? Iteration { get; set; }

    public Guid? PastSignal { get; set; }

    public int? Percentage { get; set; }

    public int? Sequence { get; set; }

    public int? SignalMode { get; set; }

    public Guid? NextSignal { get; set; }

    public string? MailModoCampaignId { get; set; }

    public string? SignalRroomId { get; set; }

    public string? OneSignalMessage { get; set; }

    public string? Smsmessage { get; set; }

    public string? LocalNotificationMessage { get; set; }

    public string? ExitCondition { get; set; }

    public string? UpdateSetting { get; set; }

    public virtual ICollection<Signal> Signals { get; set; } = new List<Signal>();
}
