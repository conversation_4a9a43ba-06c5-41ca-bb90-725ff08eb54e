﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UniversityTag
{
    public Guid Id { get; set; }

    public Guid UniversityTag1 { get; set; }

    public Guid TagId { get; set; }

    public Guid? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public virtual Tag Tag { get; set; } = null!;

    public virtual University UniversityTag1Navigation { get; set; } = null!;
}
