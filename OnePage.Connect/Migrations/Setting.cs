﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Setting
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public string? DefaultValue { get; set; }

    public byte DataType { get; set; }

    public bool IsAdmin { get; set; }

    public virtual ICollection<Discover> Discovers { get; set; } = new List<Discover>();

    public virtual ICollection<OfferSetting> OfferSettings { get; set; } = new List<OfferSetting>();

    public virtual ICollection<UserSetting> UserSettings { get; set; } = new List<UserSetting>();
}
