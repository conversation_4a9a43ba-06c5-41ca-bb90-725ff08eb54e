﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class OrgIdP
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public Guid IdPtypeId { get; set; }

    public string? Name { get; set; }

    public string? Saml20endPoint { get; set; }

    public string? IdentityProviderIssuer { get; set; }

    public string? SingleSignOnServiceUrl { get; set; }

    public string? SingleLogoutServiceUrl { get; set; }

    public string? Certificate { get; set; }

    public string? CertificatePath { get; set; }

    public string? EmailDomain { get; set; }

    public virtual IdPtype IdPtype { get; set; } = null!;

    public virtual Org Org { get; set; } = null!;
}
