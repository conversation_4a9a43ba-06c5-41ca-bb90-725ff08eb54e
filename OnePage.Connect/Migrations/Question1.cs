﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Question1
{
    public Guid Id { get; set; }

    public string? Question { get; set; }

    public string? AnswerOptions { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int? ShowSequence { get; set; }

    public virtual ICollection<Answer> Answers { get; set; } = new List<Answer>();
}
