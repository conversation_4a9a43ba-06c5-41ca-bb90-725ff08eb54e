﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class OrgProviderTigger
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public Guid OrgProviderId { get; set; }

    public Guid TriggerId { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual OrgProvider OrgProvider { get; set; } = null!;
}
