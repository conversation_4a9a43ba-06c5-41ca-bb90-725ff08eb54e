﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class FieldsToDisplay
{
    public Guid Id { get; set; }

    public Guid CardId { get; set; }

    public string? OrgId { get; set; }

    public string? OrgRoleId { get; set; }

    public string? UserId { get; set; }

    public string DisplayName { get; set; } = null!;

    public string? Icon { get; set; }

    public bool IsWearable { get; set; }

    public bool IsMobile { get; set; }

    public bool IsWebApp { get; set; }

    public string? NameMatch { get; set; }

    public string ValueMatch { get; set; } = null!;

    public byte Type { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public int ShowSequence { get; set; }

    public bool IsLink { get; set; }

    public string? Condition { get; set; }

    public string? ModelAttribute { get; set; }

    public string? Entity { get; set; }

    public bool? IsDirect { get; set; }

    public bool IsDownload { get; set; }

    public bool IsTitle { get; set; }

    public bool IsSub { get; set; }

    public bool IsDesc { get; set; }

    public bool IsCreated { get; set; }

    public bool IsUpdated { get; set; }

    public bool? IsPhone { get; set; }

    public string? ExtLink { get; set; }

    public string? IntLink { get; set; }

    public bool IsHidden { get; set; }

    public virtual Card Card { get; set; } = null!;
}
