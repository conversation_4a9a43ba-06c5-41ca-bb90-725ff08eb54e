﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Social
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public int ShowSequence { get; set; }

    public virtual ICollection<CompanySocial> CompanySocials { get; set; } = new List<CompanySocial>();

    public virtual ICollection<PersonSocial> PersonSocials { get; set; } = new List<PersonSocial>();

    public virtual ICollection<UniversitySocial> UniversitySocials { get; set; } = new List<UniversitySocial>();
}
