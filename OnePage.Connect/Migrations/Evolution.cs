﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Evolution
{
    public int Id { get; set; }

    public string Title { get; set; } = null!;

    public string? TitleDown { get; set; }

    public string? Description { get; set; }

    public int? Batch { get; set; }

    public string? Checksum { get; set; }

    public int? Status { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }
}
