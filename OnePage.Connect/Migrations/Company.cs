﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Company
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Aka { get; set; }

    public string? Founded { get; set; }

    public string? Employees { get; set; }

    public string? Industry { get; set; }

    public string? Country { get; set; }

    public string? Locality { get; set; }

    public string? Address { get; set; }

    public string? Description { get; set; }

    public bool IsDataKnown { get; set; }

    public string? Ticker { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsVerified { get; set; }

    public string? RefId { get; set; }

    public virtual ICollection<CompanyDomain> CompanyDomains { get; set; } = new List<CompanyDomain>();

    public virtual ICollection<CompanyGic> CompanyGics { get; set; } = new List<CompanyGic>();

    public virtual ICollection<CompanyLocation> CompanyLocations { get; set; } = new List<CompanyLocation>();

    public virtual ICollection<CompanyPhone> CompanyPhones { get; set; } = new List<CompanyPhone>();

    public virtual ICollection<CompanyProduct> CompanyProducts { get; set; } = new List<CompanyProduct>();

    public virtual ICollection<CompanySocial> CompanySocials { get; set; } = new List<CompanySocial>();

    public virtual ICollection<CompanyStory> CompanyStories { get; set; } = new List<CompanyStory>();

    public virtual ICollection<CompanyTag> CompanyTags { get; set; } = new List<CompanyTag>();

    public virtual ICollection<PersonCompany> PersonCompanies { get; set; } = new List<PersonCompany>();

    public virtual ICollection<Stack> Stacks { get; set; } = new List<Stack>();
}
