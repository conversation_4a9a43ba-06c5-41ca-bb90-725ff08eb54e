﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Country
{
    public int Id { get; set; }

    public string Name { get; set; } = null!;

    public string? Alpha2 { get; set; }

    public string? Alpha3 { get; set; }

    public string? Prefix { get; set; }

    public string? IntlPrefix { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string? Continent { get; set; }

    public byte ServerZone { get; set; }
}
