﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Urltype
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public int ShowOrder { get; set; }

    public virtual ICollection<Url> Urls { get; set; } = new List<Url>();
}
