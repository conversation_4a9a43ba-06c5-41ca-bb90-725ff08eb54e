﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Location
{
    public Guid Id { get; set; }

    public string? Name { get; set; }

    public string? Locality { get; set; }

    public string? Metro { get; set; }

    public string? Region { get; set; }

    public string? Country { get; set; }

    public string? Continent { get; set; }

    public string? StreetAddress { get; set; }

    public string? AddressLine2 { get; set; }

    public string? PostalCode { get; set; }

    public DateTime? LastUpdated { get; set; }

    public virtual ICollection<CompanyLocation> CompanyLocations { get; set; } = new List<CompanyLocation>();

    public virtual ICollection<PersonLocation> PersonLocations { get; set; } = new List<PersonLocation>();

    public virtual ICollection<UniversityLocation> UniversityLocations { get; set; } = new List<UniversityLocation>();
}
