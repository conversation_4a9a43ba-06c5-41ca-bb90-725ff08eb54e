﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ActionUrl
{
    public Guid Id { get; set; }

    public Guid ActionId { get; set; }

    public Guid UrlId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public virtual Action Action { get; set; } = null!;

    public virtual Url Url { get; set; } = null!;
}
