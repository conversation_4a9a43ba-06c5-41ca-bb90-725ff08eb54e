﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class MorgCreditUsuage
{
    public Guid Id { get; set; }

    public Guid MorgId { get; set; }

    public Guid? OpportunityId { get; set; }

    public Guid UserId { get; set; }

    public string Credits { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public bool? IsCredit { get; set; }

    public bool? IsDeducted { get; set; }
}
