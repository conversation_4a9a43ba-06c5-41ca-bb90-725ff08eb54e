﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Translate
{
    public Guid Id { get; set; }

    public int ContextId { get; set; }

    public int LanguageId { get; set; }

    public string ReferenceId { get; set; } = null!;

    public string? Value { get; set; }

    public bool IsDefault { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public int ShowSequence { get; set; }

    public bool IsAutoTranslated { get; set; }

    public bool IsEdited { get; set; }

    public virtual Language Language { get; set; } = null!;
}
