﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Offer
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public string? ProviderType { get; set; }

    public string? Provider { get; set; }

    public bool? IsSingleUse { get; set; }

    public virtual ICollection<Coupon> Coupons { get; set; } = new List<Coupon>();

    public virtual ICollection<OfferSetting> OfferSettings { get; set; } = new List<OfferSetting>();
}
