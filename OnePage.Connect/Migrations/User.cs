﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class User
{
    public Guid Id { get; set; }

    public string? FirstName { get; set; }

    public string? MiddleName { get; set; }

    public string? LastName { get; set; }

    public string? Email { get; set; }

    public bool IsEmailValidated { get; set; }

    public string? Password { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public bool IsUser { get; set; }

    public string? InviteCode { get; set; }

    public bool IsDisabled { get; set; }

    public string? Notes { get; set; }

    public DateTime? FirstLogin { get; set; }

    public DateTime? PreviousLogin { get; set; }

    public DateTime? LastLogin { get; set; }

    public long IsReady { get; set; }

    public string? Alias { get; set; }

    public bool? IsSpam { get; set; }

    public int? SpamCount { get; set; }

    public DateTime? LastAuthenticated { get; set; }

    public string? CountryId { get; set; }

    public int? Ptype { get; set; }

    public bool? IsTestUser { get; set; }

    public string? Company { get; set; }

    public int? LanguageId { get; set; }

    public Guid AppId { get; set; }

    public string? ChargebeeCustomerId { get; set; }

    public Guid AnalyticsId { get; set; }

    public string? SubscriptionDetails { get; set; }

    public string? CardId { get; set; }

    public string? CurrentTimeZone { get; set; }

    public string? InvitedBy { get; set; }

    public byte UserType { get; set; }

    public Guid? AdminUserId { get; set; }

    public bool? IsInvestor { get; set; }

    public bool? IsFounder { get; set; }

    public virtual ICollection<Answer> Answers { get; set; } = new List<Answer>();
}
