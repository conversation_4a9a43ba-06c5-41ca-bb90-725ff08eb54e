﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Recommend
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public string DarkImage { get; set; } = null!;

    public string Link { get; set; } = null!;

    public bool IsActive { get; set; }

    public string LightImage { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public bool IsAppPage { get; set; }

    public double ImageHeight { get; set; }

    public double ImageWidth { get; set; }

    public Guid? SettingId { get; set; }

    public string? SettingValue { get; set; }
}
