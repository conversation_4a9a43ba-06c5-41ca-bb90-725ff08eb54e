﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Discover
{
    public Guid Id { get; set; }

    public Guid SettingId { get; set; }

    public string? TriggerValue { get; set; }

    public string? Title { get; set; }

    public string? Detail { get; set; }

    public string? Video { get; set; }

    public string? Action1 { get; set; }

    public string? Action2 { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual Setting Setting { get; set; } = null!;
}
