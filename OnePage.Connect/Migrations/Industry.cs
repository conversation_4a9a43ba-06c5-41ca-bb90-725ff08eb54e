﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Industry
{
    public string Id { get; set; } = null!;

    public string IndustryGroupId { get; set; } = null!;

    public string Text { get; set; } = null!;

    public virtual IndustryGroup IndustryGroup { get; set; } = null!;

    public virtual ICollection<SubIndustry> SubIndustries { get; set; } = new List<SubIndustry>();
}
