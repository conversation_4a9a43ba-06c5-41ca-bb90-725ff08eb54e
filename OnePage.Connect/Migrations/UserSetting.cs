﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserSetting
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public Guid SettingId { get; set; }

    public byte DataType { get; set; }

    public bool IsActive { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string? Value { get; set; }

    public bool IsUserUpdated { get; set; }

    public bool IsAdminUpdated { get; set; }

    public virtual Setting Setting { get; set; } = null!;
}
