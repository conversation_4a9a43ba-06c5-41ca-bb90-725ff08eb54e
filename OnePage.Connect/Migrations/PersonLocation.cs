﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PersonLocation
{
    public Guid Id { get; set; }

    public Guid PersonId { get; set; }

    public Guid LocationId { get; set; }

    public Guid? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public bool IsMarkedIncorrect { get; set; }

    public virtual Location Location { get; set; } = null!;

    public virtual Person Person { get; set; } = null!;
}
