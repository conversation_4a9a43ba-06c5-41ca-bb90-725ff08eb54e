﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class OfferSetting
{
    public Guid Id { get; set; }

    public Guid OfferId { get; set; }

    public Guid SettingId { get; set; }

    public byte DataType { get; set; }

    public bool IsActive { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string? Value { get; set; }

    public virtual Offer Offer { get; set; } = null!;

    public virtual Setting Setting { get; set; } = null!;
}
