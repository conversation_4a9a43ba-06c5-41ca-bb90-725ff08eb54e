﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class OrgUser
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public Guid UserId { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public DateTime? ActivatedOn { get; set; }

    public DateTime? DeactivatedOn { get; set; }

    public bool IsActivated { get; set; }

    public bool? IsAdmin { get; set; }

    public string? Designation { get; set; }

    public string? Roles { get; set; }

    public virtual Org Org { get; set; } = null!;
}
