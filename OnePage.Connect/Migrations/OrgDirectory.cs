﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class OrgDirectory
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public string? UserId { get; set; }

    public string? Salutation { get; set; }

    public string? FirstName { get; set; }

    public string? MiddleName { get; set; }

    public string? LastName { get; set; }

    public string Email { get; set; } = null!;

    public string SanitizedNumber { get; set; } = null!;

    public string ProvidedNumber { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public string? Designation { get; set; }

    public string? CountryId { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<Provision> Provisions { get; set; } = new List<Provision>();
}
