﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PersonTag
{
    public Guid Id { get; set; }

    public Guid PersonId { get; set; }

    public Guid TagId { get; set; }

    public Guid? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public bool IsMarkedIncorrect { get; set; }

    public virtual Person Person { get; set; } = null!;

    public virtual Tag Tag { get; set; } = null!;
}
