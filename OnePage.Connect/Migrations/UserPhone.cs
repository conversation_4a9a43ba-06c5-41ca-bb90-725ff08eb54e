﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserPhone
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public bool IsPrimary { get; set; }

    public string? ProvidedNumber { get; set; }

    public string? SanitizedNumber { get; set; }

    public bool IsPhoneValidated { get; set; }

    public bool IsAccessible { get; set; }

    public bool IsPersonal { get; set; }

    public bool IsMultiUser { get; set; }

    public string? Extn { get; set; }

    public int PhoneType { get; set; }

    public bool IsPreviouslyOwned { get; set; }

    public bool IsAddedByUser { get; set; }

    public bool IsAcceptedByUser { get; set; }

    public string? AddedBy { get; set; }

    public int? Type { get; set; }

    public string? CountryId { get; set; }

    public int? Ptype { get; set; }
}
