﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Micontact
{
    public Guid Id { get; set; }

    public Guid MorgId { get; set; }

    public string? Name { get; set; }

    public string? Email { get; set; }

    public string? Phone { get; set; }

    public string? Role { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public bool IsDp { get; set; }

    public string? Discount { get; set; }

    public Guid? InviteId { get; set; }

    public DateTime? ExpiryTime { get; set; }

    public string? ProfilePic { get; set; }

    public string? LinkedIn { get; set; }

    public bool? IsSigned { get; set; }

    public bool? IsUpload { get; set; }

    public bool? IsOnboardingMailSent { get; set; }

    public bool? IsDpmailSent { get; set; }

    public bool? IsRequested { get; set; }

    public bool IsRequestedMailSent { get; set; }

    public virtual Miorg Morg { get; set; } = null!;
}
