﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Plan
{
    public int Id { get; set; }

    public string? PlanId { get; set; }

    public string? Description { get; set; }

    public string? DisplayName { get; set; }

    public bool? IsmeteringSupported { get; set; }

    public bool? IsPerUser { get; set; }

    public Guid PlanGuid { get; set; }

    public Guid OfferId { get; set; }

    public virtual ICollection<MeteredDimension> MeteredDimensions { get; set; } = new List<MeteredDimension>();
}
