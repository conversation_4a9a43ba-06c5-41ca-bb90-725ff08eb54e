﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class FeatureTrigger
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public string? BeforeValue { get; set; }

    public string? AfterValue { get; set; }

    public Guid? HelpId { get; set; }

    public string? Action { get; set; }

    public bool IsActive { get; set; }

    public bool IsAction { get; set; }

    public bool IsPage { get; set; }

    public string? PageName { get; set; }

    public bool IsFeature { get; set; }

    public string? Message { get; set; }

    public string? Action2 { get; set; }
}
