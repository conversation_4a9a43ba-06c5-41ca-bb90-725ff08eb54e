﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Coupon
{
    public Guid Id { get; set; }

    public Guid? OfferId { get; set; }

    public string EventName { get; set; } = null!;

    public int ShowSequence { get; set; }

    public bool IsActive { get; set; }

    public bool IsRedeemed { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public Guid? UserId { get; set; }

    public bool IsClaimed { get; set; }

    public int Count { get; set; }

    public bool IsApproved { get; set; }

    public bool IsCancelled { get; set; }

    public Guid? FromUserId { get; set; }

    public virtual Offer? Offer { get; set; }
}
