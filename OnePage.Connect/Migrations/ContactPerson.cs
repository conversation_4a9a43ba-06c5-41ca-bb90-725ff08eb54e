﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ContactPerson
{
    public Guid Id { get; set; }

    public Guid? ContactId { get; set; }

    public Guid? PersonId { get; set; }

    public DateTime? CreatedDate { get; set; }

    public DateTime? UpdatedDate { get; set; }

    public string? Email { get; set; }

    public string? Phone { get; set; }

    public bool? IsMatchFound { get; set; }

    public bool? IsPdlcalled { get; set; }

    public DateTime? NeededBy { get; set; }

    public bool IsVerified { get; set; }

    public Guid? VerifiedBy { get; set; }

    public string? FirstName { get; set; }

    public string? LastName { get; set; }

    public string? CompanyName { get; set; }

    public string? Designation { get; set; }

    public string? AddedBy { get; set; }

    public DateTime? AddedDateTime { get; set; }

    public string? VerificationComments { get; set; }

    public DateTime? VerificationDateTime { get; set; }

    public string? SecondaryVerificationBy { get; set; }

    public DateTime? SecondaryVerificationDateTime { get; set; }

    public string? SecondaryVerificationComments { get; set; }

    public string? LinkedIn { get; set; }

    public string? Twitter { get; set; }

    public bool IsUser { get; set; }

    public bool IsRedeemed { get; set; }

    public bool IsRequested { get; set; }

    public bool IsSearchComplete { get; set; }

    public Guid? ReportedUserId { get; set; }

    public string? Suggestion { get; set; }

    public Guid? PastPersonMatch { get; set; }

    public virtual Person? Person { get; set; }
}
