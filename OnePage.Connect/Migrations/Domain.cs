﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Domain
{
    public Guid Id { get; set; }

    public bool IsActive { get; set; }

    public int ShowSequence { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string? Address { get; set; }

    public Guid? CompanyId { get; set; }

    public bool? IsFree { get; set; }
    public ICollection<UniversityDomain> UniversityDomains { get; set; } // Added navigation property
}
