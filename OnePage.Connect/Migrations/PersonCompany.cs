﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PersonCompany
{
    public Guid Id { get; set; }

    public Guid PersonId { get; set; }

    public Guid CompanyId { get; set; }

    public string? Title { get; set; }

    public bool IsCurrent { get; set; }

    public string? Notes { get; set; }

    public string? StartDate { get; set; }

    public string? EndDate { get; set; }

    public string? Role { get; set; }

    public string? JobLevel { get; set; }

    public string? IsPrimary { get; set; }

    public string? Summary { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsVerified { get; set; }

    public int? ShowSequence { get; set; }

    public bool IsActive { get; set; }

    public bool IsMarkedIncorrect { get; set; }

    public virtual Company Company { get; set; } = null!;

    public virtual Person Person { get; set; } = null!;
}
