﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserEmail
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public string? Email { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public bool IsEmailValidated { get; set; }

    public bool IsAccessible { get; set; }

    public bool IsPersonal { get; set; }

    public bool IsMultiUser { get; set; }

    public bool IsGroupEmail { get; set; }

    public int Status { get; set; }

    public int EmailType { get; set; }

    public bool IsPreviouslyOwned { get; set; }

    public string? DisplayName { get; set; }

    public bool IsAddedByUser { get; set; }

    public bool IsAcceptedByUser { get; set; }

    public string? AddedBy { get; set; }
}
