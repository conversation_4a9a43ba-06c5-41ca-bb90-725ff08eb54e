﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Subscription
{
    public int Id { get; set; }

    public Guid AmpsubscriptionId { get; set; }

    public string? SubscriptionStatus { get; set; }

    public string? AmpplanId { get; set; }

    public bool? IsActive { get; set; }

    public int? CreateBy { get; set; }

    public DateTime? CreateDate { get; set; }

    public DateTime? ModifyDate { get; set; }

    public string? Name { get; set; }

    public int Ampquantity { get; set; }

    public string? PurchaserEmail { get; set; }

    public Guid? PurchaserTenantId { get; set; }

    public Guid? UserGuid { get; set; }

    public virtual ICollection<MeteredAuditLog> MeteredAuditLogs { get; set; } = new List<MeteredAuditLog>();

    public virtual ICollection<SubscriptionAuditLog> SubscriptionAuditLogs { get; set; } = new List<SubscriptionAuditLog>();
}
