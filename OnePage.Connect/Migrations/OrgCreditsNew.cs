﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace OnePage.Connect.Migrations
{
    [Table("OrgCreditsNew")]
    public partial class OrgCreditsNew
    {
        [Key]
        public Guid Id { get; set; }
        public Guid? OrgId { get; set; }
        public Guid? CreditTypeId { get; set; }
        [StringLength(50)]
        public string Credits { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastPurchaseDate { get; set; }
        public string InvoiceLink { get; set; }
        public Guid? CouponId { get; set; }
    }
}
