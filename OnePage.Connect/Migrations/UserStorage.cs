﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserStorage
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public string ContactId { get; set; } = null!;

    public string Name { get; set; } = null!;

    /// <summary>
    /// Type bit - 0 for folder, 1 for search
    /// </summary>
    public int Type { get; set; }

    public bool ShowForAll { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public int? ShowSequence { get; set; }
}
