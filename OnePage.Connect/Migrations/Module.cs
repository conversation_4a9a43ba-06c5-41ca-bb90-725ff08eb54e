﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Module
{
    public Guid Id { get; set; }

    public Guid ProviderId { get; set; }

    public string Name { get; set; } = null!;

    public string? ReferenceId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public virtual Provider Provider { get; set; } = null!;
}
