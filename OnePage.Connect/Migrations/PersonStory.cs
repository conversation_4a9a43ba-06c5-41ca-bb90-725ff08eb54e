﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PersonStory
{
    public Guid Id { get; set; }

    public Guid PersonId { get; set; }

    public Guid StoryId { get; set; }

    public Guid? CreatedBy { get; set; }

    public bool IsActive { get; set; }

    public virtual Person Person { get; set; } = null!;

    public virtual Story Story { get; set; } = null!;
}
