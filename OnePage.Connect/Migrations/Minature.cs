﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Minature
{
    public Guid Id { get; set; }

    public string? Name { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool IsActive { get; set; }

    public bool? IsLocal { get; set; }

    public bool? IsState { get; set; }

    public bool? IsCountry { get; set; }

    public byte? StartPercentage { get; set; }

    public byte? EndPercentage { get; set; }

    public bool? IsGlobal { get; set; }

    public string? AppLink { get; set; }

    public virtual ICollection<Miorg> Miorgs { get; set; } = new List<Miorg>();

    public virtual ICollection<MorgOption> MorgOptions { get; set; } = new List<MorgOption>();

    public virtual ICollection<Question> Questions { get; set; } = new List<Question>();
}
