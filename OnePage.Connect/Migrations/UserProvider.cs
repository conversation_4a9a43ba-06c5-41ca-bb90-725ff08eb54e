﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserProvider
{
    public Guid Id { get; set; }

    public Guid? OrgId { get; set; }

    public Guid UserId { get; set; }

    public Guid ProviderId { get; set; }

    public Guid? AppId { get; set; }

    public string? Token { get; set; }

    public string? RefreshToken { get; set; }

    public string? Account { get; set; }

    public string? ConnectionDetails { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public DateTime ActiveFrom { get; set; }

    public DateTime? ActiveTill { get; set; }

    public string? Url { get; set; }

    public string? DataUrl { get; set; }

    public int Status { get; set; }

    public string? Code { get; set; }

    public bool IsAuthenticated { get; set; }

    public string? MailboxGuid { get; set; }

    public string? ReferenceId { get; set; }

    public string? EmailAddress { get; set; }

    public string? DisplayName { get; set; }

    public string? Tenant { get; set; }

    public Guid? ProvisionId { get; set; }

    public string? Password { get; set; }

    public string? Popserver { get; set; }

    public string? Imapserver { get; set; }

    public string? Smtpserver { get; set; }

    public bool IsProvisioned { get; set; }

    public string? ProfileUrl { get; set; }

    public bool IsPayed { get; set; }

    public string? WebhookId { get; set; }

    public DateTime? WebhookExpiration { get; set; }

    public string? IdpproviderUrl { get; set; }

    public string? IdpproviderInstance { get; set; }

    public string? Type { get; set; }

    public string? Port { get; set; }

    public string? ServerUrl { get; set; }

    public string? AccountId { get; set; }

    public bool? IsSsl { get; set; }

    public bool IsFree { get; set; }

    public string? CeuserId { get; set; }

    public string? CeorgId { get; set; }

    public string? CeinstanceId { get; set; }

    public string? CeinstanceUrl { get; set; }

    public string? BulkDataUrl { get; set; }

    public int? Source { get; set; }

    public string? ProductId { get; set; }

    public string? PurchaseId { get; set; }

    public bool? IsMonthly { get; set; }

    public int? PurchaseState { get; set; }

    public string? InstanceUrl { get; set; }

    public DateTime? TrialStart { get; set; }

    public DateTime? TrialEnd { get; set; }

    public bool? IsTrial { get; set; }

    public bool? IsClaimed { get; set; }

    public bool? IsTrialCompleted { get; set; }

    public bool? IsPaymentStoped { get; set; }

    public bool IsRelogginRequired { get; set; }

    public int? Priority { get; set; }

    public bool IsFullSyncDone { get; set; }

    public DateTime? LastFullSyncDate { get; set; }

    public DateTime? LastUpdateDate { get; set; }

    public DateTime? LastCheckedDate { get; set; }

    public virtual ICollection<UserProviderIdentifier> UserProviderIdentifiers { get; set; } = new List<UserProviderIdentifier>();
}
