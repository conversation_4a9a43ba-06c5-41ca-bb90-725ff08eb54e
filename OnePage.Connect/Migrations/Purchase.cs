﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Purchase
{
    public Guid Id { get; set; }

    public Guid OrgId { get; set; }

    public Guid ProviderId { get; set; }

    public string? IdpappId { get; set; }

    public string? Details { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public DateTime? PurchasedOn { get; set; }

    public DateTime? ValidTill { get; set; }

    public string? IdpproviderUrl { get; set; }

    public string? IdpsandboxUrl { get; set; }

    public string? Idpinstance { get; set; }

    public string? Idpkey { get; set; }

    public int? PurchaseCount { get; set; }

    public int? AssignedCount { get; set; }

    public int? RedeemedCount { get; set; }
}
