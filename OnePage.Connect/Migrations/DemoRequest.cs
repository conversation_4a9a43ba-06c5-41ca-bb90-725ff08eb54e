﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class DemoRequest
{
    public Guid Id { get; set; }

    public string? Salutation { get; set; }

    public string FirstName { get; set; } = null!;

    public string? MiddleName { get; set; }

    public string LastName { get; set; } = null!;

    public string Email { get; set; } = null!;

    public string PhoneNumber { get; set; } = null!;

    public string? CompanyName { get; set; }

    public DateTime Createddate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public bool? IsDemoCreated { get; set; }

    public bool IsActive { get; set; }

    public string? IdpproviderUrl { get; set; }

    public string? IdpsandboxUrl { get; set; }

    public bool IsApp { get; set; }

    public string? CountryId { get; set; }

    public int? Ptype { get; set; }

    public bool IsEmailValidated { get; set; }
}
