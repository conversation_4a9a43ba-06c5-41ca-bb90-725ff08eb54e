﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class ActivityCall
{
    public Guid? ActivityId { get; set; }

    public Guid? ProviderId { get; set; }

    public string? Url { get; set; }

    public bool? IsEdited { get; set; }

    public Guid? UserId { get; set; }

    public string? CallId { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public bool? IsActive { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }

    public bool? IsSynced { get; set; }

    public DateTime? LastSync { get; set; }
}
