﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class PersonDomain
{
    public Guid Id { get; set; }

    public Guid PersonId { get; set; }

    public Guid DomainId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public Guid? CreatedBy { get; set; }

    public Guid? ModifiedBy { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsVerified { get; set; }

    public bool IsWork { get; set; }

    public bool IsResearch { get; set; }

    public virtual Domain Domain { get; set; } = null!;

    public virtual Person Person { get; set; } = null!;
}
