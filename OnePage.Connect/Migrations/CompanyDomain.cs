﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class CompanyDomain
{
    public Guid Id { get; set; }

    public Guid CompanyId { get; set; }

    public Guid DomainId { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public Guid? CreatedBy { get; set; }

    public Guid? ModifiedBy { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsVerified { get; set; }

    public virtual Company Company { get; set; } = null!;

    public virtual Domain Domain { get; set; } = null!;
}
