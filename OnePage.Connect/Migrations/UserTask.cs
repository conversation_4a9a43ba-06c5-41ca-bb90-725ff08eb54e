﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserTask
{
    public Guid Id { get; set; }

    public Guid UserId { get; set; }

    public Guid TaskId { get; set; }

    public bool IsActive { get; set; }

    public string? VerificationInfo { get; set; }

    public bool IsUserSubmitted { get; set; }

    public bool IsVerfied { get; set; }

    public DateTime ModifiedDate { get; set; }

    public DateTime CreatedDate { get; set; }

    public virtual CreditTask Task { get; set; } = null!;
}
