﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class UserProviderIdentifier
{
    public Guid Id { get; set; }

    public Guid UserProviderId { get; set; }

    public Guid IdentifierId { get; set; }

    public string Value { get; set; } = null!;

    public DateTime CreatedDate { get; set; }

    public bool IsActive { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public virtual UserProvider UserProvider { get; set; } = null!;
}
