﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Option
{
    public Guid Id { get; set; }

    public Guid? QuestionId { get; set; }

    public string? OptionName { get; set; }

    public bool? IsDefault { get; set; }

    public bool? IsActive { get; set; }

    public virtual ICollection<MorgOption> MorgOptions { get; set; } = new List<MorgOption>();

    public virtual Question? Question { get; set; }
}
