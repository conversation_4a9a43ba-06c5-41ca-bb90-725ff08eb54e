﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class App
{
    public Guid Id { get; set; }

    public string Name { get; set; } = null!;

    public DateTime Createddate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string? ProxyServer { get; set; }

    public bool IsActive { get; set; }

    public string LanguagesToShow { get; set; } = null!;

    public bool ShowAds { get; set; }

    public bool ShowImportContacts { get; set; }

    public bool ShowSocialProviders { get; set; }

    public bool ShowNotifications { get; set; }

    public bool ShowRecentCalls { get; set; }

    public bool ShowUnknownCalls { get; set; }

    public bool ShowLanguage { get; set; }

    public bool ShowTheme { get; set; }

    public bool ShowPermissions { get; set; }

    public bool ShowPushNotifications { get; set; }

    public bool ShowRedeem { get; set; }

    public string AndroidOneSignalId { get; set; } = null!;

    public string AndroidOneSignalApiKey { get; set; } = null!;

    public string IosoneSignalId { get; set; } = null!;

    public string IosoneSignalApiKey { get; set; } = null!;

    public string WebOneSignalId { get; set; } = null!;

    public string WebOneSignalApiKey { get; set; } = null!;

    public string? AndroidProjectId { get; set; }

    public string? AndroidProjectKey { get; set; }

    public string PrivacyUrl { get; set; } = null!;

    public string Tocurl { get; set; } = null!;

    public string SupportPhone { get; set; } = null!;

    public string SupportEmail { get; set; } = null!;

    public string SupportName { get; set; } = null!;

    public bool IsOneDevicePerAccount { get; set; }

    public virtual ICollection<AppLanguage> AppLanguages { get; set; } = new List<AppLanguage>();

    public virtual ICollection<Server> Servers { get; set; } = new List<Server>();

    public virtual ICollection<Theme> Themes { get; set; } = new List<Theme>();
}
