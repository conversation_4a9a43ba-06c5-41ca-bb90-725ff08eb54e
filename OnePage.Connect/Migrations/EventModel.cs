﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class EventModel
{
    public string? EventId { get; set; }

    public DateTime? Start { get; set; }

    public DateTime? End { get; set; }

    public string? Lstart { get; set; }

    public string? Lend { get; set; }

    public string? Desc { get; set; }

    public string? Subject { get; set; }

    public string? ICallUid { get; set; }

    public string? Location { get; set; }

    public string? Url { get; set; }

    public string? Link { get; set; }

    public string? Importance { get; set; }

    public string? Status { get; set; }

    public bool? IsOrganizer { get; set; }

    public bool? IsAllDay { get; set; }

    public Guid? ProviderId { get; set; }

    public Guid? UserProviderId { get; set; }

    public bool? IsRepeat { get; set; }

    public string? RepeatId { get; set; }

    public bool? IsScheduled { get; set; }

    public string? Sipnumbers { get; set; }

    public string? Dinumbers { get; set; }

    public string? H323numbers { get; set; }

    public string? Otmobile { get; set; }

    public string? DialIn { get; set; }

    public string? HostPin { get; set; }

    public bool? IsOnline { get; set; }

    public bool? IsCancelled { get; set; }

    public bool? IsMrecorded { get; set; }

    public string? ConvUrl { get; set; }

    public string? RecUrl { get; set; }

    public string? Lcolor { get; set; }

    public string? Dcolor { get; set; }

    public string? CalId { get; set; }

    public string? Notes { get; set; }

    public Guid? Id { get; set; }

    public DateTime? Created { get; set; }

    public DateTime? Modified { get; set; }

    public Guid? UserId { get; set; }

    public bool? IsActive { get; set; }

    public bool? Bone { get; set; }

    public bool? Btwo { get; set; }

    public string? Sone { get; set; }

    public string? Stwo { get; set; }
}
