﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Org
{
    public Guid Id { get; set; }

    public Guid PlanId { get; set; }

    public string Name { get; set; } = null!;

    public string? Details { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime ModifiedDate { get; set; }

    public DateTime? PurchasedOn { get; set; }

    public DateTime? ValidTill { get; set; }

    public bool IsBeta { get; set; }

    public DateTime? BetaValidTill { get; set; }

    public int AllowedAccounts { get; set; }

    public bool? IsPaid { get; set; }

    public string? Countries { get; set; }

    public string? IdpproviderUrl { get; set; }

    public string? IdpsandboxUrl { get; set; }

    public string? Idpinstance { get; set; }

    public string? Idpkey { get; set; }

    public Guid? IdpproviderId { get; set; }

    public string? Domains { get; set; }

    public string? AppId { get; set; }

    public bool IsProvider { get; set; }

    public bool IsPurchaser { get; set; }

    public int ActionCount { get; set; }

    public int CardCount { get; set; }

    public int TriggerCount { get; set; }

    public bool IsRestricted { get; set; }

    public bool IsAdminGivenFree { get; set; }

    public int FreeAccounts { get; set; }

    public bool IsInternal { get; set; }

    public int? Credits { get; set; }

    public virtual ICollection<OrgDirectory> OrgDirectories { get; set; } = new List<OrgDirectory>();

    public virtual ICollection<OrgIdP> OrgIdPs { get; set; } = new List<OrgIdP>();

    public virtual ICollection<OrgProviderTigger> OrgProviderTiggers { get; set; } = new List<OrgProviderTigger>();

    public virtual ICollection<OrgProvider> OrgProviders { get; set; } = new List<OrgProvider>();

    public virtual ICollection<OrgRoleTemplate> OrgRoleTemplates { get; set; } = new List<OrgRoleTemplate>();

    public virtual ICollection<OrgRole> OrgRoles { get; set; } = new List<OrgRole>();

    public virtual ICollection<OrgUser> OrgUsers { get; set; } = new List<OrgUser>();

    public virtual ICollection<Provision> Provisions { get; set; } = new List<Provision>();
}
