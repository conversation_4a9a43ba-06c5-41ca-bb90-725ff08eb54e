﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class Person
{
    public Guid Id { get; set; }

    public string? Salutation { get; set; }

    public string FirstName { get; set; } = null!;

    public string? MiddleName { get; set; }

    public string LastName { get; set; } = null!;

    public string? FullName { get; set; }

    public string? Aka { get; set; }

    public DateTime CreatedDate { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public Guid? CreatedBy { get; set; }

    public Guid? ModifiedBy { get; set; }

    public Guid? VerifiedBy { get; set; }

    public bool IsDataKnown { get; set; }

    public string? Gender { get; set; }

    public bool IsVerified { get; set; }

    public string? RefId { get; set; }

    public string? BirthYear { get; set; }

    public string? BirthDate { get; set; }

    public string? Pdldata { get; set; }

    public string? Summary { get; set; }

    public string? UploadedData { get; set; }

    public virtual ICollection<ContactPerson> ContactPeople { get; set; } = new List<ContactPerson>();

    public virtual ICollection<PersonAward> PersonAwards { get; set; } = new List<PersonAward>();

    public virtual ICollection<PersonCompany> PersonCompanies { get; set; } = new List<PersonCompany>();

    public virtual ICollection<PersonDomain> PersonDomains { get; set; } = new List<PersonDomain>();

    public virtual ICollection<PersonEmail> PersonEmails { get; set; } = new List<PersonEmail>();

    public virtual ICollection<PersonLanguage> PersonLanguages { get; set; } = new List<PersonLanguage>();

    public virtual ICollection<PersonLocation> PersonLocations { get; set; } = new List<PersonLocation>();

    public virtual ICollection<PersonPhone> PersonPhones { get; set; } = new List<PersonPhone>();

    public virtual ICollection<PersonSocial> PersonSocials { get; set; } = new List<PersonSocial>();

    public virtual ICollection<PersonStory> PersonStories { get; set; } = new List<PersonStory>();

    public virtual ICollection<PersonTag> PersonTags { get; set; } = new List<PersonTag>();

    public virtual ICollection<PersonUniversity> PersonUniversities { get; set; } = new List<PersonUniversity>();
}
