﻿using System;
using System.Collections.Generic;

namespace OnePage.Connect.Migrations;

public partial class M
{
    public Guid Id { get; set; }

    public Guid Pid { get; set; }

    public DateTime D { get; set; }

    public byte? Tid { get; set; }

    public virtual ICollection<I> Is { get; set; } = new List<I>();

    public virtual P PidNavigation { get; set; } = null!;

    public virtual T? TidNavigation { get; set; }
}
