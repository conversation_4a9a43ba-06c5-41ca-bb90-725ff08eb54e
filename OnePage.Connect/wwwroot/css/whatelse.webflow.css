h1 {
    margin-top: 20px;
    margin-bottom: 16px;
    font-size: 40px;
    line-height: 55px;
    font-weight: 300;
    letter-spacing: .5px
}

h2 {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 35px;
    line-height: 50px;
    font-weight: 300;
    letter-spacing: .5px
}

h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 25px;
    line-height: 35px;
    font-weight: 300
}

h4 {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 20px;
    line-height: 25px;
    font-weight: 300
}

h5 {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 16px;
    line-height: 20px;
    font-weight: 300
}

h6 {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 20px;
    font-weight: 300;
    letter-spacing: .5px;
    text-transform: uppercase
}

p {
    margin-bottom: 20px;
    color: #676b75;
    font-size: 16px;
    line-height: 30px
}

a {
    color: #469af4;
    text-decoration: none
}

ul {
    margin-top: 0;
    margin-bottom: 10px;
    padding-left: 40px;
    text-align: left
}

ol {
    margin-top: 0;
    margin-bottom: 10px;
    padding-left: 40px;
    text-align: left
}

li {
    padding-top: 8px;
    padding-bottom: 8px;
    padding-left: 5px;
    font-size: 15px;
    line-height: 20px
}

img {
    display: inline-block;
    max-width: 100%
}

blockquote {
    margin-top: 30px;
    margin-bottom: 30px;
    padding: 10px 30px;
    border-left: 3px solid #ffc250;
    font-size: 20px;
    line-height: 35px;
    font-style: italic
}

figure {
    margin-top: 30px;
    margin-bottom: 30px
}

figcaption {
    margin-top: 5px;
    color: #999ea8;
    font-size: 14px;
    text-align: center
}

.hero-section {
    height: 720px;
    background-image: url(../images/bg-gradient-1.svg),-webkit-linear-gradient(315deg,#7956ec,#2fb9f8);
    background-image: url(../images/bg-gradient-1.svg),linear-gradient(135deg,#7956ec,#2fb9f8);
    background-position: 50% 0,0 0;
    background-size: cover,auto;
    background-repeat: no-repeat,repeat;
    background-attachment: fixed,scroll
}

    .hero-section.video {
        background-image: -webkit-linear-gradient(315deg,rgba(121,86,236,.9),rgba(47,185,248,.9));
        background-image: linear-gradient(135deg,rgba(121,86,236,.9),rgba(47,185,248,.9))
    }

    .hero-section.desktop {
        overflow: hidden;
        height: 820px;
        margin-bottom: -20px
    }

    .hero-section.web {
        margin-bottom: 540px
    }

    .hero-section.all-platforms {
        margin-bottom: 360px
    }

    .hero-section.phone {
        margin-bottom: 120px
    }

    .hero-section.tablet {
        margin-bottom: 120px
    }

.logo {
    position: absolute;
    left: 0;
    margin-top: -2px;
    padding-right: 20px;
    padding-left: 15px;
    float: left;
    color: #fff;
    font-size: 20px;
    font-weight: 700
}

.body {
    background-color: #f8f9fa;
    font-family: 'Nunito Sans',sans-serif;
    color: #282b31;
    font-size: 16px;
    line-height: 25px;
    font-weight: 400
}

.nav-section {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    z-index: 99;
    background-color: transparent;
    color: #fff
}

.nav-link {
    display: inline-block;
    padding: 8px 20px;
    -webkit-transition: opacity 200ms ease;
    transition: opacity 200ms ease;
    color: #fff;
    font-size: 15px;
    font-weight: 700;
    text-decoration: none
}

    .nav-link:hover {
        opacity: .8
    }

    .nav-link.w--current {
        color: #fff
    }

    .nav-link.dropdown {
        padding-right: 34px
    }

.wrapper {
    display: block;
    max-width: 1230px;
    margin-right: auto;
    margin-left: auto;
    padding-right: 15px;
    padding-left: 15px
}

    .wrapper.hero-2-cols {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
        justify-content: space-between
    }

    .wrapper.center {
        text-align: center
    }

    .wrapper.side-reviews {
        position: relative;
        z-index: 1;
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-pack: justify;
        -webkit-justify-content: space-between;
        -ms-flex-pack: justify;
        justify-content: space-between;
        -webkit-flex-wrap: nowrap;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap
    }

.button {
    display: none;
    height: 50px;
    padding-top: 11px;
    padding-right: 20px;
    padding-left: 20px;
    border-radius: 6px;
    background-color: #fff;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08);
    -webkit-transition: box-shadow 200ms ease;
    transition: box-shadow 200ms ease;
    color: #282b31;
    font-size: 17px;
    line-height: 30px;
    font-weight: 700;
    text-align: center;
    letter-spacing: 1px
}

    .button:hover {
        box-shadow: 0 12px 24px 0 rgba(40,43,49,.16)
    }

    .button.nav-panel-button {
        height: 40px;
        margin-bottom: 0;
        padding-top: 11px;
        padding-right: 15px;
        padding-left: 15px;
        float: right;
        font-size: 14px;
        line-height: 20px;
        text-transform: uppercase
    }

    .button.feature-video-button {
        margin-top: 60px;
        margin-bottom: -50px
    }

    .button.beta {
        position: fixed;
        top: 30px;
        right: 30px;
        height: 40px;
        padding-right: 15px;
        padding-left: 15px;
        font-size: 14px;
        line-height: 20px
    }

    .button.hero-second-button {
        margin-right: 10px;
        margin-left: 10px
    }

.nav-panel {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding: 25px 15px;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start
}

.nav-menu {
    position: relative;
    display: block;
    margin-right: 15px;
    float: right;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
    background-color: transparent;
    text-align: left
}

.grey-section {
    overflow: hidden;
    padding-top: 10px;
    padding-bottom: 10px;
    background-color: #f8f9fa
}

    .grey-section.second-phone-feature {
        margin-top: -70px;
        padding-top: 0
    }

    .grey-section.reviews {
        position: relative;
        padding-top: 0;
        padding-bottom: 0;
        box-shadow: 0 0 0 1px #e7e8ea
    }

    .grey-section.second-web-feature {
        padding-top: 65px;
        padding-bottom: 145px
    }

    .grey-section.first-web-feature {
        padding-top: 145px;
        padding-bottom: 70px
    }

.hero-heading {
    max-width: 570px;
    margin-bottom: 36px;
    color: #fff
}

.heading-accent {
    font-weight: 800
}

.hero-intro-left {
    padding-top: 70px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.app-icon {
    display: block;
    width: 90px;
    margin-bottom: 34px;
    border-radius: 18px;
    box-shadow: 0 3px 6px 0 rgba(40,43,49,.08)
}

    .app-icon.middle {
        display: none
    }

    .app-icon.coming-soon-icon-v1 {
        margin-bottom: 19px
    }

    .app-icon.coming-soon-icon-v2 {
        margin-top: -45px;
        margin-bottom: 20px
    }

.hero {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    padding-top: 140px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start
}

.section-header {
    display: block;
    max-width: 570px;
    margin: 14px auto 20px;
    text-align: center
}

.feature-big-icon {
    margin-bottom: 5px;
    border-radius: 15px;
    box-shadow: 0 6px 12px 0 rgba(0,0,0,.08);
    text-shadow: 0 -1px 0 hsla(0,0%,100%,.25)
}

.feature-text-small {
    margin-top: -3px;
    color: #676b75;
    font-size: 14px;
    line-height: 25px
}

.feature-text-small-justify {
    margin-top: -3px;
    color: #676b75;
    font-size: 14px;
    line-height: 25px;
    text-align: justify
}

.feature-big {
    padding: 26px 30px 70px;
    -webkit-flex-basis: 33.33333333%;
    -ms-flex-preferred-size: 33.33333333%;
    flex-basis: 33.33333333%
}

.white-section {
    display: none;
    margin-bottom: 1px;
    padding-top: 90px;
    padding-bottom: 110px;
    background-color: #fff;
    box-shadow: 0 0 0 1px #e7e8ea
}

    .white-section.press {
        position: relative;
        z-index: 1;
        display: none;
        padding-top: 55px;
        padding-bottom: 55px;
        box-shadow: 0 0 0 1px #e7e8ea;
        text-align: center
    }

    .white-section.no-paddings {
        padding-top: 0;
        padding-bottom: 0
    }

.press-link {
    display: none;
    padding: 15px 25px;
    opacity: .7;
    -webkit-transition: opacity 200ms ease;
    transition: opacity 200ms ease
}

    .press-link:hover {
        opacity: 1
    }

.background-section {
    display: none;
    padding-top: 90px;
    padding-bottom: 110px;
    background-image: url(../images/bg-gradient-1.svg),-webkit-linear-gradient(315deg,#7956ec,#2fb9f8);
    background-image: url(../images/bg-gradient-1.svg),linear-gradient(135deg,#7956ec,#2fb9f8);
    background-position: 50% 0,0 0;
    background-size: cover,auto;
    background-repeat: no-repeat,repeat;
    background-attachment: fixed,scroll;
    color: #fff
}

    .background-section.page-top {
        padding-bottom: 80px
    }

.footer-v1 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-bottom: 0;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    color: #fff
}

.footer-v1-column {
    width: 25%;
    padding-right: 15px;
    padding-left: 15px
}

.footer-description {
    max-width: 220px;
    margin-top: 17px;
    margin-bottom: 27px;
    opacity: .8;
    font-size: 16px;
    line-height: 25px;
    font-weight: 300
}

.footer-link-v1 {
    margin-top: 10px;
    float: left;
    clear: left;
    color: #fff;
    font-size: 13px;
    line-height: 20px;
    text-decoration: none
}

    .footer-link-v1:hover {
        text-decoration: underline
    }

.footer-bottom-v1 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    margin-top: 60px;
    margin-right: 15px;
    margin-left: 15px;
    padding-top: 20px;
    padding-bottom: 20px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-top: 1px solid hsla(0,0%,100%,.3);
    color: hsla(0,0%,100%,.8);
    font-size: 12px;
    line-height: 20px
}

.heart-icon {
    background-image: url(../images/heart-icon-red.svg);
    background-position: 50% 50%;
    background-repeat: no-repeat;
    color: transparent
}

    .heart-icon.white-heart-icon {
        background-image: url(../images/heart-icon-white.svg);
        background-size: auto
    }

.author-dark-link {
    color: #282b31;
    font-weight: 700;
    text-decoration: none
}

    .author-dark-link:hover {
        text-decoration: underline
    }

.social-button {
    border: 1px solid #e7e8ea;
    line-height: 0
}

    .social-button.left-button {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px
    }

        .social-button.left-button.white {
            border-color: hsla(0,0%,100%,.3)
        }

    .social-button.right-button {
        border-left-style: none;
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px
    }

        .social-button.right-button.white {
            border-color: hsla(0,0%,100%,.3)
        }

    .social-button.middle-button {
        border-left-style: none
    }

        .social-button.middle-button.white {
            border-color: hsla(0,0%,100%,.3)
        }

.video-tour-section {
    background-image: -webkit-linear-gradient(315deg,rgba(121,86,236,.85),rgba(47,185,248,.85));
    background-image: linear-gradient(135deg,rgba(121,86,236,.85),rgba(47,185,248,.85))
}

    .video-tour-section.phone {
        position: relative;
        overflow: hidden;
        background-image: -webkit-linear-gradient(315deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-phone.jpg);
        background-image: linear-gradient(135deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-phone.jpg);
        background-position: 0 0,50% 50%;
        background-size: auto,cover;
        background-attachment: scroll,fixed
    }

    .video-tour-section.tablet {
        background-image: -webkit-linear-gradient(315deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-tablet.jpg);
        background-image: linear-gradient(135deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-tablet.jpg);
        background-position: 0 0,50% 50%;
        background-size: auto,cover;
        background-repeat: repeat,no-repeat;
        background-attachment: scroll,fixed
    }

.video-tour-phone {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 90px;
    padding-bottom: 90px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    text-decoration: none
}

    .video-tour-phone:hover {
        text-decoration: none
    }

.video-tour-text {
    color: #fff;
    font-size: 25px;
    line-height: 35px;
    text-decoration: none
}

    .video-tour-text:hover {
        text-decoration: none
    }

.big-side-feature {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center
}

    .big-side-feature.second {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: reverse;
        -webkit-flex-direction: row-reverse;
        -ms-flex-direction: row-reverse;
        flex-direction: row-reverse
    }

    .big-side-feature.side-feature-blocks {
        margin-bottom: -30px;
        padding-top: 85px
    }

.big-feature-info {
    width: 33.33333333%;
    margin-right: 8.33333333%;
    margin-left: 8.33333333%;
    padding-right: 15px;
    padding-left: 15px
}

    .big-feature-info.big-device {
        width: 33.33333333%;
        margin-right: 0%;
        margin-left: 0%
    }

.feature-text-big {
    margin-top: 16px;
    color: #676b75;
    line-height: 30px
}

.feature-device {
    position: relative;
    left: 0;
    top: 0;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: center
}

    .feature-device.laptop {
        width: 66.66666667%
    }

    .feature-device.desktop {
        width: 66.66666667%;
        margin-top: -20px;
        margin-bottom: -90px;
        padding-left: 100px
    }

    .feature-device.web {
        width: 66.66666666%;
        padding-right: 100px;
        padding-left: 100px
    }

    .feature-device.tab-device {
        overflow: visible;
        width: 66.66666667%;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto
    }

    .feature-device.phone {
        padding-top: 20px;
        padding-bottom: 20px
    }

.stars-center {
    display: block;
    margin: 20px auto 30px
}

.testimonials-v1 {
    height: 420px;
    padding-top: 50px;
    background-color: transparent
}

.review-slide-v1 {
    width: 33.333333%;
    padding-top: 40px;
    padding-right: 15px;
    padding-left: 15px;
    border-radius: 12px;
    -webkit-perspective-origin: 50% 50%;
    perspective-origin: 50% 50%
}

.review-v1 {
    position: relative;
    padding-right: 40px;
    padding-left: 40px;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 10px 20px 0 rgba(40,43,49,.16);
    color: #282b31;
    font-size: 15px;
    font-style: italic;
    font-weight: 400;
    text-align: center
}

.review-v1-avatar {
    position: relative;
    top: -35px;
    display: block;
    width: 70px;
    height: 70px;
    margin-right: auto;
    margin-bottom: -17px;
    margin-left: auto;
    border-radius: 100%;
    background-color: #999ea8;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.16)
}

.review-author {
    margin-top: 17px;
    padding-top: 15px;
    padding-bottom: 24px;
    border-top: 1px solid #e7e8ea;
    color: #999ea8;
    font-size: 12px;
    line-height: 20px;
    font-style: normal;
    font-weight: 700
}

.reviews-nav-dots {
    position: relative;
    height: 10px;
    padding-top: 0;
    font-size: 10px
}

.hero-device {
    width: 50%;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
    text-align: center
}

    .hero-device.desktop {
        position: relative;
        left: -90px;
        padding-top: 40px
    }

.footer-section-v1 {
    padding-top: 40px;
    background-image: url(../images/bg-gradient-1.svg);
    background-position: 50% 50%;
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed
}

.footer-section-v4 {
    border-top: 1px solid #e7e8ea;
    background-color: #fff
}

.footer-v4 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 20px;
    padding-bottom: 25px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    color: #999ea8;
    font-size: 12px;
    text-align: center
}

.container {
    padding: 3px;
    border: 1px solid #e6e6e6;
    width: 700px;
    overflow: hidden
}

.marquee {
    display: block;
    position: relative;
    width: 800px;
    height: 160px;
    animation: scroll 10s linear infinite;
    padding: 3px
}

    .marquee:hover {
        animation-play-state: paused
    }

.img1 {
    width: 150px;
    height: 150px;
    background-color: #f6f6f6;
    margin: 3px;
    float: left
}

.tech-slideshow {
    height: 200px;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    transform: translate3d(0,0,0)
}

    .tech-slideshow > div {
        height: 200px;
        width: 9264px;
        background: url(https://whatelse.io/images/integrations1.png);
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        transform: translate3d(0,0,0)
    }

    .tech-slideshow .mover-1 {
        animation: moveSlideshow 12s linear infinite
    }

@keyframes moveSlideshow {
    100% {
        transform: translateX(-66.6666%)
    }
}

@keyframes scroll {
    0% {
        left: 800px
    }

    100% {
        left: -800px
    }
}

.author-link-white {
    color: #fff;
    text-decoration: none
}

    .author-link-white:hover {
        text-decoration: underline
    }

.footer-logo {
    padding-top: 10px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-size: 20px;
    font-weight: 800;
    text-align: left
}

.author-copyright {
    padding-top: 10px;
    -webkit-box-flex: 40%;
    -webkit-flex: 40%;
    -ms-flex: 40%;
    flex: 40%;
    line-height: 30px
}

.social-buttons {
    padding-top: 10px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: right
}

.white-text {
    color: #fff
}

.store-badge {
    margin-right: 10px;
    margin-left: 10px;
    border-radius: 6px;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08);
    -webkit-transition: box-shadow 200ms ease;
    transition: box-shadow 200ms ease
}

    .store-badge:hover {
        box-shadow: 0 12px 24px 0 rgba(40,43,49,.24)
    }

.store-badges {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 26px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center
}

.feature-cards-big {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 66px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.feature-small-icon {
    position: absolute;
    display: block;
    margin-top: 8px
}

.feature-small-heading {
    margin-left: 45px
}

.feature-small-container {
    width: 33.3333333%;
    padding: 20px 15px 25px
}

.feature-small-text {
    margin-left: 45px;
    color: #676b75;
    font-size: 14px
}

.feature-card-v2 {
    margin: 15px;
    padding: 50px 30px;
    -webkit-box-flex: 20%;
    -webkit-flex: 20%;
    -ms-flex: 20%;
    flex: 20%;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgba(40,43,49,.12);
    font-size: 14px;
    text-align: center
}

.footer-section-v2 {
    padding-top: 30px;
    border-top: 1px solid #e7e8ea;
    background-color: #fff
}

.footer-v2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-bottom: 50px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start
}

.footer-right-links {
    width: 30%;
    padding-top: 16px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: right
}

.footer-link-v2 {
    margin-top: 10px;
    float: right;
    clear: right;
    color: #282b31;
    font-size: 13px;
    line-height: 20px;
    text-decoration: none
}

    .footer-link-v2:hover {
        text-decoration: underline
    }

.footer-bottom-v2 {
    width: 100%;
    padding-top: 20px;
    padding-bottom: 20px;
    border-top: 1px solid #e7e8ea;
    color: #999ea8;
    font-size: 12px;
    line-height: 20px;
    text-align: center
}

.footer-store-badge {
    margin-right: 10px;
    margin-left: 10px;
    border-radius: 6px;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08);
    -webkit-transition: box-shadow 200ms ease;
    transition: box-shadow 200ms ease
}

    .footer-store-badge:hover {
        box-shadow: 0 12px 24px 0 rgba(40,43,49,.16)
    }

.hero-play-button {
    margin-top: 190px;
    padding-top: 30px;
    padding-bottom: 30px
}

.phone-center-features {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 30px;
    padding-bottom: 10px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center
}

.phone-side-features {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 25%;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: right
}

.side-feature {
    padding-top: 22px;
    padding-bottom: 25px
}

.highlight-icon {
    margin-bottom: 11px;
    border-radius: 11px;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.12)
}

.text-left {
    text-align: left
}

.phone-center {
    width: 33.333333%;
    padding-right: 15px;
    padding-left: 15px;
    text-align: center
}

.features-small {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: -20px;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.footer-store-badges {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 46%;
    padding-top: 30px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto
}

.feature-icon-50 {
    width: 50px;
    margin-bottom: 15px;
    border-radius: 10px;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

.feature-card-big {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-right: 15px;
    margin-left: 15px;
    padding: 60px 30px 62px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgba(40,43,49,.12)
}

.feature-card-heading {
    margin-bottom: 15px
}

.feature-card-icon {
    width: 70px;
    max-width: none;
    margin-bottom: 21px;
    border-radius: 15px;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

.feature-card-link {
    margin-top: 15px;
    font-size: 14px
}

.feature-tabs-wrapper {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-top: 30px;
    margin-bottom: 10px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center
}

.feature-tabs {
    width: 33.333333%;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto
}

    .feature-tabs.big {
        width: 41.66666666%
    }

.feature-tab-v2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-top: 10px;
    margin-bottom: 10px;
    padding-top: 31px;
    padding-bottom: 26px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    border-radius: 12px;
    background-color: transparent;
    -webkit-transition: background-color 200ms ease,box-shadow 200ms ease;
    transition: background-color 200ms ease,box-shadow 200ms ease
}

    .feature-tab-v2:hover {
        background-color: #fff;
        box-shadow: 0 1px 3px 0 rgba(40,43,49,.12)
    }

    .feature-tab-v2.w--current {
        background-color: #fff;
        box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
    }

    .feature-tab-v2.tab-1.w--current {
        background-image: url(../images/tab-reload-icon.svg);
        background-position: 107% 50%;
        background-size: 120px;
        background-repeat: no-repeat
    }

    .feature-tab-v2.tab-2.w--current {
        background-image: url(../images/tab-inbox-icon.svg);
        background-position: 107% 50%;
        background-size: 120px;
        background-repeat: no-repeat
    }

    .feature-tab-v2.tab-3.w--current {
        background-image: url(../images/tab-clock-icon.svg);
        background-position: 107% 50%;
        background-size: 120px;
        background-repeat: no-repeat
    }

.feature-tab-heading {
    margin-bottom: 10px;
    font-size: 16px;
    line-height: 20px
}

.download {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-top: -36px;
    margin-bottom: -50px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    color: #fff
}

.download-text {
    width: 50%;
    padding-right: 15px;
    padding-bottom: 20px;
    padding-left: 15px
}

.device-big-image {
    max-width: none
}

    .device-big-image.fright {
        float: right
    }

    .device-big-image.tab-big-device {
        margin-right: 8.33333333%;
        margin-left: 8.33333333%;
        float: right
    }

.press-container {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    max-width: 770px;
    margin: 66px auto 10px;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    border-radius: 12px;
    background-image: -webkit-linear-gradient(270deg,#fff,#fff);
    background-image: linear-gradient(180deg,#fff,#fff);
    box-shadow: 0 1px 3px 0 rgba(40,43,49,.12)
}

.press-color-link {
    width: 33.33333333%;
    padding: 40px 10px;
    background-color: #fff;
    box-shadow: 0 0 0 1px #e7e8ea;
    text-align: center
}

.gradient-button {
    height: 50px;
    padding-top: 11px;
    padding-right: 20px;
    padding-left: 20px;
    border-radius: 6px;
    background-image: -webkit-linear-gradient(315deg,#ff8e64,#ffe641);
    background-image: linear-gradient(135deg,#ff8e64,#ffe641);
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08);
    -webkit-transition: box-shadow 200ms ease;
    transition: box-shadow 200ms ease;
    color: #fff;
    font-size: 17px;
    line-height: 30px;
    font-weight: 700;
    text-align: center;
    letter-spacing: 1px
}

    .gradient-button:hover {
        background-image: -webkit-linear-gradient(315deg,#ff8e64,#ffe641);
        background-image: linear-gradient(135deg,#ff8e64,#ffe641);
        box-shadow: 0 12px 24px 0 rgba(40,43,49,.16)
    }

    .gradient-button.hero-first-button {
        margin-right: 10px
    }

    .gradient-button.pricing-buy-button {
        display: block;
        margin-top: 30px
    }

    .gradient-button.pricing-free-button {
        display: block;
        margin-top: 40px;
        background-image: -webkit-linear-gradient(315deg,#009fc5,#3cecb0);
        background-image: linear-gradient(135deg,#009fc5,#3cecb0)
    }

    .gradient-button.trial-button {
        margin-right: 10px;
        margin-left: 10px;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1
    }

    .gradient-button.buy-button {
        margin-right: 20px
    }

    .gradient-button.blue {
        background-image: -webkit-linear-gradient(315deg,#7956ec,#2fb9f8);
        background-image: linear-gradient(135deg,#7956ec,#2fb9f8)
    }

.square-features-container {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    margin: 30px 15px 10px;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    background-color: #fff;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

.square-feature {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 50%;
    padding-right: 105px;
    padding-left: 105px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center
}

.download-buttons-contaner {
    padding-right: 15px;
    padding-left: 15px
}

.square-feature-image {
    width: 50%;
    height: 560px;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
    background-color: #469af4
}

    .square-feature-image.image-1 {
        background-image: url(../images/tablet-feature-1.jpg);
        background-position: 50% 0%;
        background-size: cover;
        background-repeat: no-repeat
    }

    .square-feature-image.image-2 {
        background-image: url(../images/tablet-feature-2.jpg);
        background-position: 50% 0;
        background-size: cover;
        background-repeat: no-repeat
    }

    .square-feature-image.image-3 {
        background-image: url(../images/all-platforms-feature-1.jpg);
        background-position: 100% 0;
        background-size: cover;
        background-repeat: no-repeat
    }

    .square-feature-image.image-4 {
        background-image: url(../images/all-platforms-feature-2.jpg);
        background-position: 0 0;
        background-size: cover;
        background-repeat: no-repeat
    }

.video-tour-tablet {
    display: block;
    padding-top: 100px;
    padding-bottom: 120px;
    text-align: center
}

.section-description {
    display: inline-block;
    max-width: 570px;
    color: #676b75;
    font-size: 16px;
    line-height: 30px
}

    .section-description.white-text {
        display: inline-block;
        color: #fff
    }

    .section-description.white-transparent-text {
        color: hsla(0,0%,100%,.8)
    }

.contact-window {
    display: block;
    width: 64%;
    margin: 60px auto 10px;
    padding: 70px 60px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 12px 24px 0 rgba(40,43,49,.16);
    color: #282b31
}

    .contact-window.popup {
        position: relative;
        z-index: 1000;
        margin-top: 0;
        margin-bottom: 0
    }

.input {
    height: 50px;
    margin-bottom: 0;
    padding-top: 11px;
    padding-right: 20px;
    padding-left: 20px;
    border: 1px solid #d3d5da;
    border-radius: 6px;
    background-color: #f8f9fa;
    font-size: 17px;
    line-height: 30px
}

    .input.text-area {
        min-height: 180px;
        margin-right: 10px;
        margin-bottom: 30px;
        margin-left: 10px;
        padding: 21px 30px
    }

    .input.contact-input {
        width: 50%;
        margin-right: 10px;
        margin-bottom: 20px;
        margin-left: 10px;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1
    }

    .input.subscribe-input {
        margin-right: 20px;
        margin-bottom: 0;
        padding-top: 10px;
        padding-left: 50px;
        border-style: none;
        background-color: #fff;
        background-image: url(../images/mail-icon.svg);
        background-position: 20px 50%;
        background-repeat: no-repeat
    }

    .input.trial-input {
        margin-right: 10px;
        margin-bottom: 0;
        margin-left: 10px;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1
    }

    .input.trial-white-input {
        margin-right: 10px;
        margin-bottom: 0;
        margin-left: 10px;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1;
        border-style: none;
        background-color: #fff
    }

    .input.footer-subscribe {
        margin-top: 24px;
        margin-bottom: 30px;
        border-style: none;
        background-color: #fff
    }

    .input.trial-email-input {
        margin: 20px 10px 25px
    }

    .input.notify-input {
        margin-right: 20px;
        margin-bottom: 0;
        padding-left: 50px;
        background-image: url(../images/mail-icon.svg);
        background-position: 20px 50%;
        background-repeat: no-repeat
    }

    .input.password-input {
        margin-top: 16px;
        margin-bottom: 24px;
        border-color: #fff;
        text-align: center
    }

.contact-form {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 30px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.reviews-left-wrapper {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 0;
    width: 33%;
    height: 100%;
    background-color: #fff
}

.reviews-left-container {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 33.33333333%;
    padding: 150px 30px 170px 15px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    border-right: 1px solid #e7e8ea;
    background-color: #fff
}

.stars-left {
    margin-bottom: 44px
}

.reviews-right-container {
    padding: 110px 15px 110px 60px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.review-v2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    max-width: 570px;
    margin-top: 10px;
    margin-bottom: 10px;
    padding: 30px 15px 21px;
    float: left;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

    .review-v2.review-reversed {
        float: right;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: reverse;
        -webkit-flex-direction: row-reverse;
        -ms-flex-direction: row-reverse;
        flex-direction: row-reverse
    }

.review-v2-avatar {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    margin-left: 15px;
    border-radius: 50%
}

.review-v2-text {
    margin-top: -7px;
    margin-right: 15px;
    margin-left: 15px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    font-size: 14px;
    line-height: 25px;
    font-style: italic;
    font-weight: 400
}

.review-v2-author-info {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    margin-top: 22px;
    margin-right: 15px;
    margin-left: 15px;
    padding-top: 13px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    border-top: 1px solid #e7e8ea;
    color: #999ea8;
    font-size: 12px;
    line-height: 25px
}

    .review-v2-author-info.author-info-reversed {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: reverse;
        -webkit-flex-direction: row-reverse;
        -ms-flex-direction: row-reverse;
        flex-direction: row-reverse
    }

.review-v2-icon {
    width: 16px;
    height: 16px;
    margin-top: 4px;
    background-image: url(../images/twitter-icon.svg);
    background-position: 50% 50%;
    background-repeat: no-repeat;
    opacity: .25;
    -webkit-transition: opacity 200ms ease;
    transition: opacity 200ms ease
}

    .review-v2-icon:hover {
        opacity: .5
    }

.reviews-v2-header {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.reviews-v2-more-text {
    margin-bottom: 20px;
    color: #999ea8;
    font-size: 15px;
    line-height: 30px;
    font-weight: 700
}

.reviews-v2-store-link {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    color: #282b31;
    line-height: 30px;
    font-weight: 700
}

    .reviews-v2-store-link:hover {
        text-decoration: underline
    }

.arrow-icon {
    margin-right: 12px
}

.pricing-v1 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center
}

.pricing-v1-info {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 33.3333333%;
    padding-right: 16px;
    padding-left: 16px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start
}

.pricing-v1-plans {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.pricing-v1-plan {
    position: relative;
    z-index: 1;
    width: 350px;
    padding: 50px 50px 60px;
    border-radius: 12px;
    background-color: #0098ff;
    background-image: url(../images/bg-gradient-1.svg);
    background-position: 50% 0;
    background-size: cover;
    background-repeat: no-repeat;
    box-shadow: 0 6px 12px 0 rgba(0,0,0,.08);
    color: #fff
}

.pricing-v1-plan-name {
    height: 40px;
    margin-right: -50px;
    margin-left: -50px;
    padding-right: 50px;
    padding-left: 50px;
    border-top: 1px solid hsla(0,0%,100%,.3);
    border-bottom: 1px solid hsla(0,0%,100%,.3);
    background-color: hsla(0,0%,100%,.07);
    color: #fff;
    font-size: 11px;
    line-height: 40px;
    font-weight: 700;
    text-align: center;
    letter-spacing: 2px;
    text-transform: uppercase
}

.pricing-v1-price {
    margin-top: 30px;
    margin-bottom: 20px;
    font-size: 60px;
    line-height: 80px;
    font-weight: 300;
    text-align: center
}

.pricing-v1-plan-feature {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    font-size: 15px;
    line-height: 30px
}

.pricing-feature-yes-icon {
    margin-right: 14px;
    opacity: .6
}

.pricing-v1-free-plan-name {
    height: 40px;
    margin-right: -50px;
    margin-left: -50px;
    padding-right: 50px;
    padding-left: 50px;
    border-top: 1px solid #e7e8ea;
    border-bottom: 1px solid #e7e8ea;
    background-color: #f8f9fa;
    color: #676b75;
    font-size: 11px;
    line-height: 40px;
    font-weight: 700;
    text-align: center;
    letter-spacing: 2px;
    text-transform: uppercase
}

.pricing-v1-free-plan {
    width: 335px;
    margin-top: 20px;
    margin-bottom: 20px;
    margin-left: -15px;
    padding: 50px 50px 60px;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgba(40,43,49,.12);
    color: #676b75
}

.pricing-v1-free {
    margin-top: 7px;
    margin-bottom: 3px;
    color: #282b31;
    font-size: 40px;
    line-height: 100px;
    font-weight: 300;
    text-align: center;
    text-transform: uppercase
}

.features-video {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 46px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.features-video-list {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 50%;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-align-content: center;
    -ms-flex-line-pack: center;
    align-content: center
}

.features-video-preview {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 50%;
    height: 360px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
    border-radius: 12px;
    background-image: -webkit-linear-gradient(315deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-laptop.jpg);
    background-image: linear-gradient(135deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-laptop.jpg);
    background-position: 0 0,50% 50%;
    background-size: auto,cover;
    background-repeat: repeat,no-repeat;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

.feature-video-list-item {
    width: 50%;
    padding-top: 20px;
    padding-right: 45px;
    padding-bottom: 27px;
    text-align: left
}

.feature-video-header {
    margin-top: 20px
}

.hero-desktop-bottom {
    height: 120px;
    margin-top: -76px;
    background-color: #f8f9fa
}

.device-big-center {
    margin-top: 57px;
    margin-bottom: -214px
}

.trial-form {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.trial-form-wrapper {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 66.66666666%;
    margin-right: auto;
    margin-left: auto;
    padding-top: 36px;
    padding-right: 5px;
    padding-left: 5px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    text-align: center
}

.trial-text {
    margin-top: 40px;
    margin-bottom: 3px;
    color: hsla(0,0%,100%,.8);
    font-size: 12px;
    line-height: 22px
}

    .trial-text.light-grey-text {
        width: 100%
    }

.white-bold-link {
    color: #fff;
    font-weight: 700;
    text-decoration: none
}

    .white-bold-link:hover {
        text-decoration: underline
    }

.questions-list {
    display: block;
    margin-bottom: -20px;
    padding-top: 50px;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-column-count: 2;
    column-count: 2;
    -webkit-column-gap: 0em;
    column-gap: 0em
}

.question {
    display: inline-block;
    padding: 20px 55px 25px 15px;
    text-align: left
}

.question-icon {
    position: relative;
    display: inline-block;
    float: left
}

.question-text {
    margin-top: 12px;
    margin-bottom: 8px;
    margin-left: 60px;
    font-weight: 700
}

.answer-text {
    margin-left: 60px;
    color: #676b75;
    font-size: 14px;
    line-height: 25px
}

.reviews-arrow {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    background-color: #fff;
    background-image: url(../images/arrow-left-icon.svg);
    background-position: 50% 50%;
    background-repeat: no-repeat;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.12);
    -webkit-transition: box-shadow 200ms ease;
    transition: box-shadow 200ms ease;
    color: transparent
}

    .reviews-arrow:hover {
        box-shadow: 0 12px 24px 0 rgba(40,43,49,.16)
    }

    .reviews-arrow.right {
        background-image: url(../images/arrow-right-icon.svg);
        background-size: auto
    }

.review-v3-avatar {
    width: 70px;
    height: 70px;
    margin-bottom: 37px;
    border-radius: 50%;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

.review-v3 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center
}

.review-v3-text {
    max-width: 570px;
    padding-right: 15px;
    padding-left: 15px;
    color: #fff;
    font-size: 20px;
    line-height: 35px;
    font-style: italic
}

.review-v3-author {
    margin-top: 32px;
    padding: 8px 50px 22px;
    background-image: url(../images/quote-icon.svg);
    background-position: 50% 0;
    background-size: auto;
    background-repeat: no-repeat;
    color: #fff;
    font-size: 20px;
    line-height: 35px;
    text-align: center
}

.review-v3-author-text {
    color: hsla(0,0%,100%,.8);
    font-size: 12px;
    line-height: 20px
}

.feature-cards-v2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 40px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.testimonials-v3 {
    height: 410px;
    padding-top: 50px;
    background-color: transparent
}

.pricing-v2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 46px;
    padding-bottom: 10px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    font-size: 11px;
    line-height: 20px
}

.pricing-v2-plan {
    margin-right: 15px;
    margin-left: 15px;
    padding: 50px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-radius: 12px
}

    .pricing-v2-plan.best-plan-v2 {
        background-color: #fff;
        box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
    }

.pricing-v2-name {
    height: 40px;
    border: 1px solid #e7e8ea;
    border-radius: 6px;
    background-image: -webkit-linear-gradient(270deg,#fff,#fff);
    background-image: linear-gradient(180deg,#fff,#fff);
    color: #676b75;
    font-size: 11px;
    line-height: 40px;
    font-weight: 700;
    letter-spacing: 2px;
    text-transform: uppercase
}

.pricing-v2-price {
    margin-top: 14px;
    color: #282b31;
    font-size: 40px;
    line-height: 86px;
    font-weight: 300
}

.pricing-v2-features {
    overflow: hidden;
    margin-bottom: 40px;
    border: 1px solid #e7e8ea;
    border-radius: 6px
}

.pricing-v2-feature {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding: 9px 16px 10px;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    background-color: #fff;
    box-shadow: 0 0 0 1px #e7e8ea;
    font-size: 15px;
    line-height: 20px;
    text-align: left
}

    .pricing-v2-feature.feature-off-v2 {
        color: #babdc3
    }

.block {
    display: block
}

.subscribe-container {
    display: block;
    max-width: 770px;
    margin: 66px auto 10px;
    padding: 50px 100px;
    border-radius: 12px;
    background-color: #469af4;
    background-image: url(../images/bg-gradient-1.svg);
    background-position: 50% 0;
    background-size: cover;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08);
    color: #fff
}

.subscribe-form {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start
}

.footer-section-v3 {
    background-image: url(../images/bg-gradient-1.svg);
    background-position: 50% 0;
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
    color: #fff
}

.footer-v3 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 20px;
    padding-bottom: 33px;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start
}

.footer-v3-author {
    padding-top: 13px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    color: hsla(0,0%,100%,.8);
    font-size: 12px;
    text-align: right
}

.footer-v3-nav {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 3px;
    padding-bottom: 10px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-flex: 30%;
    -webkit-flex: 30%;
    -ms-flex: 30%;
    flex: 30%
}

.footer-v3-nav-link {
    padding: 10px 15px;
    color: #fff;
    font-size: 11px;
    font-weight: 700;
    letter-spacing: .5px;
    text-transform: uppercase
}

    .footer-v3-nav-link:hover {
        text-decoration: underline
    }

.footer-v3-copyright {
    width: 100%;
    margin-top: 8px;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
    color: hsla(0,0%,100%,.8);
    font-size: 11px;
    line-height: 20px;
    text-align: center
}

.hero-intro-center {
    display: block;
    width: 100%;
    padding-top: 40px;
    padding-right: 10px;
    padding-left: 10px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    text-align: center
}

.hero-center {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 110px;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-align-content: flex-start;
    -ms-flex-line-pack: start;
    align-content: flex-start
}

.hero-device-center {
    margin-top: 70px
}

.middle {
    display: block;
    margin-right: auto;
    margin-left: auto
}

.features-big-v2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.feature-big-v2 {
    width: 50%;
    padding: 100px 105px
}

    .feature-big-v2.left-border {
        border-left: 1px solid #e7e8ea
    }

.feature-big-heading-v2 {
    margin-top: 30px
}

.light-grey-text {
    color: #babdc3
}

.bold-grey-link {
    color: #999ea8;
    font-weight: 700
}

    .bold-grey-link:hover {
        text-decoration: underline
    }

.video-tour-web {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 470px;
    max-width: 770px;
    margin: 65px auto 60px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 12px;
    background-image: -webkit-linear-gradient(315deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-web.jpg);
    background-image: linear-gradient(135deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-web.jpg);
    background-position: 0 0,50% 50%;
    background-size: auto,cover;
    background-repeat: repeat,no-repeat;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

.pricing-v3-plan {
    margin-right: 15px;
    margin-left: 15px;
    padding: 35px 30px 40px;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 6px 12px 0 rgba(0,0,0,.08);
    font-size: 16px
}

    .pricing-v3-plan.best {
        background-color: transparent;
        background-image: url(../images/bg-gradient-1.svg);
        background-position: 50% 0;
        background-size: cover;
        background-repeat: no-repeat;
        color: #fff
    }

.pricing-v3-name {
    margin-bottom: 12px;
    color: #469af4;
    font-weight: 700;
    text-align: left;
    letter-spacing: 1px
}

    .pricing-v3-name.best {
        color: #fff
    }

.pricing-v3-price-container {
    display: block;
    width: 100%;
    height: 80px;
    margin-bottom: 12px;
    padding-top: 20px;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    font-weight: 300;
    text-align: left
}

.pricing-v3-features {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 40px;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    border-top: 1px solid #e7e8ea;
    text-align: left
}

    .pricing-v3-features.best-features {
        border-top-color: hsla(0,0%,100%,.3)
    }

.pricing-v3-feature {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    margin-bottom: -1px;
    padding-top: 9px;
    padding-bottom: 9px;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    border-bottom: 1px solid #e7e8ea;
    color: #282b31;
    font-size: 15px;
    line-height: 20px;
    text-align: left
}

    .pricing-v3-feature.off {
        color: #babdc3
    }

    .pricing-v3-feature.best-feature {
        border-bottom-color: hsla(0,0%,100%,.3);
        color: #fff
    }

        .pricing-v3-feature.best-feature.off {
            color: hsla(0,0%,100%,.5)
        }

.pricing-v3-price {
    position: absolute;
    display: inline-block;
    -webkit-box-flex: 0;
    -webkit-flex: 0 0 auto;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    font-size: 40px;
    line-height: 40px;
    text-transform: uppercase
}

.pricing-v3-per-text {
    display: block;
    margin-left: 85px;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
    color: #999ea8;
    font-size: 11px;
    line-height: 20px;
    font-weight: 700;
    letter-spacing: 2px;
    text-transform: uppercase
}

    .pricing-v3-per-text.best {
        color: #fff
    }

.pricing-v3-billed-text {
    display: block;
    margin-left: 85px;
    color: #999ea8;
    font-size: 11px;
    line-height: 20px
}

    .pricing-v3-billed-text.best {
        color: #fff
    }

.pricing-v3 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 45px;
    padding-bottom: 10px;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

    .pricing-v3.page {
        padding-top: 30px;
        padding-bottom: 80px
    }

.ceature-cards-v3 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 45px;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.feature-card-v3 {
    position: relative;
    overflow: hidden;
    margin-right: 15px;
    margin-bottom: 30px;
    margin-left: 15px;
    padding: 25px 40px 33px 110px;
    -webkit-box-flex: 45%;
    -webkit-flex: 45%;
    -ms-flex: 45%;
    flex: 45%;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 6px 12px 0 rgba(0,0,0,.08);
    color: #282b31;
    text-align: left
}

.feature-card-v3-icon {
    position: absolute;
    top: 40px;
    display: block;
    height: 100px;
    margin-left: -140px
}

.feature-card-v3-heading {
    margin-bottom: 15px
}

.footer-v5 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-top: -35px;
    margin-bottom: -110px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    color: #fff
}

.footer-v5-header {
    margin-bottom: 15px;
    font-weight: 700;
    text-transform: uppercase
}

.footer-v5-nav-list {
    width: 16.66666666%;
    padding-right: 15px;
    padding-left: 15px
}

.footer-v5-nav-link {
    display: block;
    padding-top: 5px;
    padding-bottom: 10px;
    color: #fff;
    font-size: 13px;
    line-height: 20px
}

    .footer-v5-nav-link:hover {
        text-decoration: underline
    }

.footer-newsletter {
    width: 33.33333333%;
    padding-right: 0;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto
}

.social-icon {
    padding: 11px;
    opacity: .6;
    -webkit-transition: opacity 200ms ease;
    transition: opacity 200ms ease
}

    .social-icon:hover {
        opacity: 1
    }

.feature-cards-v4 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    margin: 5px 15px 5px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgba(40,43,49,.12)
}

.feature-card-v4 {
    width: 65.33333333%;
    padding: 60px 38px 70px;
    text-align: center
}

    .feature-card-v4.middle-card {
        border-right: 1px solid #e7e8ea;
        border-left: 1px solid #e7e8ea
    }

.feature-card-v4-heading {
    margin-top: 4px
}

.platform-tabs {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-top: 25px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center
}

.platform-tab {
    padding: 17px;
    border-radius: 6px;
    background-color: transparent;
    opacity: .6;
    -webkit-transition: opacity 200ms ease;
    transition: opacity 200ms ease;
    line-height: 10px
}

    .platform-tab:hover {
        opacity: 1
    }

    .platform-tab.w--current {
        background-image: -webkit-linear-gradient(315deg,#ff8e64,#ffe641);
        background-image: linear-gradient(135deg,#ff8e64,#ffe641);
        box-shadow: 0 6px 12px 0 rgba(40,43,49,.08);
        opacity: 1
    }

.platform-tab-content {
    padding-top: 50px;
    text-align: center
}

.platforms-bottom-wrapper {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-top: -160px;
    margin-bottom: -110px;
    padding-top: 205px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    -ms-flex-align: end;
    align-items: flex-end;
    background-color: #f8f9fa
}

.bg-feature-cards {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 45px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center
}

.bg-feature-card {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    height: 270px;
    margin-right: 15px;
    margin-left: 15px;
    padding-right: 260px;
    padding-left: 40px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-radius: 12px;
    background-image: url(../images/all-platforms-card-phone.png),url(../images/bg-gradient-1.svg);
    background-position: 88% 37px,50% 0%;
    background-size: 182px,cover;
    background-repeat: no-repeat,no-repeat;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08);
    color: #fff;
    text-align: left
}

    .bg-feature-card.laptop {
        background-image: url(../images/all-platforms-card-laptop.png),url(../images/bg-gradient-1.svg);
        background-position: 147% 50%,50% 0;
        background-size: 348px,cover;
        background-repeat: no-repeat,no-repeat
    }

.bg-feature-text {
    margin-bottom: 5px;
    color: hsla(0,0%,100%,.8);
    font-size: 14px
}

.available-text {
    margin-top: 63px;
    margin-bottom: 2px;
    color: #999ea8;
    font-size: 15px;
    line-height: 25px
}

.text-link {
    color: #469af4
}

    .text-link:hover {
        text-decoration: underline
    }

.testimonials-v4 {
    margin-right: 8.33333333%;
    margin-bottom: 35px;
    margin-left: 8.33333333%;
    padding-top: 45px;
    padding-bottom: 20px;
    background-color: transparent
}

.review-slide-v4 {
    display: inline-block;
    width: 50%;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto
}

.testimonials {
    margin-bottom: 50px;
    background-color: transparent
}

.review-v4 {
    padding: 30px 30px 32px;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08);
    font-size: 14px;
    line-height: 25px;
    font-style: italic
}

.review-v4-avatar {
    position: absolute;
    left: 0;
    top: 0;
    width: 50px;
    border-radius: 50%
}

.review-v4-author {
    position: relative;
    height: 50px;
    margin-bottom: 23px;
    font-style: normal;
    font-weight: 400
}

.review-v4-author-name {
    margin-left: 65px;
    padding-top: 3px;
    font-size: 16px;
    line-height: 25px;
    font-weight: 700
}

.review-v4-author-text {
    margin-left: 65px;
    color: #999ea8;
    font-size: 12px;
    line-height: 16px
}

.review-v4-social-icon {
    position: absolute;
    top: 0;
    right: 0;
    width: 16px;
    height: 16px;
    margin-top: 10px;
    margin-right: 10px;
    background-image: url(../images/twitter-icon.svg);
    background-position: 0 0;
    opacity: .25;
    -webkit-transition: opacity 200ms ease;
    transition: opacity 200ms ease
}

    .review-v4-social-icon:hover {
        opacity: .5
    }

.platform-cards {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 45px;
    padding-bottom: 10px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.platform-card {
    margin-right: 15px;
    margin-left: 15px;
    padding: 60px 30px;
    -webkit-box-flex: 20%;
    -webkit-flex: 20%;
    -ms-flex: 20%;
    flex: 20%;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 1px 3px 0 rgba(40,43,49,.12);
    -webkit-transition: box-shadow 200ms ease;
    transition: box-shadow 200ms ease;
    color: #282b31;
    text-align: center
}

    .platform-card:hover {
        box-shadow: 0 12px 24px 0 rgba(40,43,49,.16)
    }

.platform-card-heading {
    font-weight: 800
}

.features-tour {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 30px;
    padding-bottom: 10px
}

.features-tour-info {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 33.33333333%;
    padding-right: 8.33333333%;
    padding-left: 15px;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    color: #fff
}

.features-tour-list {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    margin-right: 15px;
    margin-left: 15px;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 12px 24px 0 rgba(40,43,49,.16);
    color: #282b31
}

.features-tour-list-item {
    position: relative;
    width: 50%;
    padding: 30px 30px 30px 110px;
    background-color: #fff;
    box-shadow: 0 0 0 1px #e7e8ea
}

.feature-tour-list-icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 50px;
    margin-top: 40px;
    margin-left: 30px;
    border-radius: 10px
}

.features-video-tour {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    height: 90px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    background-image: -webkit-linear-gradient(315deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-laptop.jpg);
    background-image: linear-gradient(135deg,rgba(121,86,236,.85),rgba(47,185,248,.85)),url(../images/video-tour-laptop.jpg);
    background-position: 0 0,50% 50%;
    background-size: auto,cover;
    background-repeat: repeat,no-repeat;
    color: #fff;
    font-weight: 700;
    text-align: center
}

.feature-icon-left-block {
    position: relative;
    margin-top: 64px;
    margin-bottom: 64px;
    padding-left: 80px;
    text-align: left
}

.side-feature-icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 50px;
    border-radius: 10px;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

.trial-form-window {
    display: block;
    max-width: 770px;
    margin: 80px auto 10px;
    padding-right: 100px;
    padding-bottom: 40px;
    padding-left: 100px;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 12px 24px 0 rgba(40,43,49,.16);
    color: #282b31;
    text-align: center
}

.trial-form-v2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-right: -11px;
    margin-left: -11px;
    padding-top: 25px;
    padding-bottom: 15px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.trial-app-icon {
    width: 90px;
    margin-top: -45px;
    margin-bottom: 25px;
    border-radius: 18px;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

.newsletter-checkbox {
    width: 100%;
    margin-bottom: 28px;
    padding-left: 30px;
    color: #999ea8;
    font-size: 13px;
    line-height: 22px;
    text-align: left
}

.checkbox {
    margin-right: 8px;
    font-size: 20px
}

.features-v2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: -20px;
    padding-top: 10px;
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start
}

.features-v2-info {
    width: 25%;
    padding-right: 15px;
    padding-left: 15px
}

.features-v2-list {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.feature-v2-item {
    position: relative;
    margin: 20px 15px 25px;
    padding-left: 80px;
    -webkit-box-flex: 1;
    -webkit-flex-grow: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -webkit-flex-basis: 45%;
    -ms-flex-preferred-size: 45%;
    flex-basis: 45%
}

.background-full {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    height: 100vh;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    background-image: url(../images/bg-gradient-1.svg);
    background-position: 50% 0;
    background-size: cover;
    background-repeat: no-repeat
}

.coming-soon-wrapper {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    max-width: 570px;
    padding-right: 20px;
    padding-bottom: 10px;
    padding-left: 20px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    color: #fff;
    text-align: center
}

.coming-soon-text-v1 {
    margin-top: -4px;
    margin-bottom: 20px;
    color: hsla(0,0%,100%,.8);
    font-size: 16px;
    line-height: 30px
}

.notify-form {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 430px
}

.coming-soon-social-v1 {
    position: fixed;
    left: 30px;
    top: 30px
}

.coming-soon-copyright-text {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding-top: 20px;
    padding-bottom: 25px;
    color: hsla(0,0%,100%,.8);
    font-size: 11px;
    line-height: 15px;
    text-align: center
}

.coming-soon-window {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    max-width: 670px;
    margin-right: 20px;
    margin-left: 20px;
    padding-right: 80px;
    padding-bottom: 70px;
    padding-left: 80px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 12px;
    background-color: #fff;
    box-shadow: 0 12px 24px 0 rgba(40,43,49,.16);
    text-align: center
}

.coming-soon-text-v2 {
    margin-top: -4px;
    margin-bottom: 30px;
    color: #676b75;
    font-size: 16px;
    line-height: 30px
}

.coming-soon-social-v2 {
    width: 100%;
    margin-top: 20px
}

.play-button {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 70px;
    height: 70px;
    margin-right: 30px;
    margin-left: 30px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 50%;
    background-color: #fff;
    background-image: url(../images/play-icon.svg);
    background-position: 52% 50%;
    background-repeat: no-repeat
}

    .play-button.tablet {
        display: block;
        margin-top: 20px;
        margin-right: auto;
        margin-left: auto
    }

    .play-button.small {
        width: 50px;
        height: 50px;
        background-size: 16px 16px
    }

.play-wave {
    position: absolute;
    left: 0;
    top: 0;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100%;
    height: 100%;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    border-style: solid;
    border-width: 2px;
    border-color: hsla(0,0%,100%,.8);
    border-radius: 50%;
    background-color: transparent
}

.hero-phone-image {
    max-height: 740px
}

.dropdown-arrow {
    width: 10px;
    height: 10px;
    margin-right: 15px;
    background-image: url(../images/dropdown-arrow-icon.svg);
    background-position: 50% 50%;
    background-repeat: no-repeat;
    color: hsla(0,0%,100%,0)
}

.dropdown-list {
    position: absolute;
    overflow: hidden;
    margin-top: 10px;
    box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
}

    .dropdown-list.w--open {
        margin-left: -5px;
        border-radius: 6px;
        background-color: #fff
    }

.dropdown-link {
    padding-top: 8px;
    padding-right: 25px;
    padding-bottom: 8px;
    -webkit-transition: background-color 200ms ease,color 200ms ease;
    transition: background-color 200ms ease,color 200ms ease;
    font-size: 14px;
    line-height: 30px;
    font-weight: 400
}

    .dropdown-link:hover {
        background-color: #469af4;
        color: #fff
    }

    .dropdown-link.w--current {
        color: #282b31;
        font-weight: 700
    }

        .dropdown-link.w--current:hover {
            color: #fff
        }

.hero-tablet-image {
    max-height: 720px
}

.feature-tab-v1 {
    margin-top: 10px;
    margin-bottom: 10px;
    padding-top: 31px;
    padding-bottom: 26px;
    padding-left: 74px;
    border-radius: 12px;
    background-color: transparent;
    -webkit-transition: background-color 200ms ease,box-shadow 200ms ease;
    transition: background-color 200ms ease,box-shadow 200ms ease
}

    .feature-tab-v1:hover {
        background-color: #fff;
        box-shadow: 0 1px 3px 0 rgba(40,43,49,.12)
    }

    .feature-tab-v1.w--current {
        background-color: #fff;
        box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
    }

.tab-icon {
    position: absolute;
    left: 30px;
    top: 30px
}

.text-block {
    text-align: right
}

.page-header {
    display: block;
    max-width: 570px;
    margin: 70px auto 10px;
    color: #fff
}

.platform-tab-image {
    max-height: 640px
}

.hero-video {
    position: relative;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.feature-v2-header {
    margin-top: 0
}

.table {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    max-width: 100%;
    margin-top: 65px;
    margin-right: 15px;
    margin-left: 15px;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    border: 1px solid #e7e8ea;
    border-radius: 6px
}

.table-cell {
    display: block;
    height: 48px;
    padding: 14px 20px;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-flex: 0;
    -webkit-flex: 0 auto;
    -ms-flex: 0 auto;
    flex: 0 auto;
    background-color: #fff;
    box-shadow: 0 0 0 1px #e7e8ea;
    font-size: 14px;
    line-height: 20px
}

    .table-cell.column-header {
        font-weight: 700
    }

    .table-cell.plus-feature {
        background-image: url(../images/success-icon.svg);
        background-position: 50% 50%;
        background-repeat: no-repeat;
        color: transparent
    }

    .table-cell.minus-feature {
        background-image: url(../images/minus-icon.svg);
        background-position: 50% 50%;
        background-repeat: no-repeat;
        color: transparent
    }

.compare-section {
    overflow: hidden;
    margin-top: -135px;
    padding-top: 95px;
    padding-bottom: 120px;
    border-top: 1px solid #e7e8ea;
    background-color: #f8f9fa
}

.contact {
    position: fixed;
    z-index: 100;
    display: none;
    width: 100%;
    height: 100vh;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    box-shadow: 0 0 0 30px rgba(40,43,49,.3);
    opacity: 0;
    -webkit-transform: translate(0,30px);
    -ms-transform: translate(0,30px);
    transform: translate(0,30px)
}

.dark-close-bg {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 100;
    background-color: rgba(40,43,49,.3)
}

.style-buttons {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 36px;
    padding-bottom: 90px;
    -webkit-justify-content: space-around;
    -ms-flex-pack: distribute;
    justify-content: space-around
}

.style-element-wrapper {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding: 10px 15px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.style-typography-content {
    display: block;
    margin-right: auto;
    margin-left: auto;
    padding-right: 15px;
    padding-bottom: 60px;
    padding-left: 15px;
    -webkit-box-flex: 60%;
    -webkit-flex: 60%;
    -ms-flex: 60%;
    flex: 60%
}

.form-success {
    padding-left: 0;
    background-color: transparent;
    font-size: 14px;
    text-align: left
}

    .form-success.center {
        padding-right: 0;
        font-size: 16px;
        text-align: center
    }

    .form-success.trial {
        padding-top: 10px;
        padding-right: 15px;
        padding-left: 15px;
        text-align: center
    }

.form-error {
    margin-top: 20px;
    margin-bottom: 20px;
    padding-top: 5px;
    padding-bottom: 5px;
    border-style: solid;
    border-width: 1px;
    border-color: hsla(0,0%,100%,.3);
    border-radius: 6px;
    background-color: transparent;
    color: #fff;
    font-size: 12px;
    line-height: 20px
}

    .form-error.notify-form-v2 {
        margin-top: 20px
    }

    .form-error.red {
        border-color: #ff4876;
        background-color: rgba(255,72,118,.1);
        color: #ff4876
    }

    .form-error.footer {
        margin-top: -16px
    }

.style-lists {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    padding-top: 36px
}

.checklist {
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: left
}

.checklist-item {
    position: relative;
    margin-left: 20px;
    padding-left: 31px
}

.checklist-icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 16px;
    margin-top: 9px
}

.style-typography-heading {
    width: 25%;
    padding-top: 6px;
    padding-right: 15px;
    padding-left: 15px
}

.style-typography {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: -60px;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.utility-page-wrap {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 100vw;
    height: 100vh;
    max-height: 100%;
    max-width: 100%;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    background-image: url(../images/bg-gradient-1.svg),-webkit-linear-gradient(315deg,#7956ec -2%,#2fb9f8 99%,#fff);
    background-image: url(../images/bg-gradient-1.svg),linear-gradient(135deg,#7956ec -2%,#2fb9f8 99%,#fff);
    background-position: 50% 50%,0 0;
    background-size: cover,auto;
    background-repeat: no-repeat,repeat;
    color: #fff
}

.utility-page-content {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    width: 300px;
    padding-right: 15px;
    padding-left: 15px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    text-align: center
}

.utility-page-form {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: stretch;
    -webkit-align-items: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
}

.table-column {
    padding-right: 0;
    padding-left: 0;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.preloader {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: none;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-direction: row;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    background-image: -webkit-linear-gradient(315deg,#7956ec,#2fb9f8);
    background-image: linear-gradient(135deg,#7956ec,#2fb9f8)
}

.preloader-content {
    padding: 40px;
    color: #676b75;
    text-align: center
}

.preloader-icon {
    width: 32px;
    margin-bottom: 8px
}

html.w-mod-js *[data-ix="preloader"] {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

html.w-mod-js *[data-ix="fade-up-on-load"] {
    opacity: 0;
    -webkit-transform: translate(0,30px);
    -ms-transform: translate(0,30px);
    transform: translate(0,30px)
}

html.w-mod-js *[data-ix="fade-up-1"] {
    opacity: 0;
    -webkit-transform: translate(0,30px);
    -ms-transform: translate(0,30px);
    transform: translate(0,30px)
}

html.w-mod-js *[data-ix="fade-up-2"] {
    opacity: 0;
    -webkit-transform: translate(0,30px);
    -ms-transform: translate(0,30px);
    transform: translate(0,30px)
}

html.w-mod-js *[data-ix="fade-up-3"] {
    opacity: 0;
    -webkit-transform: translate(0,30px);
    -ms-transform: translate(0,30px);
    transform: translate(0,30px)
}

html.w-mod-js *[data-ix="fade-up-4"] {
    opacity: 0;
    -webkit-transform: translate(0,30px);
    -ms-transform: translate(0,30px);
    transform: translate(0,30px)
}

html.w-mod-js *[data-ix="fade-left"] {
    opacity: 0;
    -webkit-transform: translate(30px,0);
    -ms-transform: translate(30px,0);
    transform: translate(30px,0)
}

html.w-mod-js *[data-ix="fade-right"] {
    opacity: 0;
    -webkit-transform: translate(-30px,0);
    -ms-transform: translate(-30px,0);
    transform: translate(-30px,0)
}

html.w-mod-js *[data-ix="hide-compare-section"] {
    display: none
}

@media (max-width:991px) {
    .hero-section {
        height: 600px
    }

        .hero-section.video {
            height: 510px
        }

        .hero-section.desktop {
            height: 510px;
            margin-bottom: 0
        }

        .hero-section.web {
            height: auto;
            margin-bottom: 350px
        }

        .hero-section.all-platforms {
            height: auto;
            margin-bottom: 180px
        }

        .hero-section.phone {
            height: 510px;
            margin-bottom: 70px
        }

        .hero-section.tablet {
            height: 510px;
            margin-bottom: 50px
        }

    .logo {
        padding-left: 20px
    }

    .nav-section {
        margin-bottom: -70px
    }

    .nav-link {
        display: block;
        padding: 12px 25px;
        background-color: #fff;
        -webkit-transition: background-color 200ms ease;
        transition: background-color 200ms ease;
        color: #282b31;
        font-size: 15px;
        line-height: 30px
    }

        .nav-link:hover {
            background-color: #e7e8ea;
            opacity: 1
        }

        .nav-link.w--current {
            color: #282b31
        }

        .nav-link.dropdown:active {
            box-shadow: inset 0 1px 0 0 hsla(0,0%,100%,.16),inset 0 -1px 0 0 hsla(0,0%,100%,.16)
        }

    .wrapper {
        padding-right: 10px;
        padding-left: 10px
    }

        .wrapper.center {
            padding-right: 10px;
            padding-left: 10px
        }

    .button {
        height: 45px;
        padding-right: 17px;
        padding-left: 17px;
        font-size: 16px;
        line-height: 24px
    }

        .button.nav-panel-button {
            height: 35px;
            padding-right: 12px;
            padding-left: 12px;
            font-size: 13px;
            line-height: 15px
        }

        .button.hero-second-button {
            margin-right: 7px;
            margin-left: 7px
        }

    .nav-panel {
        margin-right: -10px;
        margin-left: -10px;
        padding-top: 15px;
        padding-right: 10px;
        padding-bottom: 15px;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center
    }

    .nav-menu {
        position: absolute;
        left: 0;
        right: 0;
        overflow: hidden;
        width: 100%;
        box-shadow: 0 6px 12px 0 rgba(40,43,49,.08)
    }

    .grey-section {
        padding-top: 05px;
        padding-bottom: 05px
    }

        .grey-section.second-web-feature {
            padding-top: 20px;
            padding-bottom: 70px
        }

        .grey-section.first-web-feature {
            padding-top: 70px;
            padding-bottom: 50px
        }

    .hero-heading {
        margin-bottom: 26px;
        font-size: 25px;
        line-height: 35px
    }

    .hero-intro-left {
        padding-top: 40px;
        padding-right: 10px;
        padding-left: 5%
    }

        .hero-intro-left.big-device {
            padding-left: 10px
        }

    .app-icon {
        width: 70px;
        margin-bottom: 24px;
        border-radius: 12px
    }

        .app-icon.coming-soon-icon-v2 {
            margin-top: -35px
        }

    .hero {
        padding-top: 110px
    }

    .section-header {
        padding-right: 10px;
        padding-left: 10px;
        font-size: 25px;
        line-height: 35px;
        text-align: center
    }

    .feature-big-icon {
        width: 50px;
        border-radius: 10px
    }

    .feature-big {
        padding: 6px 19px 60px
    }

    .white-section {
        padding-top: 50px;
        padding-bottom: 70px
    }

        .white-section.press {
            padding-top: 35px;
            padding-bottom: 35px
        }

    .background-section {
        padding-top: 05px;
        padding-bottom: 10px
    }

        .background-section.page-top {
            padding-top: 40px;
            padding-bottom: 60px
        }

    .footer-description {
        margin-top: 7px;
        font-size: 14px
    }

    .video-tour-phone {
        padding-top: 70px;
        padding-bottom: 70px
    }

    .video-tour-text {
        font-size: 20px;
        line-height: 30px
    }

    .big-side-feature.side-feature-blocks {
        margin-bottom: -55px;
        padding-top: 20px
    }

    .big-feature-info {
        padding-right: 10px;
        padding-left: 10px
    }

        .big-feature-info.big-device {
            position: relative;
            width: 41.66666666%;
            padding-right: 10px;
            padding-left: 10px;
            -webkit-box-flex: 0;
            -webkit-flex: 0 auto;
            -ms-flex: 0 auto;
            flex: 0 auto
        }

    .feature-text-big {
        font-size: 15px;
        line-height: 25px
    }

    .feature-device {
        padding-right: 30px;
        padding-left: 30px
    }

        .feature-device.laptop {
            left: 30px;
            width: 58.33333333%;
            padding-right: 0;
            -webkit-box-flex: 0;
            -webkit-flex: 0 auto;
            -ms-flex: 0 auto;
            flex: 0 auto
        }

        .feature-device.desktop {
            width: 58.33333333%;
            margin-bottom: -50px;
            padding-left: 30px;
            -webkit-box-flex: 0;
            -webkit-flex: 0 auto;
            -ms-flex: 0 auto;
            flex: 0 auto
        }

        .feature-device.web {
            width: 58.33333333%;
            padding-right: 20px;
            padding-left: 20px;
            -webkit-box-flex: 0;
            -webkit-flex: 0 auto;
            -ms-flex: 0 auto;
            flex: 0 auto
        }

        .feature-device.tab-device {
            width: 100%;
            height: auto;
            padding-top: 15px;
            padding-right: 8%;
            padding-left: 8%
        }

    .testimonials-v1 {
        height: 380px;
        padding-top: 40px
    }

    .review-slide-v1 {
        width: 50%;
        padding-right: 10px;
        padding-left: 10px
    }

    .review-v1 {
        padding-right: 30px;
        padding-left: 30px;
        box-shadow: 0 6px 12px 0 rgba(40,43,49,.16)
    }

    .hero-device {
        padding-right: 10px;
        padding-left: 10px
    }

        .hero-device.tablet {
            padding-top: 5px
        }

        .hero-device.desktop {
            left: -30px;
            padding-top: 0
        }

    .footer-logo {
        padding-right: 10px;
        padding-left: 10px
    }

    .social-buttons {
        padding-right: 10px;
        padding-left: 10px
    }

    .store-badge {
        margin-right: 7px;
        margin-left: 7px
    }

    .feature-cards-big {
        padding-top: 36px
    }

    .feature-small-container {
        width: 50%
    }

    .feature-card-v2 {
        margin: 10px;
        padding-top: 40px;
        padding-bottom: 40px;
        -webkit-flex-basis: 40%;
        -ms-flex-preferred-size: 40%;
        flex-basis: 40%
    }

    .footer-right-links {
        padding-right: 10px;
        padding-left: 10px
    }

    .footer-store-badge {
        margin-right: 7px;
        margin-left: 7px
    }

    .hero-play-button {
        margin-top: 130px
    }

    .phone-side-features {
        width: 33.33333333%;
        padding-right: 20px;
        padding-left: 20px
    }

    .side-feature {
        padding-top: 17px;
        padding-bottom: 20px;
        text-align: center
    }

    .phone-center {
        width: 33.333333%
    }

    .side-feature-heading {
        font-size: 16px;
        line-height: 20px
    }

    .feature-big-heading {
        font-size: 20px;
        line-height: 25px
    }

    .feature-header-big {
        font-size: 25px;
        line-height: 35px
    }

    .feature-card-big {
        margin-right: 10px;
        margin-left: 10px;
        padding: 40px 25px 42px
    }

    .feature-card-icon {
        width: 50px;
        border-radius: 10px
    }

    .feature-tabs-wrapper {
        margin-top: 10px;
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }

    .feature-tabs {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        width: 100%;
        margin-bottom: -10px;
        padding-top: 40px;
        padding-right: 0;
        padding-left: 0
    }

        .feature-tabs.big {
            display: -webkit-box;
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            width: 100%;
            margin-bottom: 30px;
            padding-top: 10px;
            padding-right: 0;
            padding-left: 0
        }

    .feature-tab-v2 {
        margin-right: 10px;
        margin-left: 10px;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1;
        box-shadow: inset 0 0 0 1px #e7e8ea
    }

        .feature-tab-v2:hover {
            border-style: none
        }

        .feature-tab-v2.tab-1.w--current {
            border-style: none
        }

    .download {
        padding-top: 20px
    }

    .download-text {
        padding-right: 10px;
        padding-left: 10px;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1
    }

    .device-big-image {
        width: 600px
    }

        .device-big-image.tab-big-device {
            margin-right: 0%;
            margin-left: 0%;
            float: none
        }

    .press-container {
        margin-right: 10px;
        margin-left: 10px
    }

    .gradient-button {
        height: 45px;
        padding-right: 17px;
        padding-left: 17px;
        font-size: 16px;
        line-height: 24px
    }

        .gradient-button.hero-first-button {
            margin-right: 7px;
            margin-bottom: 20px;
            letter-spacing: .5px
        }

        .gradient-button.trial-button {
            margin-right: 7px;
            margin-left: 7px
        }

        .gradient-button.buy-button {
            margin-right: 14px
        }

    .square-features-container {
        margin-right: 10px;
        margin-left: 10px
    }

    .square-feature {
        padding-right: 30px;
        padding-left: 30px
    }

    .download-heading {
        font-size: 25px;
        line-height: 35px
    }

    .download-buttons-contaner {
        padding-right: 10px;
        padding-left: 10px;
        -webkit-box-flex: 0;
        -webkit-flex: 0 40%;
        -ms-flex: 0 40%;
        flex: 0 40%;
        text-align: right
    }

    .square-feature-image {
        height: 360px
    }

    .video-tour-tablet {
        padding-top: 40px;
        padding-bottom: 60px
    }

    .contact-window {
        margin-top: 40px;
        margin-bottom: 0;
        padding: 40px
    }

    .input {
        height: 45px;
        padding-top: 8px;
        font-size: 15px
    }

        .input.text-area {
            margin-right: 0;
            margin-left: 0;
            padding: 11px 20px
        }

        .input.contact-input {
            width: 100%;
            margin-right: 0;
            margin-bottom: 14px;
            margin-left: 0;
            -webkit-box-flex: 0;
            -webkit-flex: 0 auto;
            -ms-flex: 0 auto;
            flex: 0 auto
        }

        .input.subscribe-input {
            margin-right: 14px
        }

        .input.trial-input {
            margin-right: 7px;
            margin-left: 7px
        }

        .input.trial-white-input {
            margin-right: 7px;
            margin-left: 7px
        }

        .input.footer-subscribe {
            text-align: center
        }

        .input.trial-email-input {
            margin: 14px 7px
        }

        .input.notify-input {
            margin-right: 14px
        }

    .reviews-left-container {
        padding-top: 90px;
        padding-bottom: 100px
    }

    .reviews-right-container {
        padding: 50px 10px 80px 20px
    }

    .review-v2 {
        width: 100%
    }

    .reviews-v2-header {
        font-size: 25px;
        line-height: 35px
    }

    .pricing-v1 {
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }

    .pricing-v1-info {
        width: 100%;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        text-align: center
    }

    .pricing-v1-plans {
        width: 100%;
        padding-top: 52px;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto
    }

    .features-video {
        padding-top: 30px;
        padding-right: 10px;
        padding-left: 10px
    }

    .features-video-list {
        width: 100%
    }

    .features-video-preview {
        width: 88.88888888%;
        height: 420px;
        margin-top: 40px;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto
    }

    .feature-video-list-item {
        padding-right: 30px;
        padding-left: 30px;
        text-align: center
    }

    .hero-desktop-bottom {
        display: none
    }

    .trial-form {
        width: 100%
    }

    .trial-form-wrapper {
        width: 100%;
        padding-top: 26px;
        padding-right: 3px;
        padding-left: 3px
    }

    .question {
        padding-right: 30px
    }

    .review-v3-text {
        max-width: 66.6666666%;
        font-size: 16px;
        line-height: 30px
    }

    .review-v3-author {
        font-size: 16px
    }

    .feature-cards-v2 {
        padding-top: 30px
    }

    .testimonials-v3 {
        height: 360px;
        padding-top: 30px
    }

    .pricing-v2 {
        margin-right: -10px;
        margin-left: -10px
    }

    .pricing-v2-plan {
        margin-right: 0;
        margin-left: 0;
        padding: 30px
    }

        .pricing-v2-plan.best-plan-v2 {
            overflow: hidden
        }

    .pricing-v2-price {
        margin-top: 4px;
        font-size: 35px;
        line-height: 76px
    }

    .pricing-v2-features {
        margin-bottom: 30px
    }

    .pricing-v2-feature {
        font-size: 14px
    }

    .subscribe-container {
        max-width: 100%;
        margin-right: 10px;
        margin-left: 10px
    }

    .footer-v3-author {
        padding-right: 9px;
        padding-left: 9px
    }

    .footer-v3-nav-link {
        padding-right: 10px;
        padding-left: 10px
    }

    .hero-intro-center {
        padding-top: 0
    }

    .hero-device-center {
        margin-top: 30px;
        margin-bottom: -180px;
        padding-right: 30px;
        padding-left: 30px
    }

    .feature-big-v2 {
        padding: 60px 45px
    }

    .feature-big-heading-v2 {
        margin-top: 20px;
        font-size: 25px;
        line-height: 35px
    }

    .video-tour-web {
        max-width: 970px;
        margin-right: 10px;
        margin-left: 10px
    }

    .pricing-v3-plan {
        margin-right: 10px;
        margin-bottom: 20px;
        margin-left: 10px;
        -webkit-box-flex: 45%;
        -webkit-flex: 45%;
        -ms-flex: 45%;
        flex: 45%
    }

    .pricing-v3 {
        padding-bottom: 40px
    }

        .pricing-v3.page {
            padding-bottom: 60px
        }

    .feature-card-v3 {
        margin-right: 10px;
        margin-bottom: 20px;
        margin-left: 10px;
        padding: 15px 20px 23px 60px
    }

    .feature-card-v3-icon {
        top: 25px;
        height: 60px;
        margin-left: -75px
    }

    .feature-card-v3-heading {
        margin-bottom: 10px;
        font-size: 16px
    }

    .footer-v5 {
        margin-top: -25px;
        margin-bottom: -70px
    }

    .footer-v5-nav-list {
        width: 25%;
        padding-right: 10px;
        padding-left: 10px;
        text-align: center
    }

    .footer-newsletter {
        width: 50%;
        margin-right: 25%;
        margin-left: 25%;
        padding: 20px 10px 50px;
        -webkit-box-ordinal-group: 0;
        -webkit-order: -1;
        -ms-flex-order: -1;
        order: -1;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto;
        text-align: center
    }

    .feature-cards-v4 {
        margin-top: 5px;
        margin-right: 10px;
        margin-left: 10px
    }

    .feature-card-v4 {
        padding: 40px 25px
    }

    .feature-card-v4-heading {
        font-size: 20px;
        line-height: 25px
    }

    .platforms-bottom-wrapper {
        padding-bottom: 40px
    }

    .bg-feature-cards {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center
    }

    .bg-feature-card {
        max-width: 570px;
        margin-right: 10px;
        margin-left: 10px
    }

        .bg-feature-card.laptop {
            margin-top: 20px
        }

    .platform-card {
        margin-right: 10px;
        margin-left: 10px;
        padding-top: 40px;
        padding-bottom: 30px
    }

    .platform-card-heading {
        margin-top: 0;
        font-size: 16px
    }

    .features-tour {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center
    }

    .features-tour-info {
        width: 100%;
        padding-right: 10px;
        padding-bottom: 30px;
        padding-left: 10px;
        text-align: center
    }

    .features-tour-list {
        margin-right: 10px;
        margin-left: 10px
    }

    .features-tour-list-item {
        padding-left: 90px
    }

    .feature-tour-list-icon {
        width: 40px;
        border-radius: 8px
    }

    .feature-icon-left-block {
        margin-top: 35px;
        margin-bottom: 25px;
        margin-left: 5px;
        padding-left: 70px
    }

    .side-feature-icon {
        left: 0;
        top: 0
    }

    .trial-form-window {
        width: 80%;
        max-width: none;
        padding-right: 60px;
        padding-left: 60px
    }

    .trial-form-v2 {
        padding-top: 15px
    }

    .trial-app-icon {
        width: 70px;
        margin-top: -35px;
        margin-bottom: 15px;
        border-radius: 12px
    }

    .newsletter-checkbox {
        padding-left: 27px
    }

    .features-v2 {
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }

    .features-v2-info {
        width: 100%
    }

    .features-v2-list {
        width: 100%;
        padding-top: 20px;
        padding-right: 5px;
        padding-left: 5px;
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto
    }

    .feature-v2-item {
        margin-right: 20px;
        margin-left: 10px;
        padding-left: 70px
    }

    .coming-soon-window {
        max-width: 570px;
        padding-right: 60px;
        padding-bottom: 50px;
        padding-left: 60px
    }

    .coming-soon-text-v2 {
        font-size: 14px;
        line-height: 25px
    }

    .play-button {
        width: 60px;
        height: 60px;
        background-size: 20px 20px
    }

    .hero-phone-image {
        max-height: 480px
    }

    .store-badge-image {
        height: 45px
    }

    .dropdown-arrow {
        margin-right: 25px;
        background-image: url(../images/dropdown-arrow-icon-grey.svg);
        background-size: auto;
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    .dropdown-list.w--open {
        margin-top: 0;
        margin-left: 0;
        border-top: 1px solid #e7e8ea;
        border-bottom: 1px solid #e7e8ea;
        border-radius: 0;
        background-color: transparent;
        box-shadow: none
    }

    .dropdown-link {
        padding-left: 40px;
        background-color: #f8f9fa;
        -webkit-transition: color 200ms ease;
        transition: color 200ms ease;
        color: #282b31;
        font-size: 14px
    }

        .dropdown-link:hover {
            background-color: #e7e8ea;
            color: #282b31
        }

        .dropdown-link.w--current:hover {
            color: #282b31
        }

    .menu-button {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        height: 40px;
        margin-left: 10px;
        padding: 5px 15px 0;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        text-align: center
    }

        .menu-button.w--open {
            background-color: transparent
        }

    .hero-tablet-image {
        max-height: 450px
    }

    .feature-tab-v1 {
        margin-top: 0;
        margin-right: 10px;
        margin-left: 10px;
        padding-right: 30px;
        padding-left: 30px;
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1;
        box-shadow: inset 0 0 0 1px #e7e8ea;
        text-align: center
    }

        .feature-tab-v1.w--current {
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            -ms-flex-direction: column;
            flex-direction: column;
            border-style: none
        }

    .tab-icon {
        position: relative;
        left: 0;
        top: 0;
        display: block;
        margin-right: auto;
        margin-bottom: 15px;
        margin-left: auto
    }

    .page-header {
        font-size: 30px;
        line-height: 45px
    }

    .platform-tab-image {
        max-height: 440px
    }

    .hero-video {
        margin-top: -30px;
        padding-top: 0
    }

    .table {
        margin-top: 50px;
        margin-right: 10px;
        margin-left: 10px
    }

    .table-cell {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        padding-right: 15px;
        padding-left: 15px
    }

        .table-cell.text-left {
            -webkit-box-pack: start;
            -webkit-justify-content: flex-start;
            -ms-flex-pack: start;
            justify-content: flex-start
        }

    .compare-section {
        margin-top: -103px;
        padding-top: 70px;
        padding-bottom: 90px
    }

    .style-buttons {
        padding-top: 20px
    }

    .style-element-wrapper {
        padding-right: 10px;
        padding-left: 10px
    }

    .style-typography-content {
        padding-right: 10px;
        padding-left: 10px
    }

    .form-success.center {
        padding-right: 10px;
        padding-left: 10px;
        font-size: 15px
    }

    .form-error {
        margin-top: 14px;
        margin-bottom: 0
    }

        .form-error.notify-form-v2 {
            margin-top: 14px
        }

        .form-error.footer {
            margin-bottom: 20px
        }

    .style-typography-heading {
        width: 100%;
        padding-bottom: 20px
    }
}

@media (max-width:767px) {
    .hero-section.video {
        height: 540px
    }

    .hero-section.desktop {
        overflow: visible;
        height: auto;
        margin-bottom: 150px
    }

    .hero-section.web {
        margin-bottom: 180px
    }

    .hero-section.all-platforms {
        height: auto;
        margin-bottom: 130px
    }

    .hero-section.phone {
        height: 600px;
        margin-bottom: 260px
    }

    .hero-section.tablet {
        height: auto;
        margin-bottom: 220px
    }

    .wrapper.side-reviews {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .button.feature-video-button {
        margin-top: 40px;
        margin-bottom: -30px
    }

    .button.beta {
        position: absolute;
        top: 20px;
        right: 20px;
        margin-top: 0
    }

    .button.hero-second-button {
        margin-right: 7px;
        margin-left: 7px
    }

    .button.try-button {
        margin-right: 7px;
        margin-left: 7px
    }

    .nav-panel {
        padding-top: 10px;
        padding-bottom: 10px
    }

    .grey-section {
        padding-top: 40px;
        padding-bottom: 60px
    }

        .grey-section.second-phone-feature {
            margin-top: -40px;
            padding-bottom: 30px
        }

        .grey-section.first-web-feature {
            padding-top: 50px
        }

    .hero-intro-left {
        padding-top: 0;
        padding-left: 10px;
        text-align: center
    }

    .app-icon {
        display: block;
        margin-right: auto;
        margin-left: auto
    }

        .app-icon.middle {
            width: 60px;
            margin-bottom: 19px
        }

        .app-icon.coming-soon-icon-v2 {
            margin-bottom: 10px
        }

    .hero {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center
    }

    .section-header {
        font-size: 25px;
        line-height: 35px
    }

        .section-header.white-text {
            padding-right: 10px;
            padding-left: 10px
        }

    .feature-big {
        padding: 16px 8.33333333% 40px;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto
    }

    .white-section {
        padding-top: 40px;
        padding-bottom: 60px
    }

        .white-section.press {
            padding-top: 20px;
            padding-bottom: 20px
        }

    .press-link {
        padding-right: 20px;
        padding-left: 20px
    }

    .background-section {
        padding-top: 40px;
        padding-bottom: 60px
    }

        .background-section.page-top {
            padding-top: 10px;
            padding-bottom: 50px
        }

    .footer-v1-column {
        width: 50%;
        padding-bottom: 30px
    }

    .footer-description {
        max-width: none
    }

    .footer-bottom-v1 {
        margin-top: 40px
    }

    .big-side-feature {
        margin-bottom: -40px;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

        .big-side-feature.second {
            margin-bottom: -10px
        }

        .big-side-feature.side-feature-blocks {
            margin-bottom: -85px;
            -webkit-box-orient: vertical;
            -webkit-box-direction: reverse;
            -webkit-flex-direction: column-reverse;
            -ms-flex-direction: column-reverse;
            flex-direction: column-reverse
        }

    .big-feature-info {
        width: 100%;
        margin-right: 0%;
        margin-left: 0%;
        padding-right: 10px;
        padding-left: 10px;
        text-align: center
    }

        .big-feature-info.big-device {
            width: 100%;
            padding-bottom: 30px
        }

    .feature-text-big {
        margin-top: 0;
        font-size: 14px;
        line-height: 25px
    }

    .feature-device {
        width: 100%
    }

        .feature-device.laptop {
            position: static;
            width: 100%;
            padding-right: 10px;
            padding-left: 10px;
            -webkit-box-ordinal-group: 2;
            -webkit-order: 1;
            -ms-flex-order: 1;
            order: 1
        }

        .feature-device.desktop {
            width: 100%;
            margin-top: 0;
            margin-bottom: 0;
            padding-right: 10px;
            padding-left: 10px
        }

        .feature-device.web {
            width: 100%;
            padding-bottom: 30px
        }

            .feature-device.web.first {
                -webkit-box-ordinal-group: 0;
                -webkit-order: -1;
                -ms-flex-order: -1;
                order: -1
            }

        .feature-device.phone {
            padding: 30px 20% 50px
        }

    .testimonials-v1 {
        height: 320px;
        padding-top: 20px
    }

    .review-slide-v1 {
        width: 100%;
        padding-right: 8.33333333%;
        padding-left: 8.33333333%
    }

    .hero-device {
        width: 100%;
        padding-top: 30px
    }

        .hero-device.tablet {
            margin-bottom: -220px;
            padding-top: 30px
        }

        .hero-device.desktop {
            position: static;
            margin-bottom: -150px;
            padding-top: 30px
        }

    .footer-section-v1 {
        padding-top: 30px
    }

    .footer-v4 {
        padding-top: 30px;
        padding-bottom: 35px;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .footer-logo {
        width: 100%;
        padding-bottom: 10px;
        text-align: center
    }

    .author-copyright {
        -webkit-box-ordinal-group: 2;
        -webkit-order: 1;
        -ms-flex-order: 1;
        order: 1
    }

    .social-buttons {
        padding-top: 15px;
        padding-bottom: 15px;
        text-align: center
    }

    .store-badges {
        padding-top: 16px
    }

    .feature-cards-big {
        padding-top: 20px;
        padding-bottom: 0
    }

    .feature-small-container {
        width: 100%
    }

    .feature-card-v2 {
        width: 50%
    }

    .footer-v2 {
        padding-bottom: 30px;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-pack: start;
        -webkit-justify-content: flex-start;
        -ms-flex-pack: start;
        justify-content: flex-start;
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center
    }

    .footer-right-links {
        width: auto;
        text-align: center
    }

    .footer-link-v2 {
        display: inline-block;
        padding: 10px;
        float: none
    }

    .hero-play-button {
        margin-top: 20px
    }

    .phone-center-features {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .phone-side-features {
        width: 100%;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -webkit-flex-direction: row;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .side-feature {
        padding-right: 15px;
        padding-left: 15px
    }

    .phone-center {
        width: 100%;
        padding-top: 15px;
        padding-bottom: 20px
    }

    .footer-store-badges {
        width: 100%;
        padding-top: 10px;
        padding-bottom: 10px;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        -webkit-box-ordinal-group: 0;
        -webkit-order: -1;
        -ms-flex-order: -1;
        order: -1
    }

    .feature-card-big {
        width: 80%;
        margin-top: 20px;
        padding-top: 30px;
        padding-bottom: 32px;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto
    }

    .feature-card-icon {
        margin-bottom: 11px
    }

    .feature-tabs-wrapper {
        margin-top: 0
    }

    .feature-tabs {
        padding-top: 30px
    }

        .feature-tabs.big {
            padding-right: 5px;
            padding-left: 5px
        }

    .feature-tab-v2 {
        padding-bottom: 21px;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        text-align: center
    }

    .download {
        margin-top: -20px;
        margin-bottom: -10px;
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        text-align: center
    }

    .download-text {
        padding-right: 50px;
        padding-left: 50px
    }

    .device-big-image {
        width: 100%
    }

    .press-container {
        margin-top: 46px
    }

    .gradient-button.hero-first-button {
        margin-left: 7px
    }

    .gradient-button.buy-button {
        margin-right: 7px;
        margin-left: 7px
    }

    .square-features-container {
        margin-top: 10px
    }

    .square-feature {
        width: 100%;
        padding: 35px 45px 45px
    }

        .square-feature.second-feature {
            -webkit-box-ordinal-group: 2;
            -webkit-order: 1;
            -ms-flex-order: 1;
            order: 1
        }

    .download-buttons-contaner {
        width: 100%;
        padding-top: 0;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto;
        text-align: center
    }

    .square-feature-image {
        width: 100%;
        height: 520px
    }

    .video-tour-tablet {
        padding: 30px 10px 60px
    }

    .section-description {
        padding-right: 10px;
        padding-left: 10px;
        font-size: 14px;
        line-height: 25px
    }

        .section-description.white-transparent-text {
            padding-right: 10px;
            padding-left: 10px
        }

    .contact-window {
        width: auto;
        margin-top: 40px;
        margin-right: 10px;
        margin-left: 10px
    }

    .input {
        padding-top: 9px;
        font-size: 16px
    }

        .input.trial-white-input {
            margin-right: 7px;
            margin-left: 7px
        }

    .reviews-left-container {
        width: 100%;
        padding: 60px 10px 30px;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center;
        border-right-style: none;
        background-color: transparent
    }

    .stars-left {
        margin-bottom: 14px
    }

    .reviews-right-container {
        width: 100%;
        padding-top: 10px;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto
    }

    .reviews-v2-header {
        text-align: center
    }

    .reviews-v2-more-text {
        margin-top: 10px;
        margin-bottom: 10px;
        font-size: 14px
    }

    .reviews-v2-store-link {
        -webkit-flex-wrap: nowrap;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
        font-size: 15px
    }

    .arrow-icon {
        display: none
    }

    .pricing-v1-plans {
        padding-right: 10px;
        padding-left: 10px
    }

    .pricing-v1-plan {
        overflow: hidden;
        padding-top: 40px;
        padding-right: 40px;
        padding-left: 40px
    }

    .pricing-v1-plan-name {
        margin-right: -40px;
        margin-left: -40px
    }

    .pricing-v1-free-plan-name {
        margin-right: -40px;
        margin-left: -40px;
        padding-right: 40px;
        padding-left: 40px
    }

    .pricing-v1-free-plan {
        overflow: hidden;
        padding-right: 40px;
        padding-bottom: 40px;
        padding-left: 40px
    }

    .features-video {
        padding-top: 20px
    }

    .features-video-preview {
        width: 100%;
        height: 340px;
        margin-top: 20px
    }

    .hero-desktop-bottom {
        display: none
    }

    .device-big-center {
        margin-top: 37px;
        margin-bottom: -120px
    }

    .trial-form {
        margin-bottom: 0
    }

    .trial-form-wrapper {
        padding-top: 16px
    }

    .questions-list {
        padding-top: 20px;
        -webkit-column-count: 1;
        column-count: 1
    }

    .question {
        padding-right: 10px;
        padding-left: 10px
    }

    .question-text {
        margin-top: 10px
    }

    .reviews-arrow {
        margin-top: 134px;
        margin-left: 10px
    }

        .reviews-arrow.right {
            margin-right: 10px
        }

    .review-v3-avatar {
        margin-bottom: 27px
    }

    .review-v3 {
        margin-right: -10px;
        margin-left: -10px
    }

    .review-v3-author {
        margin-top: 22px
    }

    .feature-cards-v2 {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex
    }

    .testimonials-v3 {
        height: 380px;
        padding-top: 20px
    }

    .pricing-v2 {
        padding-top: 0;
        padding-bottom: 0;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-align-content: center;
        -ms-flex-line-pack: center;
        align-content: center
    }

    .pricing-v2-plan {
        width: 66.66666667%
    }

        .pricing-v2-plan.best-plan-v2 {
            margin-top: 20px;
            margin-bottom: 20px
        }

    .subscribe-container {
        margin-top: 46px;
        padding-right: 50px;
        padding-left: 50px
    }

    .footer-v3 {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: stretch;
        -webkit-align-items: stretch;
        -ms-flex-align: stretch;
        align-items: stretch
    }

    .footer-v3-author {
        padding-top: 3px;
        text-align: center
    }

    .footer-v3-nav {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1
    }

    .hero-device-center {
        margin-bottom: -130px;
        padding-right: 20px;
        padding-left: 20px
    }

    .feature-big-v2 {
        width: 100%;
        padding-top: 50px;
        padding-bottom: 50px
    }

        .feature-big-v2.left-border {
            padding-top: 0
        }

    .feature-big-heading-v2 {
        font-size: 20px;
        line-height: 30px
    }

    .video-tour-web {
        height: 320px;
        margin-top: 35px;
        margin-bottom: 30px
    }

    .pricing-v3-feature {
        padding-top: 10px;
        font-size: 14px
    }

    .pricing-v3 {
        padding-top: 30px;
        padding-bottom: 30px
    }

        .pricing-v3.page {
            padding-bottom: 40px
        }

    .ceature-cards-v3 {
        padding-top: 20px
    }

    .feature-card-v3 {
        margin-top: 10px;
        margin-bottom: 10px;
        -webkit-flex-basis: 90%;
        -ms-flex-preferred-size: 90%;
        flex-basis: 90%
    }

    .footer-v5 {
        margin-bottom: -60px
    }

    .footer-v5-header {
        margin-bottom: 10px;
        font-size: 13px
    }

    .footer-v5-nav-list {
        text-align: left
    }

    .footer-v5-nav-link {
        font-size: 12px
    }

    .footer-newsletter {
        width: 100%;
        margin-right: 0;
        margin-left: 0;
        padding: 30px 80px 40px
    }

    .feature-cards-v4 {
        margin-top: 40px;
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }

    .feature-card-v4 {
        width: 100%
    }

        .feature-card-v4.middle-card {
            border-style: solid none;
            border-top-width: 1px;
            border-top-color: #e7e8ea;
            border-bottom-width: 1px;
            border-bottom-color: #e7e8ea
        }

    .platform-tabs {
        margin-top: 15px
    }

    .platform-tab-content {
        padding-top: 30px;
        padding-bottom: 21px
    }

    .platforms-bottom-wrapper {
        margin-top: -109px;
        margin-bottom: -80px;
        padding-top: 140px
    }

    .bg-feature-cards {
        padding-top: 20px
    }

    .bg-feature-card {
        padding-left: 30px
    }

    .testimonials-v4 {
        margin-bottom: 0;
        padding-top: 25px
    }

    .review-slide-v4 {
        width: 100%
    }

    .testimonials {
        margin-bottom: 20px
    }

    .platform-cards {
        padding-top: 35px
    }

    .platform-card {
        margin-bottom: 20px;
        -webkit-flex-basis: 40%;
        -ms-flex-preferred-size: 40%;
        flex-basis: 40%
    }

    .features-tour {
        padding-top: 10px
    }

    .features-tour-info {
        padding-bottom: 20px
    }

    .features-tour-list-item {
        padding: 15px 20px 20px 80px
    }

    .feature-tour-list-icon {
        margin-top: 25px;
        margin-left: 20px
    }

    .feature-icon-left-block {
        margin-top: 30px;
        margin-bottom: 20px;
        margin-left: 10px;
        padding-right: 29px
    }

    .trial-form-window {
        width: auto;
        max-width: 100%;
        margin-top: 60px;
        margin-right: 10px;
        margin-left: 10px;
        padding-right: 50px;
        padding-left: 50px
    }

    .features-v2-list {
        padding-left: 0
    }

    .feature-v2-item {
        -webkit-flex-basis: 80%;
        -ms-flex-preferred-size: 80%;
        flex-basis: 80%
    }

    .background-full {
        position: relative;
        left: 0;
        right: 0;
        bottom: 0;
        min-height: 500px
    }

    .coming-soon-text-v1 {
        font-size: 14px
    }

    .notify-form {
        width: auto
    }

    .coming-soon-social-v1 {
        position: absolute;
        left: 20px;
        top: 20px
    }

    .coming-soon-copyright-text {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0
    }

    .coming-soon-window {
        padding-right: 40px;
        padding-bottom: 40px;
        padding-left: 40px
    }

    .feature-tab-v1 {
        margin-right: 5px;
        margin-bottom: 0;
        margin-left: 5px;
        padding-right: 20px;
        padding-bottom: 16px;
        padding-left: 20px
    }

    .press-logo-image {
        height: 18px
    }

    .platform-tab-image {
        max-height: 320px
    }

    .text-block-2 {
        font-size: 14px
    }

    .table {
        margin-right: -11px;
        margin-left: -11px;
        border-radius: 0
    }

    .table-cell {
        font-size: 13px
    }

    .compare-section {
        margin-top: -83px;
        padding-top: 65px;
        padding-bottom: 70px
    }

    .style-buttons {
        padding-bottom: 60px;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .form-success.center {
        font-size: 14px
    }

    .style-lists {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-flex-wrap: wrap;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap
    }
}

@media (max-width:479px) {
    .hero-section.video {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        height: 100vh;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center
    }

    .hero-section.desktop {
        margin-bottom: 60px
    }

    .hero-section.web {
        margin-bottom: 120px
    }

    .hero-section.all-platforms {
        margin-bottom: 70px
    }

    .hero-section.phone {
        height: auto;
        margin-bottom: 257px
    }

    .button.hero-second-button {
        display: block;
        margin-top: 0;
        margin-right: 10%;
        margin-left: 10%
    }

    .button.try-button {
        display: block;
        margin-right: 0;
        margin-left: 0
    }

    .hero-intro-left {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .app-icon {
        margin-bottom: 0
    }

        .app-icon.coming-soon-icon-v1 {
            margin-bottom: 9px
        }

    .hero {
        padding-top: 100px;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center
    }

    .press-link {
        padding: 10px
    }

    .background-section {
        padding-top: 40px;
        padding-bottom: 60px
    }

        .background-section.page-top {
            padding-bottom: 40px
        }

    .footer-description {
        margin-bottom: 17px
    }

    .footer-bottom-v1 {
        margin-top: 20px
    }

    .video-tour-section.phone {
        background-size: auto,cover
    }

    .video-tour-phone {
        padding-top: 40px;
        padding-bottom: 40px;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .big-feature-info {
        margin-right: 0%;
        margin-left: 0%
    }

    .feature-device.web {
        padding-right: 10px;
        padding-bottom: 20px;
        padding-left: 10px
    }

    .feature-device.tab-device {
        padding-top: 10px;
        padding-right: 4%;
        padding-left: 4%
    }

    .feature-device.phone {
        padding-right: 10px;
        padding-left: 10px
    }

    .testimonials-v1 {
        height: 340px
    }

    .review-slide-v1 {
        padding-right: 10px;
        padding-left: 10px
    }

    .hero-device {
        padding-top: 40px
    }

        .hero-device.phone {
            margin-bottom: -257px;
            -webkit-box-pack: center;
            -webkit-justify-content: center;
            -ms-flex-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            -webkit-align-items: center;
            -ms-flex-align: center;
            align-items: center
        }

        .hero-device.tablet {
            padding-top: 40px;
            padding-right: 4%;
            padding-left: 4%
        }

        .hero-device.desktop {
            margin-bottom: -60px;
            padding: 40px 5px 0
        }

    .footer-logo {
        padding-right: 10%;
        padding-left: 10%
    }

    .store-badge {
        margin-top: 7px;
        margin-bottom: 7px
    }

    .store-badges {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -webkit-align-items: center;
        -ms-flex-align: center;
        align-items: center
    }

    .feature-small-container {
        padding-top: 10px;
        padding-bottom: 10px
    }

    .feature-card-v2 {
        width: 100%;
        -webkit-flex-basis: 80%;
        -ms-flex-preferred-size: 80%;
        flex-basis: 80%
    }

    .footer-right-links {
        padding-top: 20px
    }

    .footer-link-v2 {
        display: block;
        margin-top: 0
    }

    .footer-store-badge {
        margin: 7px 5px
    }

    .phone-center-features {
        padding-top: 10px
    }

    .phone-side-features {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .side-feature {
        padding-right: 10px;
        padding-left: 10px
    }

    .phone-center {
        padding-top: 10px;
        padding-right: 10%;
        padding-left: 10%
    }

    .footer-store-badges {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .feature-card-big {
        width: 100%
    }

    .feature-tabs {
        padding-top: 20px;
        padding-right: 5px;
        padding-left: 5px
    }

        .feature-tabs.big {
            padding-right: 0;
            padding-left: 0
        }

    .feature-tab-v2 {
        margin-right: 5px;
        margin-bottom: 0;
        margin-left: 5px;
        padding: 17px 15px 7px
    }

    .feature-tab-heading {
        font-size: 13px
    }

    .download-text {
        padding-right: 10px;
        padding-left: 10px
    }

    .press-color-link {
        width: 100%;
        padding-top: 30px;
        padding-bottom: 30px
    }

    .gradient-button.hero-first-button {
        display: block;
        margin-right: 10%;
        margin-bottom: 14px;
        margin-left: 10%
    }

    .gradient-button.trial-button {
        margin-right: 0;
        margin-left: 0
    }

    .gradient-button.buy-button {
        display: block;
        margin-right: 0;
        margin-bottom: 14px;
        margin-left: 0
    }

    .gradient-button.contact-button {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1
    }

    .square-features-container {
        margin-bottom: 0
    }

    .square-feature {
        padding-right: 25px;
        padding-left: 25px
    }

    .square-feature-image {
        height: 300px
    }

    .video-tour-tablet {
        padding-bottom: 50px
    }

    .section-description {
        line-height: 25px
    }

    .contact-window {
        padding: 30px 20px
    }

    .input {
        margin-bottom: 14px
    }

        .input.text-area {
            margin-bottom: 14px
        }

        .input.subscribe-input {
            margin-bottom: 14px
        }

        .input.trial-input {
            margin-right: 0;
            margin-bottom: 14px;
            margin-left: 0
        }

        .input.trial-white-input {
            margin-right: 0;
            margin-bottom: 14px;
            margin-left: 0
        }

        .input.trial-email-input {
            margin-top: 0;
            margin-right: 0;
            margin-left: 0
        }

        .input.notify-input {
            margin-bottom: 14px
        }

    .contact-form {
        margin-bottom: 14px
    }

    .reviews-right-container {
        padding-right: 10px;
        padding-left: 10px
    }

    .review-v2 {
        padding-right: 10px;
        padding-left: 10px;
        -webkit-box-pack: center;
        -webkit-justify-content: center;
        -ms-flex-pack: center;
        justify-content: center
    }

        .review-v2.review-reversed {
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            -webkit-flex-direction: row;
            -ms-flex-direction: row;
            flex-direction: row
        }

    .review-v2-text {
        width: 100%;
        margin-top: 15px;
        -webkit-box-flex: 0;
        -webkit-flex: 0 auto;
        -ms-flex: 0 auto;
        flex: 0 auto;
        text-align: center
    }

    .review-v2-author-info.author-info-reversed {
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -webkit-flex-direction: row;
        -ms-flex-direction: row;
        flex-direction: row
    }

    .pricing-v1-plans {
        padding-top: 42px;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .pricing-v1-plan {
        width: 100%;
        margin-bottom: 20px;
        padding-bottom: 40px
    }

    .pricing-v1-free-plan {
        width: 100%;
        margin-top: 0;
        margin-bottom: 0;
        margin-left: 0;
        padding-top: 40px
    }

    .features-video-preview {
        height: 180px
    }

    .feature-video-list-item {
        width: 100%;
        padding-right: 0;
        padding-left: 0
    }

    .device-big-center {
        margin-bottom: -100px
    }

    .trial-form {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .trial-form-wrapper {
        padding-right: 10px;
        padding-left: 10px
    }

    .trial-text {
        padding-right: 10px;
        padding-left: 10px
    }

    .question {
        padding-top: 10px
    }

    .question-text {
        margin-top: 2px
    }

    .reviews-arrow {
        margin-top: 30px
    }

    .review-v3-text {
        max-width: 80%;
        padding-right: 10px;
        padding-left: 10px
    }

    .testimonials-v3 {
        height: 410px
    }

    .pricing-v2 {
        margin-right: 0;
        margin-bottom: -30px;
        margin-left: 0;
        padding-right: 10px;
        padding-left: 10px
    }

    .pricing-v2-plan {
        width: 100%
    }

    .subscribe-container {
        margin-top: 26px;
        margin-bottom: 0;
        padding: 30px 20px
    }

    .subscribe-form {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: stretch;
        -webkit-align-items: stretch;
        -ms-flex-align: stretch;
        align-items: stretch
    }

    .footer-v3-nav {
        -webkit-box-flex: 1;
        -webkit-flex: 1;
        -ms-flex: 1;
        flex: 1
    }

    .hero-device-center {
        margin-bottom: -70px;
        padding-right: 10px;
        padding-left: 10px
    }

    .feature-big-v2 {
        padding-right: 10px;
        padding-left: 10px
    }

        .feature-big-v2.left-border {
            border-left-style: none
        }

    .video-tour-web {
        height: 180px
    }

    .footer-v5-nav-list {
        width: 50%;
        padding-top: 10px;
        padding-bottom: 10px
    }

    .footer-newsletter {
        width: 100%;
        margin-right: 0;
        margin-left: 0;
        padding-right: 10px;
        padding-left: 10px
    }

    .platform-tab-content {
        height: 220px
    }

    .platforms-bottom-wrapper {
        margin-top: -80px;
        margin-bottom: -90px;
        padding-top: 121px
    }

    .bg-feature-card {
        min-height: 320px;
        padding-top: 20px;
        padding-right: 20px;
        padding-left: 20px;
        -webkit-box-pack: start;
        -webkit-justify-content: flex-start;
        -ms-flex-pack: start;
        justify-content: flex-start;
        -webkit-flex-wrap: nowrap;
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
        background-position: 50% 160px,50% 0%;
        background-size: 140px,cover;
        text-align: center
    }

        .bg-feature-card.laptop {
            background-position: 50% 159px,50% 0;
            background-size: 230px,cover
        }

    .available-text {
        margin-top: 33px
    }

    .testimonials-v4 {
        height: 330px;
        margin-right: 0;
        margin-left: 0;
        padding-top: 10px
    }

    .review-slide-v4 {
        padding-right: 10px;
        padding-left: 10px
    }

    .testimonials {
        margin-bottom: 40px
    }

    .review-v4-avatar {
        width: 40px;
        margin-top: 5px
    }

    .review-v4-author-name {
        margin-left: 50px;
        font-size: 14px
    }

    .review-v4-author-text {
        margin-left: 50px
    }

    .review-v4-social-icon {
        margin-top: 8px;
        margin-right: 0
    }

    .platform-cards {
        margin-bottom: -10px;
        padding: 20px 5px 0
    }

    .platform-card {
        margin-right: 6px;
        margin-bottom: 10px;
        margin-left: 6px;
        padding: 30px 20px 15px;
        -webkit-flex-basis: 40%;
        -ms-flex-preferred-size: 40%;
        flex-basis: 40%
    }

    .platform-card-heading {
        font-size: 14px
    }

    .features-tour {
        padding-bottom: 0
    }

    .features-tour-list-item {
        width: 100%;
        padding-left: 75px
    }

    .features-video-tour {
        font-size: 14px
    }

    .feature-icon-left-block {
        margin-bottom: 0;
        margin-left: 0;
        padding-right: 0;
        padding-left: 60px
    }

    .side-feature-icon {
        width: 40px;
        border-radius: 8px
    }

    .trial-form-window {
        padding-right: 20px;
        padding-bottom: 30px;
        padding-left: 20px
    }

    .trial-form-v2 {
        margin-right: 0;
        margin-left: 0;
        padding-bottom: 0;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .newsletter-checkbox {
        padding-left: 20px
    }

    .features-v2-list {
        padding-top: 10px;
        padding-right: 10px;
        padding-left: 10px
    }

    .feature-v2-item {
        margin-right: 0;
        margin-bottom: 15px;
        margin-left: 0;
        padding-left: 60px
    }

    .background-full {
        min-height: 600px
    }

    .coming-soon-text-v1 {
        font-size: 14px;
        line-height: 25px
    }

    .notify-form {
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -ms-flex-direction: column;
        flex-direction: column
    }

    .coming-soon-copyright-text {
        left: 0;
        right: 0;
        bottom: 0
    }

    .coming-soon-window {
        margin-right: 10px;
        margin-left: 10px;
        padding-right: 20px;
        padding-left: 20px
    }

    .coming-soon-social-v2 {
        margin-top: 14px
    }

    .play-button {
        margin-top: 20px;
        margin-bottom: 20px
    }

        .play-button.small {
            width: 40px;
            height: 40px;
            margin-right: 15px;
            margin-left: 15px;
            background-size: 12px 12px
        }

    .feature-tab-v1 {
        padding-right: 10px;
        padding-left: 10px
    }

    .press-logo-image {
        height: 16px
    }

    .platform-tab-image {
        max-height: 180px
    }

    .table {
        margin-top: 40px
    }

    .table-cell {
        padding: 12px 8%;
        font-size: 11px;
        line-height: 14px
    }

    .tabs-content {
        overflow: visible
    }

    .form-error.red {
        margin-top: 14px
    }
}

@font-face {
    font-family: 'Avenirltstd';
    src: url(../fonts/AvenirLTStd.ttf) format('truetype');
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: 'Avenir lt std 45 book 5a0009e8865ca';
    src: url(../fonts/avenir-lt-std-45-book-5a0009e8865ca.otf) format('opentype');
    font-weight: 400;
    font-style: normal
}

@font-face {
    font-family: 'Avenirltstd book';
    src: url(../fonts/AvenirLTStd-Book.otf) format('opentype');
    font-weight: 400;
    font-style: normal
}
