{"AzureKeyVaultConnectionStrings": {"WEAdminConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-ad;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WEDataConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-dt;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WEOrgConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-or;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WEPeopleConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-pp;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "WETokenConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-tk;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}, "ConnectionStrings": {"WEDataConnectionString": "Data Source=eu-ne-sd.database.windows.net;Initial Catalog=eu-fc-ss-dt;Persist Security Info=True;User ID=eu-ne-sd;Password=**************;MultipleActiveResultSets=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"}, "BaseUrl": "https://onepagewebapp-e5c8e9dugmamf3gq.northeurope-01.azurewebsites.net", "ServerSettings": {"Url": "https://eu-fc-ap-sz.azurewebsites.net/", "ServerType": 17}, "WebproxyUrl": "https://onepagewebproxy-dtd4bhb4fhhkcsdb.northeurope-01.azurewebsites.net", "MsMailIdentifier": "C7B2B24E-E8B3-4E23-B209-33C34E322512", "GoogleWebhook": "https://92252b03.1pageplus.com/88522BC8", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}}}