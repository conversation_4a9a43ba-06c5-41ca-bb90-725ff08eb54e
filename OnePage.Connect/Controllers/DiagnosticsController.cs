using Microsoft.AspNetCore.Mvc;
using OnePage.Connect.Context;
using System.Reflection;
using System.Text;

namespace OnePage.Connect.Controllers
{
    public class DiagnosticsController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _environment;
        private readonly WEDataEntitiesDBContext _dataContext;

        public DiagnosticsController(
            IConfiguration configuration, 
            IWebHostEnvironment environment,
            WEDataEntitiesDBContext dataContext)
        {
            _configuration = configuration;
            _environment = environment;
            _dataContext = dataContext;
        }

        public async Task<IActionResult> Index()
        {
            var diagnostics = new StringBuilder();
            
            try
            {
                diagnostics.AppendLine("=== ONEPAGE CONNECT DIAGNOSTICS ===");
                diagnostics.AppendLine($"Timestamp: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
                diagnostics.AppendLine($"Environment: {_environment.EnvironmentName}");
                diagnostics.AppendLine($"Content Root: {_environment.ContentRootPath}");
                diagnostics.AppendLine($"Web Root: {_environment.WebRootPath}");
                diagnostics.AppendLine();

                // Assembly information
                var assembly = Assembly.GetExecutingAssembly();
                diagnostics.AppendLine($"Assembly: {assembly.GetName().Name}");
                diagnostics.AppendLine($"Version: {assembly.GetName().Version}");
                diagnostics.AppendLine($"Runtime Version: {Environment.Version}");
                diagnostics.AppendLine();

                // Configuration check
                diagnostics.AppendLine("=== CONFIGURATION ===");
                var baseUrl = _configuration["BaseUrl"];
                diagnostics.AppendLine($"BaseUrl: {baseUrl ?? "NOT SET"}");
                
                var serverUrl = _configuration["ServerSettings:Url"];
                diagnostics.AppendLine($"Server URL: {serverUrl ?? "NOT SET"}");
                diagnostics.AppendLine();

                // Database connection test
                diagnostics.AppendLine("=== DATABASE CONNECTIONS ===");
                try
                {
                    var canConnect = await _dataContext.Database.CanConnectAsync();
                    diagnostics.AppendLine($"WEDataEntitiesDBContext: {(canConnect ? "✓ Connected" : "✗ Failed")}");
                }
                catch (Exception ex)
                {
                    diagnostics.AppendLine($"WEDataEntitiesDBContext: ✗ Error - {ex.Message}");
                }
                diagnostics.AppendLine();

                // Environment variables
                diagnostics.AppendLine("=== ENVIRONMENT VARIABLES ===");
                var envVars = new[] { 
                    "ASPNETCORE_ENVIRONMENT", 
                    "ASPNETCORE_DETAILEDERRORS",
                    "WEBSITE_SITE_NAME",
                    "WEBSITE_RESOURCE_GROUP"
                };
                
                foreach (var envVar in envVars)
                {
                    var value = Environment.GetEnvironmentVariable(envVar);
                    diagnostics.AppendLine($"{envVar}: {value ?? "NOT SET"}");
                }
                diagnostics.AppendLine();

                diagnostics.AppendLine("=== STATUS ===");
                diagnostics.AppendLine("Application appears to be running successfully!");

            }
            catch (Exception ex)
            {
                diagnostics.AppendLine($"=== ERROR IN DIAGNOSTICS ===");
                diagnostics.AppendLine($"Error: {ex.Message}");
                diagnostics.AppendLine($"Stack Trace: {ex.StackTrace}");
            }

            ViewBag.Diagnostics = diagnostics.ToString();
            return View();
        }
    }
}
