// using System.Diagnostics;
// using Microsoft.AspNetCore.Mvc;
// using OnePage.Connect.Models;
//
// namespace OnePage.Connect.Controllers;
//
// public class HomeController : Controller
// {
//     private readonly ILogger<HomeController> _logger;
//
//     public HomeController(ILogger<HomeController> logger)
//     {
//         _logger = logger;
//     }
//
//     public IActionResult Index()
//     {
//         return View();
//     }
//
//     public IActionResult Privacy()
//     {
//         return View();
//     }
//
//     [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
//     public IActionResult Error()
//     {
//         return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
//     }
// }

using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Microsoft.AspNetCore.Mvc;
using OnePage.Common;
using OnePage.Connect.Context;
using OnePage.Connect.Migrations;
using OnePage.Connect.Services;
using OnePage.Services;


namespace OnePage.Connect.Controllers;

public class HomeController : Controller
{
    //private WETokenEntities wetdb = new WETokenEntities();
    private readonly WEOrgEntitiesDBContext _weodb;

    private readonly WEPeopleEntitiesDBContext _wepdb;

    //private WESyncEntities wesdb = new WESyncEntities();
    private readonly WEDataEntitiesDBContext _weddb;
    private readonly WEAdminEntitiesDBContext _weadb;


    public HomeController(
        WEOrgEntitiesDBContext weOrgEntitiesDbContext, 
        WEPeopleEntitiesDBContext wePeopleEntitiesDbContext, WEAdminEntitiesDBContext weAdminEntitiesDbContext,
        WEDataEntitiesDBContext weDataEntitiesDbContext)
    {
        _weddb = weDataEntitiesDbContext;
        _weadb = weAdminEntitiesDbContext;
        _weodb = weOrgEntitiesDbContext;
        _wepdb = wePeopleEntitiesDbContext;
        
    }

    public ActionResult Index(string Id, string s, string provisionId, string providerId)
    {
        try
        {
            //var providerIdExist =
            //    _weddb.Providers.FirstOrDefault(w => w.Id.ToString().ToLower() == providerId.ToLower());
            //if (providerIdExist == null)
            //{
            //    var res = EmailServices.SendGridTestMail("provisionId: " + provisionId, "", "<EMAIL>");
            //    return View();
            //}

            //Guid userEmailId = Guid.Parse(Id);
            //var userEmail = _wepdb.UserEmails.FirstOrDefault(w => w.Id == userEmailId);
            //var user = _wepdb.Users.FirstOrDefault(w => w.Id == userEmail.UserId);
            //Guid userId = user.Id;

            //var appLanguage =
            //    _weadb.AppLanguages.FirstOrDefault(w => w.AppId == user.AppId && w.LanguageId == user.LanguageId);
            //var appSendgridTemplateId = "";
            //if (appLanguage == null)
            //{
            //    appLanguage =
            //        _weadb.AppLanguages.FirstOrDefault(w => w.AppId == CommonData.WEAppId && w.LanguageId == 1);
            //    appSendgridTemplateId = _weadb.AppLanguageTemplates.FirstOrDefault(w =>
            //        w.AppLanguageId == appLanguage.Id && w.TemplateName == "EmailSuccessfullyVerified").TemplateId;
            //}
            //else
            //{
            //    appSendgridTemplateId = _weadb.AppLanguageTemplates.FirstOrDefault(w =>
            //        w.AppLanguageId == appLanguage.Id && w.TemplateName == "EmailSuccessfullyVerified").TemplateId;
            //}

            //switch (s)
            //{
            //    case "e":
            //        Guid orgID = CommonData.WhatElseCustomerOrgId;
            //        bool isFree = false;
            //        bool isProvisioned = false;
            //        bool isTrail = true;
            //        bool isAccepted = false;
            //        bool isClaimed = true;
            //        Guid provisionID = Guid.Parse(provisionId);
            //        Guid providerID = Guid.Parse(providerId);
            //        userEmail.IsEmailValidated = true;
            //        _wepdb.SaveChanges();
            //        var provisionExist = _weodb.Provisions.Any(w =>
            //            w.UserId == user.Id && w.EmailAddress == userEmail.Email && w.ProviderId == providerID &&
            //            w.IsActive);
            //        if (provisionID == Guid.Empty && !provisionExist)
            //        {
            //            var userDomain = userEmail.Email.Split('@');
            //            var domainName = userDomain[1];
            //            if (_weddb.Domains.Any(w => w.Address == domainName && w.IsActive == true && w.IsFree == true) ==
            //                true)
            //            {
            //                isFree = true;
            //                isProvisioned = true;
            //                isTrail = false;
            //                isAccepted = true;
            //                isClaimed = false;
            //            }

            //            var weOrgProvider = _weodb.OrgProviders.FirstOrDefault(w =>
            //                w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == providerID && w.IsActive);
            //            //var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive).ToList();

            //            bool isFreeDomain = _weddb.FreeDomains.Any(w => w.DomainName.ToLower() == domainName.ToLower());
            //            if (isFreeDomain == false)
            //            {
            //                var orgExist = _weodb.Orgs.Any(w => w.Domains == domainName);
            //                if (orgExist == true)
            //                {
            //                    var org = _weodb.Orgs.FirstOrDefault(w => w.Domains == domainName);
            //                    orgID = org.Id;
            //                    isFree = true;
            //                    isProvisioned = true;
            //                    isTrail = false;
            //                    isAccepted = true;
            //                    isClaimed = false;
            //                    var orgUserExist = _weodb.OrgDirectories.FirstOrDefault(w =>
            //                        w.OrgId == orgID && w.UserId == user.Id.ToString());
            //                    if (orgUserExist == null)
            //                    {
            //                        var orgUser = new OrgUser();
            //                        orgUser.Id = Guid.NewGuid();
            //                        orgUser.IsActive = true;
            //                        orgUser.CreatedDate = DateTime.UtcNow;
            //                        // orgUser.DeactivatedOn =;
            //                        orgUser.ActivatedOn = DateTime.UtcNow;
            //                        orgUser.ModifiedDate = DateTime.UtcNow;
            //                        orgUser.OrgId = orgID;
            //                        orgUser.UserId = user.Id;
            //                        orgUser.IsAdmin = false;
            //                        _weodb.OrgUsers.Add(orgUser);
            //                        _weodb.SaveChanges();

            //                        var orgDirectory = new OrgDirectory();
            //                        orgDirectory.Id = Guid.NewGuid();
            //                        orgDirectory.OrgId = orgID;
            //                        orgDirectory.CreatedDate = DateTime.UtcNow;
            //                        orgDirectory.ModifiedDate = DateTime.UtcNow;
            //                        orgDirectory.IsActive = true;
            //                        orgDirectory.FirstName = user.FirstName ?? "";
            //                        orgDirectory.MiddleName = user.MiddleName ?? "";
            //                        orgDirectory.LastName = user.LastName ?? "";
            //                        orgDirectory.Email = user.Email ?? "";
            //                        orgDirectory.UserId = user.Id.ToString();
            //                        orgDirectory.CountryId = user.CountryId;
            //                        _weodb.OrgDirectories.Add(orgDirectory);
            //                        _weodb.SaveChanges();
            //                    }

            //                    var orgProviderExist = _weodb.OrgProviders.FirstOrDefault(w =>
            //                        w.OrgId == orgID && w.ProviderId == providerID && w.IsActive == true);
            //                    if (orgProviderExist == null)
            //                    {
            //                        var orgProvider = new OrgProvider();
            //                        orgProvider.Id = Guid.NewGuid();
            //                        orgProvider.CreatedDate = DateTime.UtcNow;
            //                        orgProvider.ModifiedDate = DateTime.UtcNow;
            //                        orgProvider.IsActive = true;
            //                        orgProvider.OrgId = orgID;
            //                        orgProvider.ProviderId = providerID;
            //                        orgProvider.CanSaveForOffline = true;
            //                        orgProvider.ForceCallLog = true;
            //                        _weodb.OrgProviders.Add(orgProvider);
            //                        _weodb.SaveChanges();
            //                        if (weOrgProvider != null)
            //                        {
            //                            var weOrgProviderIdentifiers = _weodb.OrgIdentifiers.Where(w =>
            //                                w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();

            //                            foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
            //                            {
            //                                var orgIdentifier = new OrgIdentifier();
            //                                orgIdentifier.Id = Guid.NewGuid();
            //                                orgIdentifier.OrgProviderId = orgProvider.Id;
            //                                orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
            //                                orgIdentifier.IsActive = true;
            //                                orgIdentifier.CreatedDate = DateTime.UtcNow;
            //                                orgIdentifier.ModifiedDate = DateTime.UtcNow;
            //                                orgIdentifier.ShowSequence = 1;
            //                                orgIdentifier.Value = weOrgProviderIdentifier.Value;
            //                                _weodb.OrgIdentifiers.Add(orgIdentifier);
            //                                _weodb.SaveChanges();
            //                            }
            //                        }
            //                    }
            //                }
            //                else
            //                {
            //                    var country = _weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
            //                    var org = new Org();
            //                    org.Id = Guid.NewGuid();
            //                    org.IsBeta = false;
            //                    org.AppId = CommonData.WEAppId.ToString();
            //                    //org.IsPaid =;
            //                    org.ModifiedDate = DateTime.UtcNow;
            //                    org.CreatedDate = DateTime.UtcNow;
            //                    org.AllowedAccounts = 1;
            //                    org.Name = domainName.ToUpper();
            //                    org.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
            //                    org.Countries = country.Name + "|" + country.Prefix;
            //                    org.Domains = domainName;
            //                    org.IsActive = true;
            //                    org.IsRestricted = false;
            //                    org.IsProvider = false;
            //                    org.IsPurchaser = false;
            //                    _weodb.Orgs.Add(org);
            //                    _weodb.SaveChanges();

            //                    //var orgUser = new OrgUser
            //                    //{
            //                    //    Id = Guid.NewGuid(),
            //                    //    IsActive = true,
            //                    //    CreatedDate = DateTime.UtcNow,
            //                    //    // orgUser.DeactivatedOn =;
            //                    //    ActivatedOn = DateTime.UtcNow,
            //                    //    ModifiedDate = DateTime.UtcNow,
            //                    //    OrgId = org.Id,
            //                    //    UserId = userId
            //                    //};
            //                    //orgUser.OrgId = org.Id;
            //                    //orgUser.IsAdmin = true;
            //                    //weodb.OrgUsers.Add(orgUser);
            //                    _weodb.SaveChanges();
            //                    orgID = org.Id;

            //                    var orgProvider = new OrgProvider();
            //                    orgProvider.Id = Guid.NewGuid();
            //                    orgProvider.CreatedDate = DateTime.UtcNow;
            //                    orgProvider.ModifiedDate = DateTime.UtcNow;
            //                    orgProvider.IsActive = true;
            //                    orgProvider.OrgId = orgID;
            //                    orgProvider.ProviderId = providerID;
            //                    orgProvider.CanSaveForOffline = true;
            //                    orgProvider.ForceCallLog = true;
            //                    _weodb.OrgProviders.Add(orgProvider);
            //                    _weodb.SaveChanges();
            //                    if (weOrgProvider != null)
            //                    {
            //                        var weOrgProviderIdentifiers = _weodb.OrgIdentifiers
            //                            .Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();
            //                        foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
            //                        {
            //                            var orgIdentifier = new OrgIdentifier();
            //                            orgIdentifier.Id = Guid.NewGuid();
            //                            orgIdentifier.OrgProviderId = orgProvider.Id;
            //                            orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
            //                            orgIdentifier.IsActive = true;
            //                            orgIdentifier.CreatedDate = DateTime.UtcNow;
            //                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
            //                            orgIdentifier.ShowSequence = 1;
            //                            orgIdentifier.Value = weOrgProviderIdentifier.Value;
            //                            _weodb.OrgIdentifiers.Add(orgIdentifier);
            //                            _weodb.SaveChanges();
            //                        }
            //                    }
            //                }
            //            }
            //            else
            //            {
            //                orgID = CommonData.WhatElseCustomerOrgId;
            //            }

            //            var provider = _weddb.Providers.FirstOrDefault(w => w.Id == providerID && w.IsActive);
            //            var existingProvision = _weodb.Provisions.Any(w => w.AppId == user.AppId &&
            //                                                              w.EmailAddress == userEmail.Email
            //                                                              && w.ProviderId == providerID &&
            //                                                              w.OrgId == orgID && w.UserId == userId);
            //            if (!existingProvision)
            //            {
            //                var provision = new Provision();
            //                provision.Id = Guid.NewGuid();
            //                provision.OrgId = orgID; //CommonData.WhatElseCustomerOrgId;
            //                provision.AppId = user.AppId;
            //                provision.UserCustomerId = user.ChargebeeCustomerId;
            //                provision.ProviderTypeId = provider.ProviderTypeId;
            //                provision.ProviderId = providerID;
            //                provision.UserId = user.Id;
            //                provision.UserProviderId = Guid.Empty;
            //                provision.CreatedDate = DateTime.UtcNow;
            //                provision.ModifiedDate = DateTime.UtcNow;
            //                provision.IsActive = true;
            //                provision.IsConverted = false;
            //                provision.IsEnterpriseConverted = false;
            //                provision.IsRedeemed = false;
            //                provision.FirstName = user.FirstName;
            //                provision.LastName = user.LastName;
            //                provision.MiddleName = user.MiddleName;
            //                provision.CountryId = user.CountryId;
            //                provision.UserId = user.Id;
            //                provision.IsFree = isFree;
            //                provision.IsProvisioned = isProvisioned;
            //                provision.IsPayed = false;
            //                provision.IsRequested = false;
            //                provision.IsAccepted = isAccepted;
            //                provision.IsPurchasedByUser = false;
            //                provision.IsPurchasedOnAndroid = false;
            //                provision.IsPurchasedOnIos = false;
            //                provision.EmailAddress = userEmail.Email;
            //                provision.IsClaimed = isClaimed;
            //                provision.IsTrial = isTrail;
            //                _weodb.Provisions.Add(provision);
            //                _weodb.SaveChanges();
            //            }
            //        }

            //        var res = EmailServices.SendGridMail(appLanguage.SendGridKey, appSendgridTemplateId, user.FirstName,
            //            "", userEmail.Id.ToString(), userEmail.Email, user.AppId, "e", "", "", true);


            //        //  var res = EmailServices.SendGridMail(EmailServices.SendGridAppConstants.EmailSuccessfullyVerified, user.FirstName, "", userEmail.Id.ToString(), userEmail.Email, "e", "", "", true);
            //        break;
            //    case "en":
            //        Guid orgID2 = CommonData.WhatElseCustomerOrgId;
            //        bool isFree2 = false;
            //        bool isProvisioned2 = false;
            //        bool isTrail2 = true;
            //        bool isAccepted2 = false;
            //        bool isClaimed2 = true;
            //        Guid provisionID2 = Guid.Parse(provisionId);
            //        Guid providerID2 = Guid.Parse(providerId);
            //        userEmail.IsEmailValidated = true;
            //        _wepdb.SaveChanges();
            //        var provisionExist2 = _weodb.Provisions.FirstOrDefault(w =>
            //            w.UserId == user.Id && w.EmailAddress == userEmail.Email && w.ProviderId == providerID2 &&
            //            w.IsActive);
            //        if (provisionID2 == Guid.Empty && provisionExist2 == null)
            //        {
            //            var userDomain = userEmail.Email.Split('@');
            //            var domainName = userDomain[1];
            //            if (_weddb.Domains.Any(w => w.Address == domainName && w.IsActive == true && w.IsFree == true) ==
            //                true)
            //            {
            //                isFree = true;
            //                isProvisioned = true;
            //                isTrail = false;
            //                isAccepted = true;
            //                isClaimed = false;
            //            }

            //            var weOrgProvider = _weodb.OrgProviders.FirstOrDefault(w =>
            //                w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == providerID2 && w.IsActive);
            //            //var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive).ToList();

            //            bool isFreeDomain = _weddb.FreeDomains.Any(w => w.DomainName.ToLower() == domainName.ToLower());
            //            if (isFreeDomain == false)
            //            {
            //                var orgExist = _weodb.Orgs.Any(w => w.Domains == domainName);
            //                if (orgExist == true)
            //                {
            //                    var org = _weodb.Orgs.FirstOrDefault(w => w.Domains == domainName);
            //                    orgID = org.Id;
            //                    isFree = true;
            //                    isProvisioned = true;
            //                    isTrail = false;
            //                    isAccepted = true;
            //                    isClaimed = false;
            //                    var orgUserExist = _weodb.OrgDirectories.FirstOrDefault(w =>
            //                        w.OrgId == orgID && w.UserId == user.Id.ToString());
            //                    if (orgUserExist == null)
            //                    {
            //                        var orgUser = new OrgUser();
            //                        orgUser.Id = Guid.NewGuid();
            //                        orgUser.IsActive = true;
            //                        orgUser.CreatedDate = DateTime.UtcNow;
            //                        // orgUser.DeactivatedOn =;
            //                        orgUser.ActivatedOn = DateTime.UtcNow;
            //                        orgUser.ModifiedDate = DateTime.UtcNow;
            //                        orgUser.OrgId = orgID;
            //                        orgUser.UserId = user.Id;
            //                        orgUser.IsAdmin = false;
            //                        _weodb.OrgUsers.Add(orgUser);
            //                        _weodb.SaveChanges();

            //                        var orgDirectory = new OrgDirectory();
            //                        orgDirectory.Id = Guid.NewGuid();
            //                        orgDirectory.OrgId = orgID;
            //                        orgDirectory.CreatedDate = DateTime.UtcNow;
            //                        orgDirectory.ModifiedDate = DateTime.UtcNow;
            //                        orgDirectory.IsActive = true;
            //                        orgDirectory.FirstName = user.FirstName ?? "";
            //                        orgDirectory.MiddleName = user.MiddleName ?? "";
            //                        orgDirectory.LastName = user.LastName ?? "";
            //                        orgDirectory.Email = user.Email ?? "";
            //                        orgDirectory.UserId = user.Id.ToString();
            //                        orgDirectory.CountryId = user.CountryId;
            //                        _weodb.OrgDirectories.Add(orgDirectory);
            //                        _weodb.SaveChanges();
            //                    }

            //                    var orgProviderExist = _weodb.OrgProviders.FirstOrDefault(w =>
            //                        w.OrgId == orgID && w.ProviderId == providerID2 && w.IsActive == true);
            //                    if (orgProviderExist == null)
            //                    {
            //                        var orgProvider = new OrgProvider();
            //                        orgProvider.Id = Guid.NewGuid();
            //                        orgProvider.CreatedDate = DateTime.UtcNow;
            //                        orgProvider.ModifiedDate = DateTime.UtcNow;
            //                        orgProvider.IsActive = true;
            //                        orgProvider.OrgId = orgID;
            //                        orgProvider.ProviderId = providerID2;
            //                        orgProvider.CanSaveForOffline = true;
            //                        orgProvider.ForceCallLog = true;
            //                        _weodb.OrgProviders.Add(orgProvider);
            //                        _weodb.SaveChanges();
            //                        if (weOrgProvider != null)
            //                        {
            //                            var weOrgProviderIdentifiers = _weodb.OrgIdentifiers.Where(w =>
            //                                w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();

            //                            foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
            //                            {
            //                                var orgIdentifier = new OrgIdentifier();
            //                                orgIdentifier.Id = Guid.NewGuid();
            //                                orgIdentifier.OrgProviderId = orgProvider.Id;
            //                                orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
            //                                orgIdentifier.IsActive = true;
            //                                orgIdentifier.CreatedDate = DateTime.UtcNow;
            //                                orgIdentifier.ModifiedDate = DateTime.UtcNow;
            //                                orgIdentifier.ShowSequence = 1;
            //                                orgIdentifier.Value = weOrgProviderIdentifier.Value;
            //                                _weodb.OrgIdentifiers.Add(orgIdentifier);
            //                                _weodb.SaveChanges();
            //                            }
            //                        }
            //                    }
            //                }
            //                else
            //                {
            //                    var country = _weddb.Countries.FirstOrDefault(w => w.Alpha2 == user.CountryId);
            //                    var org = new Org();
            //                    org.Id = Guid.NewGuid();
            //                    org.IsBeta = false;
            //                    org.AppId = CommonData.WEAppId.ToString();
            //                    //org.IsPaid =;
            //                    org.ModifiedDate = DateTime.UtcNow;
            //                    org.CreatedDate = DateTime.UtcNow;
            //                    org.AllowedAccounts = 1;
            //                    org.Name = domainName.ToUpper();
            //                    org.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
            //                    org.Countries = country.Name + "|" + country.Prefix;
            //                    org.Domains = domainName;
            //                    org.IsActive = true;
            //                    org.IsRestricted = false;
            //                    org.IsProvider = false;
            //                    org.IsPurchaser = false;
            //                    _weodb.Orgs.Add(org);
            //                    _weodb.SaveChanges();

            //                    //var orgUser = new OrgUser
            //                    //{
            //                    //    Id = Guid.NewGuid(),
            //                    //    IsActive = true,
            //                    //    CreatedDate = DateTime.UtcNow,
            //                    //    // orgUser.DeactivatedOn =;
            //                    //    ActivatedOn = DateTime.UtcNow,
            //                    //    ModifiedDate = DateTime.UtcNow,
            //                    //    OrgId = org.Id,
            //                    //    UserId = userId
            //                    //};
            //                    //orgUser.OrgId = org.Id;
            //                    //orgUser.IsAdmin = true;
            //                    //weodb.OrgUsers.Add(orgUser);
            //                    _weodb.SaveChanges();
            //                    orgID = org.Id;

            //                    var orgProvider = new OrgProvider();
            //                    orgProvider.Id = Guid.NewGuid();
            //                    orgProvider.CreatedDate = DateTime.UtcNow;
            //                    orgProvider.ModifiedDate = DateTime.UtcNow;
            //                    orgProvider.IsActive = true;
            //                    orgProvider.OrgId = orgID;
            //                    orgProvider.ProviderId = providerID2;
            //                    orgProvider.CanSaveForOffline = true;
            //                    orgProvider.ForceCallLog = true;
            //                    _weodb.OrgProviders.Add(orgProvider);
            //                    _weodb.SaveChanges();
            //                    if (weOrgProvider != null)
            //                    {
            //                        var weOrgProviderIdentifiers = _weodb.OrgIdentifiers
            //                            .Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();
            //                        foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
            //                        {
            //                            var orgIdentifier = new OrgIdentifier();
            //                            orgIdentifier.Id = Guid.NewGuid();
            //                            orgIdentifier.OrgProviderId = orgProvider.Id;
            //                            orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
            //                            orgIdentifier.IsActive = true;
            //                            orgIdentifier.CreatedDate = DateTime.UtcNow;
            //                            orgIdentifier.ModifiedDate = DateTime.UtcNow;
            //                            orgIdentifier.ShowSequence = 1;
            //                            orgIdentifier.Value = weOrgProviderIdentifier.Value;
            //                            _weodb.OrgIdentifiers.Add(orgIdentifier);
            //                            _weodb.SaveChanges();
            //                        }
            //                    }
            //                }
            //            }
            //            else
            //            {
            //                orgID = CommonData.WhatElseCustomerOrgId;
            //            }

            //            var provider = _weddb.Providers.FirstOrDefault(w => w.Id == providerID2 && w.IsActive);
            //            var provision = new Provision();
            //            provision.Id = Guid.NewGuid();
            //            provision.OrgId = orgID; //CommonData.WhatElseCustomerOrgId;
            //            provision.AppId = user.AppId;
            //            provision.UserCustomerId = user.ChargebeeCustomerId;
            //            provision.ProviderTypeId = provider.ProviderTypeId;
            //            provision.ProviderId = providerID2;
            //            provision.UserId = user.Id;
            //            provision.UserProviderId = Guid.Empty;
            //            provision.CreatedDate = DateTime.UtcNow;
            //            provision.ModifiedDate = DateTime.UtcNow;
            //            provision.IsActive = true;
            //            provision.IsConverted = false;
            //            provision.IsEnterpriseConverted = false;
            //            //provision.SanitizedNumber = user.SanitizedNumber;
            //            provision.IsRedeemed = false;
            //            provision.PhoneNumber = "";
            //            provision.FirstName = user.FirstName;
            //            provision.LastName = user.LastName;
            //            provision.MiddleName = user.MiddleName;
            //            //provision.CountryId = user.CountryId;
            //            provision.UserId = user.Id;
            //            provision.IsFree = isFree2;
            //            provision.IsProvisioned = isProvisioned2;
            //            provision.IsPayed = false;
            //            provision.IsRequested = false;
            //            provision.IsAccepted = isAccepted2;
            //            provision.IsPurchasedByUser = false;
            //            provision.IsPurchasedOnAndroid = false;
            //            provision.IsPurchasedOnIos = false;
            //            provision.EmailAddress = userEmail.Email;
            //            provision.IsClaimed = isClaimed2;
            //            provision.IsTrial = isTrail2;
            //            _weodb.Provisions.Add(provision);
            //            _weodb.SaveChanges();
            //        }

            //        var res2 = EmailServices.SendGridMail(appLanguage.SendGridKey, appSendgridTemplateId,
            //            user.FirstName, "", userEmail.Id.ToString(), userEmail.Email, user.AppId, "e", "", "", true);


            //        //  var res = EmailServices.SendGridMail(EmailServices.SendGridAppConstants.EmailSuccessfullyVerified, user.FirstName, "", userEmail.Id.ToString(), userEmail.Email, "e", "", "", true);
            //        break;
            //    case "s":
            //        Guid demoRequestid = Guid.Parse(Id);
            //        var demoRequest = _weadb.DemoRequests.FirstOrDefault(w => w.Id == demoRequestid);
            //        if (demoRequest.IsEmailValidated == false)
            //        {
            //            demoRequest.IsEmailValidated = true;
            //            _weadb.SaveChanges();

            //            var resWe = EmailServices.SendGridMail(appLanguage.SendGridKey, appSendgridTemplateId,
            //                demoRequest.FirstName, "", demoRequest.Id.ToString(), demoRequest.Email, user.AppId, "s",
            //                "", "", true);

            //            // var res1 = EmailServices.SendGridMail(EmailServices.SendGridAppConstants.EmailSuccessfullyVerified, demoRequest.FirstName, "", demoRequest.Id.ToString(), demoRequest.Email, "s", "", "", true);
            //        }

            //        break;
            //    case "ce":
            //        //Guid myContactEmailId = Id);
            //        //var myContactEmail = wesdb.ContactEmails.FirstOrDefault(w => w.Id == Id);
            //        //var myContact = wesdb.Contacts.FirstOrDefault(w => w.Id == myContactEmail.ContactId);
            //        //myContactEmail.IsEmailValidated = true;
            //        //wesdb.SaveChanges();

            //        //var resDefaultWe = EmailServices.SendGridMail(appLanguage.SendGridKey, appSendgridTemplateId, myContact.FirstName, "", myContactEmail.Id.ToString(), myContactEmail.Email, user.AppId, "e", "", "", true);

            //        // var res2 = EmailServices.SendGridMail(EmailServices.SendGridAppConstants.EmailSuccessfullyVerified, myContact.FirstName, "", myContactEmail.Id.ToString(), myContactEmail.Email, "e", "", "", true);
            //        break;
            //    case "p":
            //        var provisionExists = _weodb.Provisions.FirstOrDefault(w => w.Id.ToString().StartsWith(Id));
            //        if (provisionExists != null)
            //        {
            //            provisionExists.IsAccepted = true;
            //        }

            //        break;
            //    default:
            //        break;
            //}
        }
        catch (Exception ex)
        {
            throw;
        }

        //Guid userEmailId = Guid.Parse(Id);
        //var userEmail = db.UserEmails.FirstOrDefault(w => w.Id == userEmailId);
        //userEmail.IsEmailValidated = true;
        //db.SaveChanges();
        return View();
    }
}