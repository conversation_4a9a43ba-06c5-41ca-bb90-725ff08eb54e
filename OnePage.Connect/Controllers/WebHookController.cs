//
// using Newtonsoft.Json;
// using Newtonsoft.Json.Linq;
// using RestSharp;
// using System;
// using System.Collections.Generic;
// using System.Linq;
// using System.Net;
// using System.Net.Http;
// using System.Net.Http.Headers;
// using System.Text;
// using System.Threading.Tasks;
// using Microsoft.AspNetCore.Authorization;
// using Microsoft.AspNetCore.Mvc;
// using OnePage.Common;
// using OnePage.Connect.Context;
// using OnePage.Connect.Controllers;
// using OnePage.Connect.Migrations;
// using OnePage.Connect.Services;
// using OnePage.Models;
// using OnePage.Services;
//
//
// namespace WhatElse.CatchAll.Controllers
// {
//     public class WebhookController : Controller
//     {
//         private WETokenEntitiesDBContext wetdb = new WETokenEntitiesDBContext();
//         private WEOrgEntitiesDBContext weodb = new WEOrgEntitiesDBContext();
//
//         private WEPeopleEntitiesDBContext wepdb = new WEPeopleEntitiesDBContext();
//         //private WESyncEntities wesdb = new WESyncEntities();
//         private WEDataEntitiesDBContext weddb = new WEDataEntitiesDBContext();
//         private WEAdminEntitiesDBContext weadb = new WEAdminEntitiesDBContext();
//         private List<Identifier> identifierList = new List<Identifier>();
//         private List<OrgIdentifier> orgProviderIdentifierList = new List<OrgIdentifier>();
//         private List<OrgIdentifier> orgProviderIdentifierList2 = new List<OrgIdentifier>();
//         private List<Url> providerUrlList = new List<Url>();
//         private List<AuthenticatedController.TempList> nameValueList = new List<AuthenticatedController.TempList>();
//         private List<AuthenticatedController.TempList> headersList = new List<AuthenticatedController.TempList>();
//         private List<AuthenticatedController.TempList> urlsCompleted = new List<AuthenticatedController.TempList>();
//         
//         [AllowAnonymous]
//         [Route("h327915f0677a444c99a81b4998a4623c")]
//         [HttpPost]
//         public async Task<IHttpActionResult> h327915f0677a444c99a81b4998a4623c(string validationToken = null)
//         {
//             // handle validation
//             if (!string.IsNullOrEmpty(validationToken))
//             {
//                 Console.WriteLine($"Received Token: '{validationToken}'");
//                 return Ok(validationToken);
//             }
//
//             // handle notifications
//             //using (StreamReader reader = new StreamReader(Request.Body))
//             //{
//             //    string content = await reader.ReadToEndAsync();
//
//             //    Console.WriteLine(content);
//
//             //    var notifications = JsonConvert.DeserializeObject<Notifications>(content);
//
//             //    foreach (var notification in notifications.Items)
//             //    {
//             //        Console.WriteLine($"Received notification: '{notification.Resource}', {notification.ResourceData?.Id}");
//             //    }
//             //}
//
//             return Ok();
//         }
//
//         [AllowAnonymous]
//         [Route("3FB2ED07")]
//         [HttpPost]
//         public async Task<IHttpActionResult> HandleTeamsSSO(string ssotoken, string appId = "6DE53356-EB21-4830-A7DE-7E2256395525")
//         {
//             try
//             {
//                 Guid AppId = Guid.Parse(appId);
//                 Guid providerId = Guid.Parse("5CC37A0C-96DD-4E30-93C7-A0A991156640");
//                 var orgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");
//                 var providerUrls = weddb.ProviderUrls.FirstOrDefault(w => w.ProviderId == providerId);
//                 identifierList = weddb.Identifiers.Where(w => w.ProviderId == providerUrls.ProviderId && w.IsActive).ToList();
//                 var orgProvider = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == orgId && w.IsActive && w.ProviderId == providerId && w.IsActive);
//                 orgProviderIdentifierList = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == orgProvider.Id && w.IsActive).ToList();
//
//                 providerUrlList = weddb.Urls.Where(w => w.ProviderUrls.Where(c => c.ProviderId == providerId && c.IsActive == true && w.ShowOrder > 2000 && w.ShowOrder < 3000)
//                 .Count() > 0).OrderBy(w => w.ShowOrder).ToList();
//                 foreach (var providerUrl in providerUrlList)
//                 {
//                     TelegramService.SendMessageToTestBot("providerurl name: " + providerUrl.Name);
//                     if (providerUrl.HasHeaders)
//                     {
//                         var headerList = weddb.Headers.Where(w => w.Urlid == providerUrl.Id && w.IsActive == true);
//                         foreach (var header in headerList)
//                         {
//                             //headersList.Clear();
//                             if (header.Prefix.Contains("{") == true)
//                             {
//                                 var headervalue = header.Prefix;
//                                 foreach (var item in identifierList)
//                                 {
//                                     if (headervalue.Contains("{" + item.Name + "}"))
//                                     {
//                                         if (item.Value != null)
//                                         {
//                                             headervalue = headervalue.Replace("{" + item.Name + "}", item.Value);
//                                             headersList.Add(new AuthenticatedController.TempList { Name = header.Name, Value = headervalue });
//                                         }
//                                     }
//                                 }
//                                 foreach (var item in orgProviderIdentifierList)
//                                 {
//                                     var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == item.IdentifierId).Name;
//                                     if (headervalue.Contains("{" + identifierName + "}"))
//                                     {
//                                         headervalue = headervalue.Replace("{" + identifierName + "}", item.Value);
//                                         headersList.Add(new AuthenticatedController.TempList { Name = header.Name, Value = headervalue });
//                                     }
//                                 }
//                             }
//                             else
//                             {
//                                 headersList.Add(new AuthenticatedController.TempList { Name = header.Name, Value = header.Prefix });
//                             }
//                         }
//                     }
//                     var dataToReplace = "";
//                     if (providerUrl.NeedsData) //amper
//                     {
//                         dataToReplace = providerUrl.Posts.FirstOrDefault(w => w.IsActive = true).Name;
//                         foreach (var item in identifierList)
//                         {
//                             dataToReplace = dataToReplace.Replace("{" + item.Name + "}", item.Value);
//                         }
//                         foreach (var item in orgProviderIdentifierList)
//                         {
//                             var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == item.IdentifierId).Name;
//                             dataToReplace = dataToReplace.Replace("{" + identifierName + "}", item.Value);
//                         }
//                     }
//                     var finalURL = providerUrl.Name;
//                     finalURL = FindAndReplaceIndentifiers(finalURL);
//                     TelegramService.SendMessageToTestBot("final: " + finalURL);
//
//                     if (providerUrl.HTTPType == 1)
//                     {
//                         bool postRetuenValue = false;
//                         //  TelegramService.SendMessageToTestBot("Final Url: " + finalURL);
//                         postRetuenValue = await PostToken(ssotoken, providerUrl);
//                         CommonData.sbnew.AppendLine("Linkedin Post token api response: " + postRetuenValue);
//                         TelegramService.SendMessageToTestBot("POST: " + postRetuenValue);
//                         if (postRetuenValue == false)
//                         {
//                             return BadRequest();
//                         }
//                         headersList.Clear();
//                     }
//                     else if (providerUrl.HTTPType == 2)
//                     {
//                         bool getRetuenValue = false;
//                         getRetuenValue = await GetClient(finalURL, providerUrl, dataToReplace);
//                         CommonData.sbnew.AppendLine("Linkedin me api response: " + getRetuenValue);
//                         TelegramService.SendMessageToTestBot("GET: " + getRetuenValue);
//                         if (getRetuenValue == false)
//                         {
//                             return BadRequest();
//                         }
//                         headersList.Clear();
//                     }
//                     urlsCompleted.Add(new AuthenticatedController.TempList { Name = providerUrl.Id.ToString(), Value = "1" });
//                 }
//                 var provider = weddb.Providers.FirstOrDefault(w => w.Id == providerId);
//                 var emailId = provider.Name ?? "";
//                 bool mailExist = false;
//
//                 mailExist = identifierList.Any(w => w.Name.Contains("userprincipalname") && w.Value != null && w.Value != "");
//
//                 if (mailExist == false)
//                 {
//                     var nameExist = identifierList.Any(w => w.Name.Contains("name") && w.Value != null && w.Value != "");
//                     emailId = (nameExist == true) ? identifierList.FirstOrDefault(w => w.Name.Contains("name") && w.Value != null && w.Value != "").Value.ToLower().ToString() : "";
//                     if (emailId == "")
//                     {
//                         emailId = (mailExist == true) ? identifierList.FirstOrDefault(w => w.Name.Contains("mail") && w.Value != null && w.Value != "").Value.ToLower().ToString() : "";//;
//                     }
//                 }
//                 else
//                 {
//                     emailId = (mailExist == true) ? identifierList.FirstOrDefault(w => w.Name.Contains("userprincipalname") && w.Value != null && w.Value != "").Value.ToLower().ToString() : "";
//                 }
//                 Guid orgID = CommonData.WhatElseCustomerOrgId;
//
//                 bool isProvisioned = false;
//                 bool isAccepted = true;
//                 bool isTrail = false;
//                 bool isClaimed = true;
//
//                 var userDomain = emailId.Split('@');
//                 var domainName = userDomain[1].ToLower();
//                 var isfreeDomain = weddb.FreeDomains.FirstOrDefault(w => w.DomainName.ToLower() == domainName.ToLower());
//                 var orgExist = weodb.Orgs.Any(w => w.Domains == domainName);
//                 if (orgExist == true)
//                 {
//                     var org = weodb.Orgs.FirstOrDefault(w => w.Domains.ToLower() == domainName.ToLower());
//                     orgID = org.Id;
//
//                 }
//                 else
//                 {
//                     var country1 = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
//                     var org = new Org();
//                     org.Id = Guid.NewGuid();
//                     org.IsBeta = false;
//                     org.AppId = CommonData.WEAppId.ToString();
//                     org.ModifiedDate = DateTime.UtcNow;
//                     org.CreatedDate = DateTime.UtcNow;
//                     org.AllowedAccounts = 1;
//                     org.Name = domainName.ToLower();
//                     org.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
//                     org.Countries = country1.Name + "|" + country1.Prefix;
//                     org.Domains = domainName;
//                     org.IsActive = true;
//                     org.IsRestricted = false;
//                     org.IsProvider = false;
//                     org.IsPurchaser = false;
//                     org.IsAdminGivenFree = false;
//                     org.IsInternal = false;
//                     weodb.Orgs.Add(org);
//                     weodb.SaveChanges();
//
//                     orgID = org.Id;
//                     isProvisioned = true;
//                     isAccepted = true;
//                     isTrail = false;
//                     isClaimed = false;
//
//                     if (isfreeDomain == null)
//                     {
//                         ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
//
//                         //ApiConfig.Configure("mapit4me", "live_adovCI4ou0Wcdb0BokjBE0Zcg9N2nFJgz");
//                        
//                         EntityResult result = ChargeBee.Models.Customer.Create()
//                                      .FirstName("Company")
//                                      .LastName("OAYAW")
//                                      .Email(emailId)
//                                      .Company("Company - " + domainName).Request();
//                         ChargeBee.Models.Customer customer = result.Customer;
//                         ChargeBee.Models.Card card = result.Card;
//                         wepdb.SaveChanges();
//                     }
//                     else
//                     {
//                         ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
//
//                         EntityResult result = ChargeBee.Models.Customer.Create()
//                                      .FirstName("Company")
//                                      .LastName("OAYAW")
//                                      .Email(emailId)
//                                      .Company("Freedomain - " + domainName).Request();
//                         ChargeBee.Models.Customer customer = result.Customer;
//                         ChargeBee.Models.Card card = result.Card;
//                         wepdb.SaveChanges();
//                     }
//                 }
//                 var orgProviderExist = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == orgID && w.ProviderId == providerId && w.IsActive == true);
//                 if (orgProviderExist == null)
//                 {
//                     var op = new OrgProvider();
//                     op.Id = Guid.NewGuid();
//                     op.CreatedDate = DateTime.UtcNow;
//                     op.ModifiedDate = DateTime.UtcNow;
//                     op.IsActive = true;
//                     op.OrgId = orgID;
//                     op.ProviderId = provider.Id;
//                     op.CanSaveForOffline = true;
//                     op.ForceCallLog = true;
//                     weodb.OrgProviders.Add(op);
//
//                     var weOrgProvider = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == provider.Id && w.IsActive == true);
//                     if (weOrgProvider != null)
//                     {
//                         var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();
//
//                         foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
//                         {
//                             var orgIdentifier = new OrgIdentifier();
//                             orgIdentifier.Id = Guid.NewGuid();
//                             orgIdentifier.OrgProviderId = op.Id;
//                             orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
//                             orgIdentifier.IsActive = true;
//                             orgIdentifier.CreatedDate = DateTime.UtcNow;
//                             orgIdentifier.ModifiedDate = DateTime.UtcNow;
//                             orgIdentifier.ShowSequence = 1;
//                             orgIdentifier.Value = weOrgProviderIdentifier.Value;
//                             weodb.OrgIdentifiers.Add(orgIdentifier);
//                             weodb.SaveChanges();
//                         }
//                     }
//                 }
//
//                 var postZoneUrl = "https://eu-fc-ap-sz.azurewebsites.net/";
//                 TelegramService.SendMessageToTestBot("serverzone starts!");
//
//                 var authUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 17 && w.AppId.ToString().ToLower() == AppId.ToString().ToLower() && w.IsActive).URL;
//                 var tempUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 17 && w.AppId.ToString().ToLower() == AppId.ToString().ToLower() && w.IsActive).URL;
//                 emailId = (emailId == "") ? provider.Name : emailId;
//                 //  eId = emailId;
//                 string firstName = "";
//                 string lasttName = "";
//                 string profileId = "";
//                 string displayImage = "";
//                 string country = "";
//                 string role = "";
//                 PropertyModels.AuthModels.AuthUserModel authUserModel = new PropertyModels.AuthModels.AuthUserModel();
//                 PropertyModels.AuthModels.ValidationModel validationModel = new PropertyModels.AuthModels.ValidationModel();
//
//                 var displayname = identifierList.Any(w => w.Name.Contains("displayname") && w.Value != null && w.Value != "");
//                 if (displayname)
//                 {
//                     firstName = identifierList.First(w => w.Name.Contains("displayname") && w.Value != null && w.Value != "").Value.ToString();
//                 }
//                 var fnameExists = identifierList.Any(w => w.Name.Contains("first_name") && w.Value != null && w.Value != "");
//                 if (fnameExists)
//                 {
//                     firstName = identifierList.First(w => w.Name.Contains("first_name") && w.Value != null && w.Value != "").Value.ToString();
//                 }
//                 var lnameExists = identifierList.Any(w => w.Name.Contains("last_name") && w.Value != null && w.Value != "");
//                 if (lnameExists)
//                 {
//                     lasttName = identifierList.First(w => w.Name.Contains("last_name") && w.Value != null && w.Value != "").Value.ToString();
//                 }
//                 var isRole = identifierList.Any(w => w.Name.Contains("jobtitle") && w.Value != null && w.Value != "");
//                 if (isRole)
//                 {
//                     role = identifierList.First(w => w.Name.Contains("jobtitle") && w.Value != null && w.Value != "").Value.ToString();
//                 }
//                 // call serverzone api
//                 // TelegramService.SendMessageToTestBot(deviceId);
//                 // TelegramService.SendMessageToTestBot(deviceTypeId);
//
//                 Guid dId1 = Guid.NewGuid();
//                 Guid dtId1 = Guid.Parse("467aae32-8361-43a3-a8c7-be21e32c4f3b");
//                 authUserModel.AppId = AppId;
//                 authUserModel.CountryId = country;
//                 authUserModel.EmailAddress = emailId;
//                 authUserModel.PhoneNumber = "";
//                 authUserModel.Password = dId1.ToString().ToLower();
//                 authUserModel.Coupon = "";
//                 authUserModel.IsPreAuthenticated = true;
//                 authUserModel.FirstName = firstName;
//                 authUserModel.LastName = lasttName;
//                 authUserModel.LoginFlowId = providerId;
//                 authUserModel.DeviceId = Guid.NewGuid();
//                 authUserModel.DeviceType = dtId1;
//                 var json1 = JsonConvert.SerializeObject(authUserModel);
//                 postZoneUrl = postZoneUrl + "06E12CFF";
//                 //   TelegramService.SendMessageToTestBot(json1.ToString() + " " + postZoneUrl);
//                 var client1 = new RestSharp.RestClient(postZoneUrl);
//                 var request1 = new RestRequest(Method.POST);
//                 request1.AddHeader("content-type", "application/json");
//                 request1.AddParameter("application/json", json1, ParameterType.RequestBody);
//                 var response1 = await client1.ExecuteAsync(request1);
//                 TelegramService.SendMessageToTestBot(response1.StatusCode.ToString());
//                 //   TelegramService.SendMessageToTestBot(JsonConvert.SerializeObject(response1).ToString());
//                 //  PreloginReturnModel2 Djson3 = JsonConvert.DeserializeObject<PreloginReturnModel2>(response1.Content);
//
//                 // call validate api
//                 validationModel.IsPreAuthenticated = true;
//                 validationModel.EmailAddress = emailId;
//                 validationModel.AppId = AppId;
//                 validationModel.DeviceId = dId1;
//                 validationModel.DeviceTypeId = dtId1;
//                 validationModel.OTP = dId1.ToString().ToLower();
//                 validationModel.LanguageId = "English (US)";
//
//                 var validateJson1 = JsonConvert.SerializeObject(validationModel);
//                 authUrl = authUrl + "165C8F0E";
//                 TelegramService.SendMessageToTestBot(authUrl);
//                 TelegramService.SendMessageToTestBot(validateJson1);
//                 var client3 = new RestSharp.RestClient(authUrl);
//                 var request3 = new RestRequest(Method.POST);
//                 request3.AddHeader("content-type", "application/json;charset=UTF-8");
//                 request3.AddParameter("application/json;charset=UTF-8", validateJson1, ParameterType.RequestBody);
//                 var response3 = await client3.ExecuteAsync(request3);
//                 PropertyModels.AuthModels.ValidateReturnModel Djson4 = JsonConvert.DeserializeObject<PropertyModels.AuthModels.ValidateReturnModel>(response3.Content);
//
//                 //TelegramService.SendMessageToTestBot(emailId);
//                 TelegramService.SendMessageToTestBot(response3.StatusCode.ToString());
//                 var uId = wepdb.Users.First(w => w.Email == emailId).Id;
//                 //   appUserId = uId.ToString();
//                 //  TelegramService.SendMessageToTestBot(uId.ToString());
//                 if (Djson4.UserStatusModel.Registration == false)
//                 {
//                     try
//                     {
//                         var registerUrl = weadb.Servers.FirstOrDefault(w => w.ServerType == 17 && w.AppId.ToString().ToLower() == AppId.ToString().ToLower() && w.IsActive).URL;
//                         WhatElse.CatchAll.Controllers.AuthenticatedController.UserDetailModel regModel = new WhatElse.CatchAll.Controllers.AuthenticatedController.UserDetailModel();
//                         regModel.Company = "";
//                         regModel.Designation = "";
//                         regModel.Email = emailId;
//                         regModel.FirstName = firstName;
//                         regModel.LastName = lasttName;
//                         regModel.UserId = uId;
//
//                         var token = wetdb.Tokens.First(w => w.UserId == uId && w.IsActive == true).Id;
//                         var json2 = JsonConvert.SerializeObject(regModel);
//                         registerUrl = registerUrl + "59187A47";
//                         var client4 = new RestSharp.RestClient(registerUrl);
//                         var request4 = new RestRequest(Method.POST);
//                         request4.AddHeader("content-type", "application/json;charset=UTF-8");
//                         request4.AddHeader("authToken", token.ToString());
//                         request4.AddParameter("application/json;charset=UTF-8", json2, ParameterType.RequestBody);
//                         var response4 = await client4.ExecuteAsync(request4);
//                         TelegramService.SendMessageToTestBot(response4.StatusCode.ToString());
//                     }
//                     catch (Exception ex)
//                     {
//                         TelegramService.SendMessageToTestBot(ex.ToString());
//                     }
//                 }
//
//                 Models.User user;
//                 // if (isLogin == "1")
//                 user = wepdb.Users.First(w => w.Email == emailId && w.AppId == AppId);
//                 //else
//                 //{
//                 //    Guid tokenValue = Guid.Parse(deviceId);
//                 //    var usrId = wetdb.Tokens.First(w => w.Id == tokenValue).UserId;
//                 //    user = wepdb.Users.First(w => w.Id == usrId);
//                 //}
//                 if (user != null)
//                 {
//                     TelegramService.SendMessageToTestBot(user.Email);
//                     TelegramService.SendMessageToTestBot(user.Id.ToString());
//
//                     // Guid teamsProviderId = Guid.Parse(urlproviderId);
//                     var isExistingUP = wepdb.UserProviders.Any(w => w.UserId == user.Id && w.ProviderId == providerId && w.IsActive && w.EmailAddress.ToLower() == emailId.ToLower() && w.AppId == user.AppId);
//                     if (isExistingUP)
//                     {
//                         TelegramService.SendMessageToTestBot("IsExisting");
//                         var existingUP = wepdb.UserProviders.First(w => w.UserId == user.Id && w.ProviderId == providerId && w.IsActive && w.EmailAddress.ToLower() == emailId.ToLower() && w.AppId == user.AppId);
//                         TelegramService.SendMessageToTestBot("up id: " + existingUP.Id.ToString());
//                         foreach (var identifier in identifierList)
//                         {
//                             try
//                             {
//                                 if (identifier.Value != null && identifier.Value != "")
//                                 {
//                                     var isExistingUI = wepdb.UserProviderIdentifiers.Any(w => w.UserProviderId == existingUP.Id && w.IdentifierId == identifier.Id);
//                                     if (isExistingUI)
//                                     {
//                                         var existingUI = wepdb.UserProviderIdentifiers.First(w => w.UserProviderId == existingUP.Id && w.IdentifierId == identifier.Id);
//                                         existingUI.Value = identifier.Value;
//                                         existingUI.ModifiedDate = DateTime.UtcNow;
//                                         wepdb.SaveChanges();
//                                     }
//                                     else
//                                     {
//                                         var upIdentifiers = new UserProviderIdentifier();
//                                         upIdentifiers.Id = Guid.NewGuid();
//                                         upIdentifiers.IdentifierId = identifier.Id;
//                                         upIdentifiers.UserProviderId = existingUP.Id;
//                                         upIdentifiers.Value = identifier.Value;
//                                         upIdentifiers.IsActive = true;
//                                         upIdentifiers.CreatedDate = DateTime.UtcNow;
//                                         upIdentifiers.ModifiedDate = DateTime.UtcNow;
//                                         wepdb.UserProviderIdentifiers.Add(upIdentifiers);
//                                         wepdb.SaveChanges();
//                                     }
//
//                                 }
//                             }
//                             catch (Exception ex)
//                             {
//                                 continue;
//                                 // throw;
//                             }
//                         }
//                     }
//                     else
//                     {
//                         TelegramService.SendMessageToTestBot("Not Existing");
//                         var newUserProvider = new UserProvider();
//                         newUserProvider.Id = Guid.NewGuid();
//                         newUserProvider.UserId = user.Id;
//                         newUserProvider.AppId = user.AppId;
//                         newUserProvider.OrgId = orgId;
//                         newUserProvider.ProviderId = providerId;
//                         newUserProvider.EmailAddress = emailId.ToLower();
//                         newUserProvider.IsAuthenticated = true;
//                         newUserProvider.IsActive = true;
//                         newUserProvider.Code = "";
//                         newUserProvider.ActiveFrom = DateTime.UtcNow;
//                         newUserProvider.ActiveTill = DateTime.UtcNow.AddYears(1);
//                         newUserProvider.IsProvisioned = false;
//                         newUserProvider.IsFree = true;
//                         newUserProvider.IsPayed = false;
//                         newUserProvider.IsFullSyncDone = false;
//                         newUserProvider.Source = 0;
//                         newUserProvider.PurchaseID = "";
//                         newUserProvider.PurchaseState = 0;
//                         newUserProvider.ProductId = "";
//                         newUserProvider.CreatedDate = DateTime.UtcNow;
//                         newUserProvider.ModifiedDate = DateTime.UtcNow;
//                         newUserProvider.IsRelogginRequired = false;
//                         newUserProvider.Priority = provider.ProviderType.Priority ?? 100;
//                         int trialDays = 30;
//                         newUserProvider.IsTrial = false;
//                         wepdb.UserProviders.Add(newUserProvider);
//                         wepdb.SaveChanges();
//
//                         foreach (var identifier in identifierList)
//                         {
//                             try
//                             {
//                                 if (identifier.Value != null && identifier.Value != "")
//                                 {
//
//                                     var upIdentifiers = new UserProviderIdentifier();
//                                     upIdentifiers.Id = Guid.NewGuid();
//                                     upIdentifiers.IdentifierId = identifier.Id;
//                                     upIdentifiers.UserProviderId = newUserProvider.Id;
//                                     upIdentifiers.Value = identifier.Value;
//                                     upIdentifiers.IsActive = true;
//                                     upIdentifiers.CreatedDate = DateTime.UtcNow;
//                                     upIdentifiers.ModifiedDate = DateTime.UtcNow;
//                                     wepdb.UserProviderIdentifiers.Add(upIdentifiers);
//                                     wepdb.SaveChanges();
//                                 }
//                             }
//                             catch (Exception ex)
//                             {
//                                 continue;
//                                 // throw;
//                             }
//                         }
//                         wepdb.SaveChanges();
//                     }
//
//                 }
//                 // call signalr and onesignal
//
//                 //if (deviceTypeId.ToLower() != "9C329792-9519-4D6C-BE9D-F12898113416".ToLower())
//                 //{
//                 var appInfo2 = weadb.
//                 Apps.FirstOrDefault(w => w.Id == AppId && w.IsActive);
//                 var androidOneSignalId2 = appInfo2.AndroidOneSignalId;
//                 var androidOneSignalKey2 = appInfo2.AndroidOneSignalApiKey;
//                 var IosOneSignalId2 = appInfo2.IOSOneSignalId;
//                 var IosOneSignalKey2 = appInfo2.IOSOneSignalApiKey;
//                 var WebOneSignalId2 = appInfo2.WebOneSignalId;
//                 var WebOneSignalKey2 = appInfo2.WebOneSignalApiKey;
//
//                 Guid provisionId = Guid.Empty;
//                 Guid userId = user.Id;
//                 Provision provision = new Provision();
//                 if (provisionId == Guid.Empty)
//                 {
//                     bool shouldCreateUserEmail = !wepdb.UserEmails.Any(w => w.Email.ToLower() == emailId.ToLower() && w.UserId == user.Id);
//
//                     var userEmailExists = wepdb.UserEmails.FirstOrDefault(w => w.Email.ToLower() == emailId.ToLower() && w.UserId == user.Id);
//                     if (shouldCreateUserEmail)
//                     {
//                         userEmailExists = new UserEmail()
//                         {
//                             Id = Guid.NewGuid(),
//                             UserId = userId,
//                             AddedBy = userId.ToString(),
//                             CreatedDate = DateTime.UtcNow,
//                             DisplayName = user.FirstName + " " + user.LastName,
//                             Email = emailId.ToLower(),
//                             IsActive = true,
//                             IsEmailValidated = false,
//                             IsPersonal = true,
//                             ModifiedDate = DateTime.UtcNow,
//                             IsAccessible = false,
//                             IsMultiUser = false,
//                             IsGroupEmail = false,
//                             Status = 0,
//                             EmailType = 0,
//                             IsPreviouslyOwned = false,
//                             IsAddedByUser = true,
//                             IsAcceptedByUser = true
//                         };
//                         wepdb.UserEmails.Add(userEmailExists);
//                         wepdb.SaveChanges();
//                     }
//                     else
//                     {
//                         userEmailExists.IsActive = true;
//                         wepdb.SaveChanges();
//                     }
//                     Guid orgID2 = CommonData.WhatElseCustomerOrgId;
//                     bool isFree = false;
//                     bool isProvisioned2 = true;
//                     bool isTrail2 = false;
//                     bool isAccepted2 = true;
//                     bool isClaimed2 = false;
//                     Guid provisionID = Guid.Empty;
//                     userEmailExists.IsEmailValidated = true;
//                     wepdb.SaveChanges();
//                     var provisionExist = weodb.Provisions.Any(w => w.UserId == user.Id && w.EmailAddress == userEmailExists.Email && w.ProviderId == providerId);
//                     if (provisionID == Guid.Empty && !provisionExist)
//                     {
//                         var userDomain2 = userEmailExists.Email.Split('@');
//                         var domainName2 = userDomain2[1];
//                         if (weddb.Domains.Any(w => w.Address == domainName2 && w.IsActive == true && w.IsFree == true) == true)
//                         {
//                             isFree = true;
//                             isProvisioned2 = true;
//                             isTrail2 = false;
//                             isAccepted2 = true;
//                             isClaimed2 = false;
//                         }
//                         var weOrgProvider = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == providerId && w.IsActive == true);
//                         //var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive).ToList();
//
//                         bool isFreeDomain = weddb.FreeDomains.Any(w => w.DomainName.ToLower() == domainName2.ToLower());
//                         if (isFreeDomain == false)
//                         {
//                             var orgExist2 = weodb.Orgs.Any(w => w.Domains == domainName2);
//                             if (orgExist2 == true)
//                             {
//                                 var org1 = weodb.Orgs.FirstOrDefault(w => w.Domains == domainName2);
//                                 orgID2 = org1.Id;
//                                 isFree = true;
//                                 isProvisioned2 = true;
//                                 isTrail2 = false;
//                                 isAccepted2 = true;
//                                 isClaimed2 = false;
//                                 var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w => w.OrgId == orgID2 && w.UserId == user.Id.ToString());
//                                 if (orgUserExist == null)
//                                 {
//                                     var orgUser = new OrgUser();
//                                     orgUser.Id = Guid.NewGuid();
//                                     orgUser.IsActive = true;
//                                     orgUser.CreatedDate = DateTime.UtcNow;
//                                     orgUser.ActivatedOn = DateTime.UtcNow;
//                                     orgUser.ModifiedDate = DateTime.UtcNow;
//                                     orgUser.OrgId = orgID2;
//                                     orgUser.UserId = user.Id;
//                                     orgUser.IsAdmin = false;
//                                     weodb.OrgUsers.Add(orgUser);
//                                     weodb.SaveChanges();
//
//                                     var orgDirectory = new OrgDirectory();
//                                     orgDirectory.Id = Guid.NewGuid();
//                                     orgDirectory.OrgId = orgID2;
//                                     orgDirectory.CreatedDate = DateTime.UtcNow;
//                                     orgDirectory.ModifiedDate = DateTime.UtcNow;
//                                     orgDirectory.IsActive = true;
//                                     orgDirectory.FirstName = user.FirstName ?? "";
//                                     orgDirectory.MiddleName = user.MiddleName ?? "";
//                                     orgDirectory.LastName = user.LastName ?? "";
//                                     orgDirectory.SanitizedNumber = "";
//                                     orgDirectory.ProvidedNumber = "";
//                                     orgDirectory.Email = user.Email ?? "";
//                                     orgDirectory.Designation = "";
//                                     orgDirectory.Salutation = "";
//                                     orgDirectory.UserId = user.Id.ToString();
//                                     orgDirectory.CountryId = user.CountryId;
//                                     weodb.OrgDirectories.Add(orgDirectory);
//                                     weodb.SaveChanges();
//                                 }
//                                 var orgProviderExist2 = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == orgID2 && w.ProviderId == providerId && w.IsActive == true);
//                                 if (orgProviderExist2 == null)
//                                 {
//                                     var orgProvider1 = new OrgProvider();
//                                     orgProvider1.Id = Guid.NewGuid();
//                                     orgProvider1.CreatedDate = DateTime.UtcNow;
//                                     orgProvider1.ModifiedDate = DateTime.UtcNow;
//                                     orgProvider1.IsActive = true;
//                                     orgProvider1.OrgId = orgID2;
//                                     orgProvider1.ProviderId = providerId;
//                                     orgProvider1.CanSaveForOffline = true;
//                                     orgProvider1.ForceCallLog = true;
//                                     weodb.OrgProviders.Add(orgProvider1);
//                                     weodb.SaveChanges();
//                                     if (weOrgProvider != null)
//                                     {
//                                         var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();
//
//                                         foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
//                                         {
//                                             var orgIdentifier = new OrgIdentifier();
//                                             orgIdentifier.Id = Guid.NewGuid();
//                                             orgIdentifier.OrgProviderId = orgProvider1.Id;
//                                             orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
//                                             orgIdentifier.IsActive = true;
//                                             orgIdentifier.CreatedDate = DateTime.UtcNow;
//                                             orgIdentifier.ModifiedDate = DateTime.UtcNow;
//                                             orgIdentifier.ShowSequence = 1;
//                                             orgIdentifier.Value = weOrgProviderIdentifier.Value;
//                                             weodb.OrgIdentifiers.Add(orgIdentifier);
//                                             weodb.SaveChanges();
//                                         }
//                                     }
//                                 }
//                             }
//                             else
//                             {
//                                 var Country = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
//                                 var org1 = new Org();
//                                 org1.Id = Guid.NewGuid();
//                                 org1.IsBeta = false;
//                                 org1.AppId = CommonData.WEAppId.ToString();
//                                 // org1rg.IsPaid =;
//                                 org1.ModifiedDate = DateTime.UtcNow;
//                                 org1.CreatedDate = DateTime.UtcNow;
//                                 org1.AllowedAccounts = 1;
//                                 org1.Name = domainName2.ToUpper();
//                                 org1.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
//                                 org1.Countries = Country.Name + "|" + Country.Prefix;
//                                 org1.Domains = domainName2;
//                                 org1.IsActive = true;
//                                 org1.IsRestricted = false;
//                                 org1.IsProvider = false;
//                                 org1.IsPurchaser = false;
//                                 weodb.Orgs.Add(org1);
//                                 weodb.SaveChanges();
//                                 orgID2 = org1.Id;
//
//                                 var orgProvider1 = new OrgProvider();
//                                 orgProvider1.Id = Guid.NewGuid();
//                                 orgProvider1.CreatedDate = DateTime.UtcNow;
//                                 orgProvider1.ModifiedDate = DateTime.UtcNow;
//                                 orgProvider1.IsActive = true;
//                                 orgProvider1.OrgId = orgID2;
//                                 orgProvider1.ProviderId = providerId;
//                                 orgProvider1.CanSaveForOffline = true;
//                                 orgProvider1.ForceCallLog = true;
//                                 weodb.OrgProviders.Add(orgProvider1);
//                                 weodb.SaveChanges();
//                                 if (weOrgProvider != null)
//                                 {
//                                     var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();
//                                     foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
//                                     {
//                                         var orgIdentifier = new OrgIdentifier();
//                                         orgIdentifier.Id = Guid.NewGuid();
//                                         orgIdentifier.OrgProviderId = orgProvider1.Id;
//                                         orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
//                                         orgIdentifier.IsActive = true;
//                                         orgIdentifier.CreatedDate = DateTime.UtcNow;
//                                         orgIdentifier.ModifiedDate = DateTime.UtcNow;
//                                         orgIdentifier.ShowSequence = 1;
//                                         orgIdentifier.Value = weOrgProviderIdentifier.Value;
//                                         weodb.OrgIdentifiers.Add(orgIdentifier);
//                                         weodb.SaveChanges();
//                                     }
//                                 }
//                             }
//                         }
//                         else
//                         {
//                             orgID2 = CommonData.WhatElseCustomerOrgId;
//                         }
//                         var provider1 = weddb.Providers.FirstOrDefault(w => w.Id == providerId && w.IsActive == true);
//                         var existingProvision = weodb.Provisions.Any(w => w.AppId == user.AppId && w.EmailAddress == userEmailExists.Email
//                         && w.ProviderId == providerId && w.OrgId == orgID2 && w.UserId == userId);
//                         if (!existingProvision)
//                         {
//                             var provision1 = new Provision();
//                             provision1.Id = Guid.NewGuid();
//                             provision1.OrgId = orgID2; //CommonData.WhatElseCustomerOrgId;
//                             provision1.AppId = user.AppId;
//                             provision1.UserCustomerId = user.ChargebeeCustomerId;
//                             provision1.ProviderTypeId = provider1.ProviderTypeId;
//                             provision1.ProviderId = providerId;
//                             provision1.UserId = user.Id;
//                             provision1.UserProviderId = Guid.Empty;
//                             provision1.CreatedDate = DateTime.UtcNow;
//                             provision1.ModifiedDate = DateTime.UtcNow;
//                             provision1.IsActive = true;
//                             provision1.IsConverted = false;
//                             provision1.IsEnterpriseConverted = false;
//                             //provision.SanitizedNumber = user.SanitizedNumber;
//                             provision1.IsRedeemed = false;
//                             //provision.PhoneNumber = user.ProvidedNumber ?? "";
//                             //provision.Salutation = user.Salutation;
//                             provision1.FirstName = user.FirstName;
//                             provision1.LastName = user.LastName;
//                             provision1.MiddleName = user.MiddleName;
//                             provision1.CountryId = user.CountryId;
//                             provision1.UserId = user.Id;
//                             provision1.IsFree = isFree;
//                             provision1.IsProvisioned = isProvisioned2;
//                             provision1.IsPayed = false;
//                             provision1.IsRequested = false;
//                             provision1.IsAccepted = isAccepted2;
//                             provision1.IsPurchasedByUser = false;
//                             provision1.IsPurchasedOnAndroid = false;
//                             provision1.IsPurchasedOnIos = false;
//                             provision1.EmailAddress = userEmailExists.Email;
//                             provision1.IsClaimed = isClaimed2;
//                             provision1.IsTrial = isTrail2;
//                             weodb.Provisions.Add(provision1);
//                             weodb.SaveChanges();
//
//                             provision = provision1;
//                         }
//                     }
//                     else
//                     {
//                         var provision1 = weodb.Provisions.First(w => w.UserId == user.Id && w.EmailAddress == userEmailExists.Email && w.ProviderId == providerId);
//                         provision1.IsActive = true;
//                         weodb.SaveChanges();
//                         provision = provision1;
//                     }
//                 }
//                 if (emailId.Contains(","))
//                 {
//                     var mailIds = emailId.Split(',');
//                     var emailExist = mailIds.Contains(provision.EmailAddress);
//                     if (emailExist)
//                     {
//                         emailId = provision.EmailAddress;
//                     }
//                     else
//                     {
//                         //  return new Tuple<int, Guid>(3, userProviderId);
//                     }
//                 }
//                 if (provider.Id.ToString().ToLower() == CommonData.ZohoCRMProviderId.ToString().ToLower() || provider.Id.ToString().ToLower() == CommonData.ZohoMailId.ToString().ToLower() || provider.Id.ToString().ToLower() == CommonData.ZohoCalendarId.ToString().ToLower())
//                 {
//                     emailId = provision.EmailAddress;
//                 }
//                 if (provider.Id == CommonData.DynamicCRMProviderId || emailId.ToString().ToLower() == provision.EmailAddress.ToLower())
//                 {
//                     var code = identifierList.FirstOrDefault(w => w.Name.Contains("code") && w.Value != null && w.Value != "").Value;
//                     var userProvider = wepdb.UserProviders.FirstOrDefault(w => w.UserId == userId && w.EmailAddress == emailId.ToString().ToLower() && w.ProviderId == providerId && w.IsActive && w.IsRelogginRequired == false && w.AppId == user.AppId);
//                     userProvider.ProvisionId = provision.Id;
//                     wepdb.SaveChanges();
//                     //if (userProvider != null)
//                     //{
//                     TelegramService.SendMessageToTestBot("UserProvider Id: " + userProvider.Id.ToString());
//                     TelegramService.SendMessageToTestBot("Provision Id: " + provision.Id.ToString());
//                     var userProviderProvision = wepdb.UserProviders.FirstOrDefault(w => w.UserId == userId && w.ProvisionId == provision.Id && w.ProviderId == providerId && w.IsActive && w.EmailAddress == emailId.ToString().ToLower() && w.AppId == user.AppId);//
//
//                     TelegramService.SendMessageToTestBot("UserProvider Provision: " + userProviderProvision.Id.ToString());
//                     bool isMonthly = true;
//                     DateTime start, end;
//                     start = DateTime.UtcNow;
//                     end = DateTime.UtcNow.AddDays(30);
//                     if (provision.IsPayed == true)
//                     {
//
//                         var split = provision.ProductId.Split('.');
//                         isMonthly = (split[3] == "m") ? true : false;
//                         if (isMonthly == true)
//                         {
//                             start = DateTime.UtcNow;
//                             end = DateTime.UtcNow.AddDays(30);
//                         }
//                         else
//                         {
//                             start = DateTime.UtcNow;
//                             end = DateTime.UtcNow.AddYears(1);
//                         }
//                     }
//                     if (userProvider != null || userProviderProvision != null)
//                     {
//                         var upid = (userProvider != null) ? userProvider.Id : (userProviderProvision != null) ? userProviderProvision.Id : Guid.Empty;
//                         if (upid != Guid.Empty)
//                         {
//                             var up = wepdb.UserProviders.FirstOrDefault(w => w.Id == upid);
//                             //   userProviderId = up.Id;
//                             up.AppId = user.AppId;
//                             up.OrgId = provision.OrgId;
//                             up.ModifiedDate = DateTime.UtcNow;
//                             up.ModifiedDate = DateTime.UtcNow;
//                             up.EmailAddress = emailId.ToString().ToLower();
//                             up.IsProvisioned = provision.IsProvisioned;
//                             up.IsPayed = provision.IsPayed;
//                             up.IsFree = provision.IsFree;
//                             up.ProvisionId = provision.Id;
//                             up.IsAuthenticated = true;
//                             //userProvider.AccountId = 
//                             up.IsActive = true;
//                             up.Source = provision.Source ?? 0;
//                             up.PurchaseId = provision.PurchaseId ?? "";
//                             up.PurchaseState = provision.PurchaseState ?? 0;
//                             up.ProductId = provision.ProductId ?? "";
//                             up.IsRelogginRequired = false;
//                             up.Priority = provider.ProviderType.Priority ?? 100;
//                             wepdb.SaveChanges();
//                             //foreach (var identifier in identifierList)
//                             //{
//                             //    var userProviderIdentifierExist = wepdb.UserProviderIdentifiers.FirstOrDefault(w => w.UserProviderId == userProvider.Id && w.IdentifierId==identifier.Id);
//                             //    if (identifier.Value != null && identifier.Value != "")
//                             //    {
//                             //        userProviderIdentifierExist.IdentifierId = identifier.Id;
//                             //        userProviderIdentifierExist.UserProviderId = userProvider.Id;
//                             //        userProviderIdentifierExist.Value = identifier.Value;
//                             //        userProviderIdentifierExist.IsActive = true;
//                             //        userProviderIdentifierExist.ModifiedDate = DateTime.UtcNow;
//                             //        wepdb.SaveChanges();
//                             //    }
//                             //}
//                             wepdb.UserProviderIdentifiers.Where(w => w.UserProviderId == up.Id).ToList().ForEach(i => i.IsActive = false);
//                             foreach (var identifier in identifierList)
//                             {
//                                 try
//                                 {
//
//                                     //var identifierExist = weddb.Identifiers.FirstOrDefault(w => w.Id.ToString() == UPIdentifierKeyValue.Key && w.ProviderId == providerId);
//                                     if (identifier.Value != null && identifier.Value != "")
//                                     {
//                                         var upIdentifiers = new UserProviderIdentifier();
//                                         upIdentifiers.Id = Guid.NewGuid();
//                                         upIdentifiers.IdentifierId = identifier.Id;
//                                         upIdentifiers.UserProviderId = up.Id;
//                                         upIdentifiers.Value = identifier.Value;
//                                         upIdentifiers.IsActive = true;
//                                         upIdentifiers.CreatedDate = DateTime.UtcNow;
//                                         upIdentifiers.ModifiedDate = DateTime.UtcNow;
//                                         wepdb.UserProviderIdentifiers.Add(upIdentifiers);
//                                         wepdb.SaveChanges();
//                                     }
//                                 }
//                                 catch (Exception ex)
//                                 {
//                                     continue;
//                                     // throw;
//                                 }
//                             }
//
//                             var exsitingProvision = weodb.Provisions.FirstOrDefault(w => w.Id == provision.Id);
//                             exsitingProvision.IsRedeemed = true;
//                             exsitingProvision.UserProviderId = up.Id;
//                             exsitingProvision.ProviderId = providerId;
//                             exsitingProvision.EmailAddress = emailId.ToLower();
//                             exsitingProvision.IsTrial = Convert.ToBoolean(provision.IsTrial);
//                             weodb.SaveChanges();
//                         }
//                     }
//                     else
//                     {
//                         var newUserProvider = new UserProvider();
//                         newUserProvider.Id = Guid.NewGuid();
//                         newUserProvider.UserId = userId;
//                         newUserProvider.AppId = user.AppId;
//                         newUserProvider.OrgId = provision.OrgId;
//                         newUserProvider.ProviderId = providerId;
//                         newUserProvider.EmailAddress = emailId.ToLower();
//                         newUserProvider.IsAuthenticated = true;
//                         newUserProvider.IsActive = true;
//                         newUserProvider.Code = code ?? "";
//                         newUserProvider.ActiveFrom = DateTime.UtcNow;
//                         newUserProvider.ActiveTill = DateTime.UtcNow.AddYears(1);
//                         newUserProvider.IsProvisioned = provision.IsProvisioned;
//                         newUserProvider.ProvisionId = provision.Id;
//                         newUserProvider.IsFree = provision.IsFree;
//                         newUserProvider.IsPayed = provision.IsPayed;
//                         // newUserProvider.AccountId = token.id;
//                         newUserProvider.IsFullSyncDone = false;
//                         newUserProvider.Source = provision.Source ?? 0;
//                         newUserProvider.PurchaseId = provision.PurchaseId ?? "";
//                         newUserProvider.PurchaseState = provision.PurchaseState ?? 0;
//                         newUserProvider.ProductId = provision.ProductId ?? "";
//                         newUserProvider.CreatedDate = DateTime.UtcNow;
//                         newUserProvider.ModifiedDate = DateTime.UtcNow;
//                         newUserProvider.IsRelogginRequired = false;
//                         newUserProvider.Priority = provider.ProviderType.Priority ?? 100;
//                         //newUserProvider.InstanceUrl = token.instance_url;
//                         int trialDays = 30;
//                         //if (provision.IsTrial == true)
//                         //{
//                         //    newUserProvider.IsTrial = true;
//                         //    newUserProvider.TrialStart = DateTime.UtcNow;
//                         //    newUserProvider.TrialEnd = DateTime.UtcNow.AddDays(trialDays);
//                         //}
//                         //else
//                         //{
//                         newUserProvider.IsTrial = false;
//                         //}
//                         wepdb.UserProviders.Add(newUserProvider);
//                         wepdb.SaveChanges();
//                         if (provider.Name == "Pipedrive ")
//                         {
//                             if (identifierList.Select(w => w.Name).Contains("pdcompany_domain"))
//                             {
//                                 var companyInstanceName = "pdcompany_domain";
//                                 var companyDomain = identifierList.Where(w => w.Name == companyInstanceName).FirstOrDefault().Value;
//                                 var instanceString = "pdinstanceurl";
//                                 var instaceUrl = "https://" + companyDomain + ".pipedrive.com";
//                                 identifierList.First(w => w.Name == instanceString).Value = instaceUrl;
//                             }
//                         }
//                         try
//                         {
//                             if (provider.Id == CommonData.SalesforceProviderId)
//                             {
//                                 if (identifierList.Any(w => w.Name == "sflightningurl") && !string.IsNullOrEmpty(identifierList.First(x => x.Name == "sfinstanceurl").Value))
//                                 {
//                                     if (string.IsNullOrEmpty(identifierList.First(w => w.Name == "sflightningurl").Value))
//                                     {
//                                         string instanceUrl = identifierList.First(x => x.Name == "sfinstanceurl").Value;
//                                         string instance = instanceUrl.Substring(instanceUrl.IndexOf("https://") + 8, instanceUrl.IndexOf(".my.") - 8);
//                                         string lightningUrl = $"https://{instance}.lightning.force.com";
//                                         identifierList.First(w => w.Name == "sflightningurl").Value = lightningUrl;
//                                     }
//                                 }
//                             }
//                         }
//                         catch (Exception ex)
//                         {
//                             Console.WriteLine(ex.ToString());
//                         }
//                         foreach (var identifier in identifierList)
//                         {
//                             try
//                             {
//                                 if (identifier.Value != null && identifier.Value != "")
//                                 {
//
//                                     var upIdentifiers = new UserProviderIdentifier();
//                                     upIdentifiers.Id = Guid.NewGuid();
//                                     upIdentifiers.IdentifierId = identifier.Id;
//                                     upIdentifiers.UserProviderId = newUserProvider.Id;
//                                     upIdentifiers.Value = identifier.Value;
//                                     upIdentifiers.IsActive = true;
//                                     upIdentifiers.CreatedDate = DateTime.UtcNow;
//                                     upIdentifiers.ModifiedDate = DateTime.UtcNow;
//                                     wepdb.UserProviderIdentifiers.Add(upIdentifiers);
//                                     wepdb.SaveChanges();
//                                 }
//                             }
//                             catch (Exception ex)
//                             {
//                                 continue;
//                                 // throw;
//                             }
//                         }
//                         wepdb.SaveChanges();
//                         var exsitingProvision = weodb.Provisions.FirstOrDefault(w => w.Id == provision.Id);
//                         exsitingProvision.IsRedeemed = true;
//                         exsitingProvision.UserProviderId = newUserProvider.Id;
//                         exsitingProvision.ProviderId = providerId;
//                         exsitingProvision.EmailAddress = emailId.ToLower();
//                         exsitingProvision.IsTrial = Convert.ToBoolean(provision.IsTrial);
//                         weodb.SaveChanges();
//                         //userProviderId = newUserProvider.Id;
//
//                         if (user.ChargebeeCustomerId != null)
//                         {
//                             try
//                             {
//                                 var msg = provider.Name + " LoggedIn-" + newUserProvider.EmailAddress;
//                                 ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
//                                 EntityResult result = Customer.Retrieve(user.ChargebeeCustomerId).Request();
//                                 Customer customer = result.Customer;
//                                 if (customer != null)
//                                 {
//                                     EntityResult result1 = Comment.Create()
//                                        .EntityId(user.ChargebeeCustomerId)
//                                        .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
//                                        .Notes(msg)
//                                        .Request();
//                                 }
//                             }
//                             catch (Exception ex)
//                             {
//                             }
//                         }
//                     }
//
//                     var userEmailExist = wepdb.UserEmails.FirstOrDefault(w => w.Email.ToLower() == emailId && w.UserId == userId);
//                     if (userEmailExist == null && emailId != "")
//                     {
//                         var userEmail = new UserEmail();
//                         userEmail.Id = Guid.NewGuid();
//                         userEmail.Email = emailId ?? "";
//                         userEmail.IsActive = true;
//                         userEmail.UserId = userId;
//                         userEmail.IsPersonal = true;
//                         userEmail.CreatedDate = DateTime.UtcNow;
//                         userEmail.ModifiedDate = DateTime.UtcNow;
//                         userEmail.IsEmailValidated = true;
//                         userEmail.IsAccessible = true;
//                         userEmail.IsMultiUser = true;
//                         userEmail.IsGroupEmail = false;
//                         userEmail.Status = 1;
//                         userEmail.IsAddedByUser = true;
//                         userEmail.IsAcceptedByUser = false;
//                         wepdb.UserEmails.Add(userEmail);
//                     }
//                     else
//                     {
//                         userEmailExist.IsEmailValidated = true;
//                     }
//                     wepdb.SaveChanges();
//                     if (provision.IsProvisioned == true)// && provision.OrgId != CommonData.WhatElseOrgId)
//                     {
//                         var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w => w.OrgId == provision.OrgId && w.UserId == userId.ToString());
//                         if (orgUserExist == null)
//                         {
//                             var orgUser = new OrgUser();
//                             orgUser.Id = Guid.NewGuid();
//                             orgUser.IsActive = true;
//                             orgUser.CreatedDate = DateTime.UtcNow;
//                             // orgUser.DeactivatedOn =;
//                             orgUser.ActivatedOn = DateTime.UtcNow;
//                             orgUser.ModifiedDate = DateTime.UtcNow;
//                             orgUser.OrgId = provision.OrgId;
//                             orgUser.UserId = userId;
//                             orgUser.IsAdmin = false;
//                             weodb.OrgUsers.Add(orgUser);
//                             weodb.SaveChanges();
//
//                             var orgDirectory = new OrgDirectory();
//                             orgDirectory.Id = Guid.NewGuid();
//                             orgDirectory.OrgId = provision.OrgId;
//                             orgDirectory.CreatedDate = DateTime.UtcNow;
//                             orgDirectory.ModifiedDate = DateTime.UtcNow;
//                             orgDirectory.IsActive = true;
//                             orgDirectory.FirstName = user.FirstName ?? "";
//                             orgDirectory.MiddleName = user.MiddleName ?? "";
//                             orgDirectory.LastName = user.LastName ?? "";
//                             orgDirectory.Email = user.Email ?? "";
//                             orgDirectory.SanitizedNumber = "";
//                             orgDirectory.ProvidedNumber = "";
//                             orgDirectory.UserId = user.Id.ToString();
//                             orgDirectory.CountryId = user.CountryId;
//                             weodb.OrgDirectories.Add(orgDirectory);
//                             weodb.SaveChanges();
//                             weodb.Provisions.FirstOrDefault(w => w.Id == provision.Id).OrgDirectoryId = orgDirectory.Id;
//                             weodb.SaveChanges();
//                         }
//                     }
//                     wepdb.SaveChanges();
//                     weodb.SaveChanges();
//                     weadb.SaveChanges();
//                     //}
//                 }
//                 else
//                 {
//                     // return new Tuple<int, Guid>(2, userProviderId);
//                 }
//                 // return new Tuple<int, Guid>(1, userProviderId);
//             }
//             catch (Exception ex)
//             {
//                 return BadRequest(ex.ToString());
//             }
//             return Ok();
//         }
//         public class SSOTokenModel
//         {
//             public string Token { get; set; }
//         }
//         private string FindAndReplaceIndentifiers(string finalURL)
//         {
//             foreach (var item in identifierList)
//             {
//                 if (item.Value != "" && item.Value != null)
//                 {
//                     finalURL = finalURL.Replace("{" + item.Name + "}", item.Value);
//                 }
//             }
//             foreach (var orgIdentifier in orgProviderIdentifierList)
//             {
//                 var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == orgIdentifier.IdentifierId).Name;
//                 finalURL = finalURL.Replace("{" + identifierName + "}", orgIdentifier.Value);
//             }
//             finalURL = finalURL.Replace("\r\n", "");
//             return finalURL;
//         }
//
//         public async Task<bool> PostToken(string token, Url providerUrl)
//         {
//             try
//             {
//                 var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
//                 foreach (var outputIdentifier in outputIndentifiers)
//                 {
//                     var keyData = outputIdentifier.Split('|');
//                     // var selectedToken = jtoken.SelectToken(keyData[1]);
//                     //if (selectedToken != null)
//                     //{
//                     //  string tokenValue = selectedToken.Value<string>();
//                     foreach (var identifier in identifierList)
//                     {
//                         if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                         {
//                             identifier.Value = token;
//                             TelegramService.SendMessageToTestBot(token.ToString());
//                         }
//                     }
//                     //}
//                 }
//             }
//             catch (Exception ex)
//             {
//                 TelegramService.SendMessageToTestBot(ex.StackTrace.ToString());
//                 TelegramService.SendMessageToTestBot(ex.ToString());
//                 return false;
//             }
//             return true;
//         }
//         public async Task<bool> GetClient(string finalURL, Url providerUrl, string dataToReplace)
//         {
//             try
//             {
//                 var urlPart1 = finalURL;
//                 HttpClient client = new HttpClient();
//                 HttpContent content = new StringContent(String.Empty);
//                 content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
//                 foreach (var item in headersList)
//                 {
//                     if (item.Value.Contains('{'))
//                     {
//                         item.Value = FindAndReplaceIndentifiers(item.Value);
//                     }
//                     if (item.Name == "Content-Type")
//                         content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
//                     else
//                         client.DefaultRequestHeaders.Add(item.Name, item.Value);
//                 }
//                 TelegramService.SendMessageToTestBot("Get Method calll!");
//                 using (HttpResponseMessage response1 = await client.GetAsync(urlPart1))
//                 {
//                     TelegramService.SendMessageToTestBot(response1.StatusCode.ToString());
//                     TelegramService.SendMessageToTestBot(response1.RequestMessage.ToString());
//                     if (response1.IsSuccessStatusCode)
//                     {
//                         try
//                         {
//                             var json = await response1.Content.ReadAsStringAsync();
//                             CommonData.sbnew.AppendLine("me api response: " + json);
//                             JToken jtoken = JToken.Parse(json);
//                             var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
//                             foreach (var outputIdentifier in outputIndentifiers)
//                             {
//                                 JToken selectedToken;
//                                 List<JToken> tokenList = new List<JToken>();
//                                 var keyData = outputIdentifier.Split('|');
//                                 if (keyData[1].Contains(".."))
//                                 {
//                                     var list = jtoken.SelectTokens(keyData[1]).Values<string>().ToList();
//                                     string tokenValues = "";
//                                     foreach (var item in list.ToList())
//                                     {
//                                         string tokenValue = item;
//                                         tokenValues += tokenValue + ",";
//                                     }
//                                     if (tokenValues.Length > 0)
//                                         tokenValues = tokenValues.Substring(0, tokenValues.Length - 1);
//                                     foreach (var identifier in identifierList)
//                                     {
//                                         if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                                         {
//                                             identifier.Value = tokenValues;
//                                         }
//                                     }
//                                 }
//                                 else
//                                 {
//                                     selectedToken = jtoken.SelectToken(keyData[1]);
//                                     if (selectedToken != null)
//                                     {
//                                         if (selectedToken.Type == JTokenType.Array)
//                                         {
//                                             var tokens = selectedToken.Values();
//                                             string tokenValues = "";
//                                             foreach (var item in tokens)
//                                             {
//                                                 string tokenValue = item.Value<string>();
//                                                 tokenValues += tokenValue + ",";
//                                             }
//                                             if (tokenValues.Length > 0)
//                                                 tokenValues = tokenValues.Substring(0, tokenValues.Length - 1);
//                                             foreach (var identifier in identifierList)
//                                             {
//                                                 if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                                                 {
//                                                     identifier.Value = tokenValues;
//                                                 }
//                                             }
//                                         }
//                                         else
//                                         {
//                                             string tokenValue = selectedToken.Value<string>();
//                                             foreach (var identifier in identifierList)
//                                             {
//                                                 if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                                                 {
//                                                     identifier.Value = tokenValue;
//                                                 }
//                                             }
//                                         }
//                                     }
//                                 }
//                             }
//                         }
//                         catch (Exception ex)
//                         {
//
//                         }
//                     }
//                     else
//                     {
//                         PropertyModels.DataModels.ErrorMailModel errorMailModel = new PropertyModels.DataModels.ErrorMailModel();
//                         errorMailModel.Url = "server" + finalURL;
//                         errorMailModel.Count = 0;
//                         errorMailModel.CreatedDate = DateTime.UtcNow;
//                         errorMailModel.ErrorCode = response1.StatusCode.ToString();
//                         if (response1.RequestMessage.Content == null)
//                         {
//                             errorMailModel.ErrorMessage = errorMailModel.ErrorCode;
//                         }
//                         else
//                         {
//                             errorMailModel.ErrorMessage = response1.RequestMessage.Content.ToString();
//                         }
//                         errorMailModel.UserProviderId = Guid.Empty;
//                         //   errorMailModel.ProvisionId = Guid.Parse(appProvision);
//                         errorMailModel.Payload = dataToReplace ?? "";
//                         errorMailModel.Email = "Didn't find emailid";
//                         var mail = EmailServices.SendGridErrorMail(errorMailModel);
//                     }
//                 }
//             }
//             catch (Exception ex)
//             {
//                 TelegramService.SendMessageToTestBot(ex.ToString());
//                 TelegramService.SendMessageToTestBot(ex.StackTrace.ToString());
//                 return false;
//             }
//             return true;
//         }
//     }
// }
