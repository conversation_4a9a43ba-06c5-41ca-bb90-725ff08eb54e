using System.Diagnostics;
using Microsoft.ApplicationInsights.DataContracts;

namespace OnePage.Connect.Interfaces;


    public interface ITelemetryTracker
    {
        void TrackException(Exception ex);
        void TrackTrace(string message, SeverityLevel severity);
        void TrackEvent(string eventName, Dictionary<string, string> properties);
        Stopwatch StartTrackRequest(string requestName);
        void StopTrackRequest(string requestName, Stopwatch stopwatch);
    }

