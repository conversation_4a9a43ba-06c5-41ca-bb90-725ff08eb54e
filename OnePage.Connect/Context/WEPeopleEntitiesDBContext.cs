﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using OnePage.Connect;
using OnePage.Connect.Migrations;
using Action = System.Action;

namespace OnePage.Connect.Context;

public partial class WEPeopleEntitiesDBContext : DbContext
{
    public WEPeopleEntitiesDBContext()
    {
    }

    public WEPeopleEntitiesDBContext(DbContextOptions<WEPeopleEntitiesDBContext> options)
        : base(options)
    {
    }

    public virtual DbSet<OnePage.Connect.Migrations.Action> Actions { get; set; }

    public virtual DbSet<ActionUrl> ActionUrls { get; set; }

    public virtual DbSet<ActivityType> ActivityTypes { get; set; }

    public virtual DbSet<AdView> AdViews { get; set; }

    public virtual DbSet<Answer> Answers { get; set; }

    public virtual DbSet<AppProvider> AppProviders { get; set; }

    public virtual DbSet<AppProviderType> AppProviderTypes { get; set; }

    public virtual DbSet<BotDatum> BotData { get; set; }

    public virtual DbSet<Card> Cards { get; set; }

    public virtual DbSet<CardUrl> CardUrls { get; set; }

    public virtual DbSet<Conference> Conferences { get; set; }

    public virtual DbSet<Country> Countries { get; set; }

    public virtual DbSet<Coupon> Coupons { get; set; }

    public virtual DbSet<CreditActivity> CreditActivities { get; set; }

    public virtual DbSet<CreditTask> CreditTasks { get; set; }

    public virtual DbSet<DemoRequest> DemoRequests { get; set; }

    public virtual DbSet<DeviceType> DeviceTypes { get; set; }

    public virtual DbSet<Discover> Discovers { get; set; }

    public virtual DbSet<Domain> Domains { get; set; }

    public virtual DbSet<DomainBlacklist> DomainBlacklists { get; set; }

    public virtual DbSet<EmailWhiteList> EmailWhiteLists { get; set; }

    public virtual DbSet<FeatureTrigger> FeatureTriggers { get; set; }

    public virtual DbSet<FieldsToDisplay> FieldsToDisplays { get; set; }

    public virtual DbSet<GetToKnow> GetToKnows { get; set; }

    public virtual DbSet<Header> Headers { get; set; }

    public virtual DbSet<Help> Helps { get; set; }

    public virtual DbSet<Identifier> Identifiers { get; set; }

    public virtual DbSet<InApp> InApps { get; set; }

    public virtual DbSet<Language> Languages { get; set; }

    public virtual DbSet<LastSeen> LastSeens { get; set; }

    public virtual DbSet<Lookup> Lookups { get; set; }

    public virtual DbSet<Micontact> Micontacts { get; set; }

    public virtual DbSet<Minature> Minatures { get; set; }

    public virtual DbSet<Miorg> Miorgs { get; set; }

    public virtual DbSet<Module> Modules { get; set; }

    public virtual DbSet<MorgCreditUsuage> MorgCreditUsuages { get; set; }

    public virtual DbSet<MorgOption> MorgOptions { get; set; }

    public virtual DbSet<Morganisation> Morganisations { get; set; }

    public virtual DbSet<MoukhaReview> MoukhaReviews { get; set; }

    public virtual DbSet<Mperson> Mpeople { get; set; }

    public virtual DbSet<Offer> Offers { get; set; }

    public virtual DbSet<OfferSetting> OfferSettings { get; set; }

    public virtual DbSet<Option> Options { get; set; }

    public virtual DbSet<OrgNature> OrgNatures { get; set; }

    public virtual DbSet<PhoneConfig> PhoneConfigs { get; set; }

    public virtual DbSet<Post> Posts { get; set; }

    public virtual DbSet<Provider> Providers { get; set; }

    public virtual DbSet<ProviderIdentifier> ProviderIdentifiers { get; set; }

    public virtual DbSet<ProviderType> ProviderTypes { get; set; }

    public virtual DbSet<ProviderUrl> ProviderUrls { get; set; }

    public virtual DbSet<Question> Questions { get; set; }

    public virtual DbSet<Question1> Questions1 { get; set; }

    public virtual DbSet<QuestionType> QuestionTypes { get; set; }

    public virtual DbSet<Recommend> Recommends { get; set; }

    public virtual DbSet<Referral> Referrals { get; set; }

    public virtual DbSet<RoleTemplate> RoleTemplates { get; set; }

    public virtual DbSet<Setting> Settings { get; set; }

    public virtual DbSet<Signal> Signals { get; set; }

    public virtual DbSet<SignalMode> SignalModes { get; set; }

    public virtual DbSet<SignalType> SignalTypes { get; set; }

    public virtual DbSet<SocialType> SocialTypes { get; set; }

    public virtual DbSet<Sso> Ssos { get; set; }

    public virtual DbSet<Sync> Syncs { get; set; }

    public virtual DbSet<Translate> Translates { get; set; }

    public virtual DbSet<Trigger> Triggers { get; set; }

    public virtual DbSet<Url> Urls { get; set; }

    public virtual DbSet<Urltype> Urltypes { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<UserCredit> UserCredits { get; set; }

    public virtual DbSet<UserDevice> UserDevices { get; set; }

    public virtual DbSet<UserEmail> UserEmails { get; set; }

    public virtual DbSet<UserIm> UserIms { get; set; }

    public virtual DbSet<UserPhone> UserPhones { get; set; }

    public virtual DbSet<UserProvider> UserProviders { get; set; }

    public virtual DbSet<UserProviderIdentifier> UserProviderIdentifiers { get; set; }

    public virtual DbSet<UserProvideridentifierjoin> UserProvideridentifierjoins { get; set; }

    public virtual DbSet<UserRole> UserRoles { get; set; }

    public virtual DbSet<UserSetting> UserSettings { get; set; }

    public virtual DbSet<UserSocial> UserSocials { get; set; }

    public virtual DbSet<UserStatus> UserStatuses { get; set; }

    public virtual DbSet<UserStorage> UserStorages { get; set; }

    public virtual DbSet<UserTask> UserTasks { get; set; }

    public virtual DbSet<Userdeviceview> Userdeviceviews { get; set; }

    public virtual DbSet<UserproviderApiView> UserproviderApiViews { get; set; }

    public virtual DbSet<Usersettingview> Usersettingviews { get; set; }

    public virtual DbSet<VUserDevice> VUserDevices { get; set; }

//     protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
// #warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
//         => optionsBuilder.UseSqlServer("Server=eu-ne-sd.database.windows.net;Database=eu-fc-ss-pp;User=eu-ne-sd;Password=**************;MultipleActiveResultSets=true; ");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<OnePage.Connect.Migrations.Action>(entity =>
        {
            entity.ToTable("Action");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Command).HasMaxLength(250);
            entity.Property(e => e.CommandParameter).HasMaxLength(250);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Icon).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsPostCallAction).HasDefaultValue(false);
            entity.Property(e => e.IsVisible).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Note).HasMaxLength(50);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.Title).HasMaxLength(100);
            entity.Property(e => e.VisibilityConditions).HasMaxLength(1000);
        });

        modelBuilder.Entity<ActionUrl>(entity =>
        {
            entity.ToTable("ActionURL");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Action).WithMany(p => p.ActionUrls)
                .HasForeignKey(d => d.ActionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ActionURL_Action");

            entity.HasOne(d => d.Url).WithMany(p => p.ActionUrls)
                .HasForeignKey(d => d.UrlId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ActionURL_URL");
        });

        modelBuilder.Entity<ActivityType>(entity =>
        {
            entity.ToTable("ActivityType");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("(sysutcdatetime())");
            entity.Property(e => e.Notes).HasMaxLength(300);
        });

        modelBuilder.Entity<AdView>(entity =>
        {
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<Answer>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Answer1)
                .HasMaxLength(500)
                .HasColumnName("Answer");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.Question).WithMany(p => p.Answers)
                .HasForeignKey(d => d.QuestionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Answers_Questions");

            entity.HasOne(d => d.User).WithMany(p => p.Answers)
                .HasForeignKey(d => d.UserId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Answers_User");
        });

        modelBuilder.Entity<AppProvider>(entity =>
        {
            entity.ToTable("AppProvider");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Createddate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.Provider).WithMany(p => p.AppProviders)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AppProvider_Provider");
        });

        modelBuilder.Entity<AppProviderType>(entity =>
        {
            entity.ToTable("AppProviderType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Createddate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<BotDatum>(entity =>
        {
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Data).HasMaxLength(1000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<Card>(entity =>
        {
            entity.ToTable("Card");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ActionCommand).HasMaxLength(50);
            entity.Property(e => e.ActionName).HasMaxLength(50);
            entity.Property(e => e.ChartType).HasDefaultValue(0);
            entity.Property(e => e.CountPath).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataPath).HasMaxLength(1000);
            entity.Property(e => e.DetailPath).HasMaxLength(1000);
            entity.Property(e => e.Entity).HasMaxLength(1000);
            entity.Property(e => e.FieldsToShow).HasMaxLength(1000);
            entity.Property(e => e.HasActions).HasDefaultValue(true);
            entity.Property(e => e.Icon).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsGlobal).HasDefaultValue(true);
            entity.Property(e => e.IsList).HasDefaultValue(false);
            entity.Property(e => e.ListToBind).HasMaxLength(50);
            entity.Property(e => e.MaxHeight).HasDefaultValue(250);
            entity.Property(e => e.ModelToBind).HasMaxLength(50);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Note).HasMaxLength(50);
            entity.Property(e => e.ShowCounter).HasDefaultValue(true);
            entity.Property(e => e.ShowFooter).HasDefaultValue(true);
            entity.Property(e => e.ShowHeader).HasDefaultValue(true);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.Title).HasMaxLength(100);
        });

        modelBuilder.Entity<CardUrl>(entity =>
        {
            entity.ToTable("CardURL");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);

            entity.HasOne(d => d.Card).WithMany(p => p.CardUrls)
                .HasForeignKey(d => d.CardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CardURL_Card");

            entity.HasOne(d => d.Url).WithMany(p => p.CardUrls)
                .HasForeignKey(d => d.UrlId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CardURL_URL");
        });

        modelBuilder.Entity<Conference>(entity =>
        {
            entity.ToTable("Conference");

            entity.Property(e => e.DialIn).HasMaxLength(1000);
            entity.Property(e => e.Dtmf)
                .HasMaxLength(50)
                .HasColumnName("DTMF");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.OneClick).HasMaxLength(100);
            entity.Property(e => e.Sipurl)
                .HasMaxLength(50)
                .HasColumnName("SIPUrl");
            entity.Property(e => e.Source).HasMaxLength(50);
            entity.Property(e => e.Url).HasMaxLength(1000);
        });

        modelBuilder.Entity<Country>(entity =>
        {
            entity.ToTable("Country");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Alpha2).HasMaxLength(2);
            entity.Property(e => e.Alpha3).HasMaxLength(3);
            entity.Property(e => e.Continent).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IntlPrefix).HasMaxLength(10);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Prefix).HasMaxLength(10);
            entity.Property(e => e.ServerZone).HasDefaultValue((byte)1);
        });

        modelBuilder.Entity<Coupon>(entity =>
        {
            entity.ToTable("Coupon");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Count).HasDefaultValue(1);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);

            entity.HasOne(d => d.Offer).WithMany(p => p.Coupons)
                .HasForeignKey(d => d.OfferId)
                .HasConstraintName("FK_Coupon_Offer");
        });

        modelBuilder.Entity<CreditActivity>(entity =>
        {
            entity.ToTable("CreditActivity");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Credit).HasDefaultValue(0);
            entity.Property(e => e.CreditRedeemedDate).HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsCredited).HasDefaultValueSql("('0')");
            entity.Property(e => e.IsDebited).HasDefaultValueSql("('0')");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Payload).HasMaxLength(500);
            entity.Property(e => e.ProductId).HasMaxLength(100);
            entity.Property(e => e.ReceiptId).HasMaxLength(100);
            entity.Property(e => e.Redeemdate)
                .HasDefaultValueSql("((1))")
                .HasColumnType("datetime");
            entity.Property(e => e.RedeemedNonVanishngCredit).HasDefaultValue(0);
            entity.Property(e => e.RedeemedVanishngCredit).HasDefaultValue(0);
            entity.Property(e => e.TransactionDateUtc)
                .HasColumnType("datetime")
                .HasColumnName("TransactionDateUTC");
            entity.Property(e => e.VanishingCredits).HasDefaultValue(0);
        });

        modelBuilder.Entity<CreditTask>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Task");

            entity.ToTable("CreditTask");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Credits).HasDefaultValue(1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsOneTime).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
        });

        modelBuilder.Entity<DemoRequest>(entity =>
        {
            entity.ToTable("DemoRequest");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CompanyName).HasMaxLength(50);
            entity.Property(e => e.CountryId).HasMaxLength(2);
            entity.Property(e => e.Createddate).HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.FirstName).HasMaxLength(500);
            entity.Property(e => e.IdpproviderUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderUrl");
            entity.Property(e => e.IdpsandboxUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPSandboxUrl");
            entity.Property(e => e.LastName).HasMaxLength(500);
            entity.Property(e => e.MiddleName).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.Ptype)
                .HasDefaultValue(1)
                .HasColumnName("PType");
            entity.Property(e => e.Salutation).HasMaxLength(100);
        });

        modelBuilder.Entity<DeviceType>(entity =>
        {
            entity.ToTable("DeviceType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);
        });

        modelBuilder.Entity<Discover>(entity =>
        {
            entity.ToTable("Discover");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Action1).HasMaxLength(50);
            entity.Property(e => e.Action2).HasMaxLength(50);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Detail).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Title).HasMaxLength(500);
            entity.Property(e => e.TriggerValue).HasMaxLength(50);
            entity.Property(e => e.Video).HasMaxLength(50);

            entity.HasOne(d => d.Setting).WithMany(p => p.Discovers)
                .HasForeignKey(d => d.SettingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Discover_Setting");
        });

        modelBuilder.Entity<Domain>(entity =>
        {
            entity.ToTable("Domain");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Address).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsFree).HasDefaultValue(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
        });

        modelBuilder.Entity<DomainBlacklist>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_FreeDomain");

            entity.ToTable("DomainBlacklist");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.DomainName).HasMaxLength(1000);
        });

        modelBuilder.Entity<EmailWhiteList>(entity =>
        {
            entity.ToTable("EmailWhiteList");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(100);
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Phone).HasMaxLength(50);
        });

        modelBuilder.Entity<FeatureTrigger>(entity =>
        {
            entity.ToTable("FeatureTrigger");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Action).HasMaxLength(500);
            entity.Property(e => e.Action2).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsFeature).HasDefaultValue(true);
            entity.Property(e => e.Message).HasMaxLength(500);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.PageName).HasMaxLength(400);
        });

        modelBuilder.Entity<FieldsToDisplay>(entity =>
        {
            entity.ToTable("FieldsToDisplay");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Condition).HasMaxLength(500);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DisplayName).HasMaxLength(1000);
            entity.Property(e => e.Entity).HasMaxLength(500);
            entity.Property(e => e.ExtLink).HasMaxLength(1000);
            entity.Property(e => e.Icon).HasMaxLength(50);
            entity.Property(e => e.IntLink).HasMaxLength(1000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsDirect).HasDefaultValue(false);
            entity.Property(e => e.IsPhone).HasDefaultValue(false);
            entity.Property(e => e.ModelAttribute).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.NameMatch).HasMaxLength(1500);
            entity.Property(e => e.OrgId).HasMaxLength(1000);
            entity.Property(e => e.OrgRoleId).HasMaxLength(1000);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.Type).HasDefaultValue((byte)1);
            entity.Property(e => e.ValueMatch).HasMaxLength(1500);

            entity.HasOne(d => d.Card).WithMany(p => p.FieldsToDisplays)
                .HasForeignKey(d => d.CardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_FieldsToDisplay_Card");
        });

        modelBuilder.Entity<GetToKnow>(entity =>
        {
            entity.ToTable("GetToKnow");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsSelected).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Value).HasMaxLength(500);
        });

        modelBuilder.Entity<Header>(entity =>
        {
            entity.ToTable("Header");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Prefix).HasMaxLength(1000);
            entity.Property(e => e.Urlid).HasColumnName("URLId");

            entity.HasOne(d => d.Url).WithMany(p => p.Headers)
                .HasForeignKey(d => d.Urlid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Header_URL");
        });

        modelBuilder.Entity<Help>(entity =>
        {
            entity.ToTable("Help");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.DeviceType).HasMaxLength(50);
            entity.Property(e => e.Locale).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.OrgId).HasMaxLength(1000);
            entity.Property(e => e.ProviderId).HasMaxLength(1000);
            entity.Property(e => e.ProviderTypeId).HasMaxLength(1000);
            entity.Property(e => e.Url).HasMaxLength(100);
            entity.Property(e => e.UserId).HasMaxLength(1000);
        });

        modelBuilder.Entity<Identifier>(entity =>
        {
            entity.ToTable("Identifier");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsBeforeAuth).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);
            entity.Property(e => e.Type).HasDefaultValue((byte)1);
            entity.Property(e => e.Urlid).HasColumnName("URLId");

            entity.HasOne(d => d.Provider).WithMany(p => p.Identifiers)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Identifier_Provider");
        });

        modelBuilder.Entity<InApp>(entity =>
        {
            entity.ToTable("InApp");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.IconName).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.ProductId).HasMaxLength(100);
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);
        });

        modelBuilder.Entity<Language>(entity =>
        {
            entity.ToTable("Language");

            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FileName)
                .HasMaxLength(50)
                .HasDefaultValue("Resources.resx");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Locale).HasMaxLength(50);
            entity.Property(e => e.Ltr)
                .HasDefaultValue(true)
                .HasColumnName("LTR");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ShowSequence).HasDefaultValue(10);
        });

        modelBuilder.Entity<LastSeen>(entity =>
        {
            entity.ToTable("LastSeen");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.LastSeen1)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnName("LastSeen");
        });

        modelBuilder.Entity<Lookup>(entity =>
        {
            entity.ToTable("Lookup");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.LookUpId).HasMaxLength(100);
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Value)
                .HasMaxLength(100)
                .HasDefaultValueSql("((1))");
        });

        modelBuilder.Entity<Micontact>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_MContact");

            entity.ToTable("MIContact");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Discount)
                .HasMaxLength(50)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.Email).HasMaxLength(100);
            entity.Property(e => e.ExpiryTime).HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsDpmailSent)
                .HasDefaultValue(false)
                .HasColumnName("IsDPMailSent");
            entity.Property(e => e.IsOnboardingMailSent).HasDefaultValue(false);
            entity.Property(e => e.IsRequested).HasDefaultValue(false);
            entity.Property(e => e.IsSigned)
                .IsRequired()
                .HasDefaultValueSql("('0')");
            entity.Property(e => e.IsUpload)
                .IsRequired()
                .HasDefaultValueSql("('0')");
            entity.Property(e => e.LinkedIn).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.MorgId).HasColumnName("MOrgId");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Phone).HasMaxLength(50);
            entity.Property(e => e.Role).HasMaxLength(500);

            entity.HasOne(d => d.Morg).WithMany(p => p.Micontacts)
                .HasForeignKey(d => d.MorgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MContact_MOrg");
        });

        modelBuilder.Entity<Minature>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_MNature");

            entity.ToTable("MINature");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AppLink).HasMaxLength(50);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<Miorg>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_MOrg");

            entity.ToTable("MIOrg");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CountryCodeWhereIamIn)
                .HasMaxLength(200)
                .HasColumnName("CountryCodeWhereIAmIn");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Credits).HasMaxLength(50);
            entity.Property(e => e.Domain).HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Mnature).HasColumnName("MNature");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.Note).HasMaxLength(500);

            entity.HasOne(d => d.MnatureNavigation).WithMany(p => p.Miorgs)
                .HasForeignKey(d => d.Mnature)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_MIOrg_MINature");
        });

        modelBuilder.Entity<Module>(entity =>
        {
            entity.ToTable("Module");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ReferenceId).HasMaxLength(1000);

            entity.HasOne(d => d.Provider).WithMany(p => p.Modules)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Module_Provider");
        });

        modelBuilder.Entity<MorgCreditUsuage>(entity =>
        {
            entity.ToTable("MOrgCreditUsuage");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Credits).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.MorgId).HasColumnName("MOrgId");
        });

        modelBuilder.Entity<MorgOption>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MOrgOpti__3214EC070E8CC2A3");

            entity.ToTable("MOrgOptions");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Value).HasMaxLength(50);

            entity.HasOne(d => d.Nature).WithMany(p => p.MorgOptions)
                .HasForeignKey(d => d.NatureId)
                .HasConstraintName("FK__MOrgOptio__Natur__3A6CA48E");

            entity.HasOne(d => d.Option).WithMany(p => p.MorgOptions)
                .HasForeignKey(d => d.OptionId)
                .HasConstraintName("FK__MOrgOptio__Optio__3D491139");

            entity.HasOne(d => d.Org).WithMany(p => p.MorgOptions)
                .HasForeignKey(d => d.OrgId)
                .HasConstraintName("FK__MOrgOptio__OrgId__3B60C8C7");

            entity.HasOne(d => d.Question).WithMany(p => p.MorgOptions)
                .HasForeignKey(d => d.QuestionId)
                .HasConstraintName("FK__MOrgOptio__Quest__3C54ED00");
        });

        modelBuilder.Entity<Morganisation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MOrganis__3214EC07C522ABDD");

            entity.ToTable("MOrganisation");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Credits).HasMaxLength(50);
            entity.Property(e => e.MiorgId).HasColumnName("MIOrgId");
            entity.Property(e => e.Model).HasMaxLength(1000);
            entity.Property(e => e.Roles).HasMaxLength(1000);
            entity.Property(e => e.Type).HasMaxLength(1000);
        });

        modelBuilder.Entity<MoukhaReview>(entity =>
        {
            entity.ToTable("MoukhaReview");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.RatingPoints).HasMaxLength(10);
        });

        modelBuilder.Entity<Mperson>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__MPeople__3214EC073DBD1B28");

            entity.ToTable("MPeople");

            entity.Property(e => e.Id).ValueGeneratedNever();
        });

        modelBuilder.Entity<Offer>(entity =>
        {
            entity.ToTable("Offer");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsSingleUse).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.Provider).HasMaxLength(2500);
            entity.Property(e => e.ProviderType).HasMaxLength(2500);
        });

        modelBuilder.Entity<OfferSetting>(entity =>
        {
            entity.ToTable("OfferSetting");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataType).HasDefaultValue((byte)1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Value).HasMaxLength(2000);

            entity.HasOne(d => d.Offer).WithMany(p => p.OfferSettings)
                .HasForeignKey(d => d.OfferId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OfferSetting_Offer");

            entity.HasOne(d => d.Setting).WithMany(p => p.OfferSettings)
                .HasForeignKey(d => d.SettingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OfferSetting_Setting");
        });

        modelBuilder.Entity<Option>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Options__3214EC074786DAC4");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.OptionName).HasMaxLength(50);

            entity.HasOne(d => d.Question).WithMany(p => p.Options)
                .HasForeignKey(d => d.QuestionId)
                .HasConstraintName("FK__Options__Questio__40257DE4");
        });

        modelBuilder.Entity<OrgNature>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("org_nature");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .HasColumnName("name");
        });

        modelBuilder.Entity<PhoneConfig>(entity =>
        {
            entity.ToTable("PhoneConfig");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ApiKey)
                .HasMaxLength(50)
                .HasDefaultValue("");
            entity.Property(e => e.Did)
                .HasMaxLength(50)
                .HasColumnName("DID");
            entity.Property(e => e.Extn).HasMaxLength(50);
            entity.Property(e => e.InstanceUrl)
                .HasMaxLength(50)
                .HasDefaultValue("");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .HasDefaultValue("Virtual Number");
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.Prefix)
                .HasMaxLength(10)
                .HasDefaultValue("");
            entity.Property(e => e.Sipaddress)
                .HasMaxLength(50)
                .HasColumnName("SIPAddress");
        });

        modelBuilder.Entity<Post>(entity =>
        {
            entity.ToTable("Post");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Urlid).HasColumnName("URLId");

            entity.HasOne(d => d.Url).WithMany(p => p.Posts)
                .HasForeignKey(d => d.Urlid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Post_URL");
        });

        modelBuilder.Entity<Provider>(entity =>
        {
            entity.ToTable("Provider");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AdvancedSearch).HasMaxLength(500);
            entity.Property(e => e.AndroidApp).HasMaxLength(500);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IOsapp)
                .HasMaxLength(500)
                .HasColumnName("iOSApp");
            entity.Property(e => e.InShort).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsCustomizationRequired).HasDefaultValue(true);
            entity.Property(e => e.IsVisible).HasDefaultValue(true);
            entity.Property(e => e.LogInType).HasDefaultValue((byte)1);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.PageUrl).HasMaxLength(200);
            entity.Property(e => e.Prefix).HasMaxLength(10);
            entity.Property(e => e.Price).HasDefaultValue(0.0);
            entity.Property(e => e.Reference).HasMaxLength(50);
            entity.Property(e => e.RelatedProviders).HasMaxLength(2500);
            entity.Property(e => e.RequestMonthlyRange)
                .HasMaxLength(500)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.RequestYearlyRange)
                .HasMaxLength(500)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.ShowSequence).HasDefaultValue(10000);
            entity.Property(e => e.WebSite).HasMaxLength(500);
            entity.Property(e => e.Windows10).HasMaxLength(500);
            entity.Property(e => e.WindowsPhone).HasMaxLength(500);
            entity.Property(e => e.YearlyPrice).HasDefaultValue(0.0);

            entity.HasOne(d => d.ProviderType).WithMany(p => p.Providers)
                .HasForeignKey(d => d.ProviderTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Provider_ProviderType");
        });

        modelBuilder.Entity<ProviderIdentifier>(entity =>
        {
            entity.ToTable("ProviderIdentifier");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Provider).WithMany(p => p.ProviderIdentifiers)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProviderIdentifier_Provider");
        });

        modelBuilder.Entity<ProviderType>(entity =>
        {
            entity.ToTable("ProviderType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Priority).HasDefaultValue(100);
        });

        modelBuilder.Entity<ProviderUrl>(entity =>
        {
            entity.ToTable("ProviderURL");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Provider).WithMany(p => p.ProviderUrls)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProviderURL_Provider");

            entity.HasOne(d => d.Url).WithMany(p => p.ProviderUrls)
                .HasForeignKey(d => d.UrlId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProviderURL_URL");
        });

        modelBuilder.Entity<Question>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Question__3214EC0778EE7FAA");

            entity.ToTable("Question");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Name).HasMaxLength(50);

            entity.HasOne(d => d.Nature).WithMany(p => p.Questions)
                .HasForeignKey(d => d.NatureId)
                .HasConstraintName("FK__Question__Nature__45DE573A");

            entity.HasOne(d => d.Type).WithMany(p => p.Questions)
                .HasForeignKey(d => d.TypeId)
                .HasConstraintName("FK__Question__TypeId__46D27B73");
        });

        modelBuilder.Entity<Question1>(entity =>
        {
            entity.ToTable("Questions");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.AnswerOptions).HasMaxLength(500);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Question).HasMaxLength(500);
        });

        modelBuilder.Entity<QuestionType>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Question__3214EC077D397202");

            entity.ToTable("QuestionType");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<Recommend>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("Recommend");

            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DarkImage).HasMaxLength(100);
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ImageHeight).HasDefaultValue(50.0);
            entity.Property(e => e.ImageWidth).HasDefaultValue(300.0);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LightImage)
                .HasMaxLength(100)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.Link).HasMaxLength(100);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.SettingValue).HasMaxLength(50);
        });

        modelBuilder.Entity<Referral>(entity =>
        {
            entity.ToTable("Referral");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ShowSequence).HasDefaultValue(10);
        });

        modelBuilder.Entity<RoleTemplate>(entity =>
        {
            entity.ToTable("RoleTemplate");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Actions).HasMaxLength(1000);
            entity.Property(e => e.Cards).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.Triggers).HasMaxLength(1000);

            entity.HasOne(d => d.Provider).WithMany(p => p.RoleTemplates)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleTemplate_Provider");
        });

        modelBuilder.Entity<Setting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Setting_1");

            entity.ToTable("Setting");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataType).HasDefaultValue((byte)1);
            entity.Property(e => e.DefaultValue).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(200);
        });

        modelBuilder.Entity<Signal>(entity =>
        {
            entity.ToTable("Signal");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AdditionalData)
                .HasMaxLength(1000)
                .IsFixedLength();
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsProcessing).HasDefaultValue(false);
            entity.Property(e => e.Iteration).HasDefaultValue(1);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.TriggerOn).HasColumnType("datetime");

            entity.HasOne(d => d.SignalType).WithMany(p => p.Signals)
                .HasForeignKey(d => d.SignalTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Signal_SignalType");
        });

        modelBuilder.Entity<SignalMode>(entity =>
        {
            entity.ToTable("SignalMode");

            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Template)
                .HasMaxLength(10)
                .IsFixedLength();
        });

        modelBuilder.Entity<SignalType>(entity =>
        {
            entity.ToTable("SignalType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Condition).HasMaxLength(500);
            entity.Property(e => e.ExitCondition).HasMaxLength(500);
            entity.Property(e => e.Iteration).HasDefaultValue(1);
            entity.Property(e => e.LocalNotificationMessage).HasMaxLength(500);
            entity.Property(e => e.MailModoCampaignId).HasMaxLength(500);
            entity.Property(e => e.MinutesLater)
                .HasMaxLength(50)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.OneSignalMessage).HasMaxLength(500);
            entity.Property(e => e.SignalRroomId)
                .HasMaxLength(500)
                .HasColumnName("SignalRRoomId");
            entity.Property(e => e.Smsmessage)
                .HasMaxLength(500)
                .HasColumnName("SMSMessage");
            entity.Property(e => e.Type).HasMaxLength(50);
            entity.Property(e => e.UpdateSetting).HasMaxLength(500);
        });

        modelBuilder.Entity<SocialType>(entity =>
        {
            entity.ToTable("SocialType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Icon).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LookUpUrl).HasMaxLength(1500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.SearchFor).HasMaxLength(500);
            entity.Property(e => e.SearchUrl).HasMaxLength(1500);
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);
        });

        modelBuilder.Entity<Sso>(entity =>
        {
            entity.ToTable("SSO");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Cert).HasMaxLength(2000);
            entity.Property(e => e.ClientId).HasMaxLength(50);
            entity.Property(e => e.ClientSecret).HasMaxLength(100);
            entity.Property(e => e.Domain).HasMaxLength(50);
            entity.Property(e => e.Project).HasMaxLength(50);
            entity.Property(e => e.RedirectUrl)
                .HasMaxLength(500)
                .HasColumnName("RedirectURL");
            entity.Property(e => e.Type).HasMaxLength(50);
            entity.Property(e => e.Url)
                .HasMaxLength(500)
                .HasColumnName("URL");
        });

        modelBuilder.Entity<Sync>(entity =>
        {
            entity.ToTable("Sync");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ReferenceId).HasMaxLength(200);
            entity.Property(e => e.TotalCount).HasDefaultValue(1);
            entity.Property(e => e.Updated).HasDefaultValue(1);
        });

        modelBuilder.Entity<Translate>(entity =>
        {
            entity.ToTable("Translate");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsAutoTranslated).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ReferenceId).HasMaxLength(50);
            entity.Property(e => e.ShowSequence).HasDefaultValue(10);

            entity.HasOne(d => d.Language).WithMany(p => p.Translates)
                .HasForeignKey(d => d.LanguageId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Translate_Language");
        });

        modelBuilder.Entity<Trigger>(entity =>
        {
            entity.ToTable("Trigger");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataUrl).HasMaxLength(2000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(1000);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.TriggerType).HasDefaultValue((byte)1);
        });

        modelBuilder.Entity<Url>(entity =>
        {
            entity.ToTable("URL");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataPath).HasMaxLength(2000);
            entity.Property(e => e.HasHeaders).HasDefaultValue(true);
            entity.Property(e => e.Httptype)
                .HasDefaultValue((byte)1)
                .HasColumnName("HTTPType");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsApi)
                .HasDefaultValue(true)
                .HasColumnName("IsAPI");
            entity.Property(e => e.IsBeforeAuth).HasDefaultValue(false);
            entity.Property(e => e.IsMobile).HasDefaultValue(true);
            entity.Property(e => e.IsWeb).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(1000);
            entity.Property(e => e.ShowOrder).HasDefaultValue(1000);
            entity.Property(e => e.UrltypeId).HasColumnName("URLTypeId");

            entity.HasOne(d => d.Urltype).WithMany(p => p.Urls)
                .HasForeignKey(d => d.UrltypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_URL_URLType");
        });

        modelBuilder.Entity<Urltype>(entity =>
        {
            entity.ToTable("URLType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ShowOrder).HasDefaultValue(1000);
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("User");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Alias).HasMaxLength(500);
            entity.Property(e => e.AnalyticsId).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ChargebeeCustomerId).HasMaxLength(500);
            entity.Property(e => e.Company).HasMaxLength(200);
            entity.Property(e => e.CountryId).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CurrentTimeZone).HasMaxLength(20);
            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.FirstLogin)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FirstName).HasMaxLength(500);
            entity.Property(e => e.InviteCode).HasMaxLength(50);
            entity.Property(e => e.InvitedBy).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsTestUser).HasDefaultValue(false);
            entity.Property(e => e.LastAuthenticated).HasColumnType("datetime");
            entity.Property(e => e.LastLogin)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastName).HasMaxLength(500);
            entity.Property(e => e.MiddleName).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Password).HasMaxLength(50);
            entity.Property(e => e.PreviousLogin)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Ptype).HasColumnName("PType");
            entity.Property(e => e.UserType).HasDefaultValue((byte)1);
        });

        modelBuilder.Entity<UserCredit>(entity =>
        {
            entity.ToTable("UserCredit");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CreditsAdded).HasDefaultValue(1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<UserDevice>(entity =>
        {
            entity.ToTable("UserDevice");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CalendarNotifications).HasDefaultValue(true);
            entity.Property(e => e.CountryId).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Crmpush)
                .HasDefaultValue(true)
                .HasColumnName("CRMPush");
            entity.Property(e => e.ImportContact).HasDefaultValue(true);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Number).HasMaxLength(50);
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.Ptype).HasColumnName("PType");
            entity.Property(e => e.PuchRequired).HasDefaultValue(true);
            entity.Property(e => e.SanitizedNumber).HasMaxLength(50);
        });

        modelBuilder.Entity<UserEmail>(entity =>
        {
            entity.ToTable("UserEmail");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DisplayName).HasMaxLength(500);
            entity.Property(e => e.Email).HasMaxLength(200);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<UserIm>(entity =>
        {
            entity.ToTable("UserIM");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Account).HasMaxLength(200);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ServiceLabel).HasMaxLength(200);
        });

        modelBuilder.Entity<UserPhone>(entity =>
        {
            entity.ToTable("UserPhone");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CountryId).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Extn).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProvidedNumber).HasMaxLength(50);
            entity.Property(e => e.Ptype).HasColumnName("PType");
            entity.Property(e => e.SanitizedNumber).HasMaxLength(50);
            entity.Property(e => e.Type).HasDefaultValue(1);
        });

        modelBuilder.Entity<UserProvider>(entity =>
        {
            entity.ToTable("UserProvider");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AccountId).HasMaxLength(500);
            entity.Property(e => e.ActiveFrom).HasColumnType("datetime");
            entity.Property(e => e.ActiveTill).HasColumnType("datetime");
            entity.Property(e => e.BulkDataUrl).HasMaxLength(1000);
            entity.Property(e => e.CeinstanceId)
                .HasMaxLength(200)
                .HasColumnName("CEInstanceId");
            entity.Property(e => e.CeinstanceUrl)
                .HasMaxLength(1000)
                .HasColumnName("CEInstanceUrl");
            entity.Property(e => e.CeorgId)
                .HasMaxLength(200)
                .HasColumnName("CEOrgId");
            entity.Property(e => e.CeuserId)
                .HasMaxLength(200)
                .HasColumnName("CEUserId");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataUrl).HasColumnName("DataURL");
            entity.Property(e => e.DisplayName).HasMaxLength(500);
            entity.Property(e => e.EmailAddress).HasMaxLength(500);
            entity.Property(e => e.IdpproviderInstance)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderInstance");
            entity.Property(e => e.IdpproviderUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderUrl");
            entity.Property(e => e.Imapserver)
                .HasMaxLength(50)
                .HasColumnName("IMAPServer");
            entity.Property(e => e.InstanceUrl).HasMaxLength(1000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsClaimed).HasDefaultValue(false);
            entity.Property(e => e.IsPaymentStoped).HasDefaultValue(false);
            entity.Property(e => e.IsProvisioned).HasDefaultValue(true);
            entity.Property(e => e.IsSsl).HasColumnName("IsSSL");
            entity.Property(e => e.IsTrial).HasDefaultValue(false);
            entity.Property(e => e.IsTrialCompleted).HasDefaultValue(false);
            entity.Property(e => e.LastCheckedDate).HasColumnType("datetime");
            entity.Property(e => e.LastFullSyncDate).HasColumnType("datetime");
            entity.Property(e => e.LastUpdateDate).HasColumnType("datetime");
            entity.Property(e => e.MailboxGuid).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Password).HasMaxLength(50);
            entity.Property(e => e.Popserver)
                .HasMaxLength(50)
                .HasColumnName("POPServer");
            entity.Property(e => e.Port).HasMaxLength(200);
            entity.Property(e => e.Priority).HasDefaultValue(100);
            entity.Property(e => e.ProductId).HasMaxLength(1500);
            entity.Property(e => e.ProfileUrl)
                .HasMaxLength(1000)
                .HasDefaultValueSql("(NULL)");
            entity.Property(e => e.PurchaseId)
                .HasMaxLength(1500)
                .HasColumnName("PurchaseID");
            entity.Property(e => e.ReferenceId).HasMaxLength(500);
            entity.Property(e => e.Smtpserver)
                .HasMaxLength(50)
                .HasColumnName("SMTPServer");
            entity.Property(e => e.Tenant).HasMaxLength(50);
            entity.Property(e => e.TrialEnd).HasColumnType("datetime");
            entity.Property(e => e.TrialStart).HasColumnType("datetime");
            entity.Property(e => e.Type).HasMaxLength(500);
            entity.Property(e => e.Url).HasColumnName("URL");
            entity.Property(e => e.WebhookExpiration).HasColumnType("datetime");
        });

        modelBuilder.Entity<UserProviderIdentifier>(entity =>
        {
            entity.ToTable("UserProviderIdentifier");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.UserProvider).WithMany(p => p.UserProviderIdentifiers)
                .HasForeignKey(d => d.UserProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserProviderIdentifier_UserProvider");
        });

        modelBuilder.Entity<UserProvideridentifierjoin>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("userProvideridentifierjoin");
        });

        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.ToTable("UserRole");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<UserSetting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_UserSetting_1");

            entity.ToTable("UserSetting");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataType).HasDefaultValue((byte)1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Value).HasMaxLength(2000);

            entity.HasOne(d => d.Setting).WithMany(p => p.UserSettings)
                .HasForeignKey(d => d.SettingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserSetting_Setting1");
        });

        modelBuilder.Entity<UserSocial>(entity =>
        {
            entity.ToTable("UserSocial");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DisplayName).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.SocialNetworkId).HasMaxLength(500);
            entity.Property(e => e.Type).HasDefaultValue(13);
        });

        modelBuilder.Entity<UserStatus>(entity =>
        {
            entity.ToTable("UserStatus");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.IOscontacts).HasColumnName("iOSContacts");
            entity.Property(e => e.IsDrakTheme).HasDefaultValue(true);
            entity.Property(e => e.Otp).HasColumnName("OTP");
            entity.Property(e => e.ShouldShowAds).HasDefaultValue(true);
        });

        modelBuilder.Entity<UserStorage>(entity =>
        {
            entity.ToTable("UserStorage");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(2250);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.Type).HasComment("Type bit - 0 for folder, 1 for search");
        });

        modelBuilder.Entity<UserTask>(entity =>
        {
            entity.ToTable("UserTask");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.VerificationInfo).HasMaxLength(500);

            entity.HasOne(d => d.Task).WithMany(p => p.UserTasks)
                .HasForeignKey(d => d.TaskId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserTask_Task");
        });

        modelBuilder.Entity<Userdeviceview>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("userdeviceview");

            entity.Property(e => e.Userid).HasColumnName("userid");
        });

        modelBuilder.Entity<UserproviderApiView>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("userprovider_api_view");

            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.UpId).HasColumnName("up_id");
            entity.Property(e => e.UsId).HasColumnName("us_id");
            entity.Property(e => e.UserEmailId).HasColumnName("user_email_id");
            entity.Property(e => e.UserStatusId).HasColumnName("user_status_id");
            entity.Property(e => e.Value).HasMaxLength(2000);
        });

        modelBuilder.Entity<Usersettingview>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("usersettingview");

            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.Value).HasMaxLength(2000);
        });

        modelBuilder.Entity<VUserDevice>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vUserDevices");

            entity.Property(e => e.Email).HasMaxLength(500);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
