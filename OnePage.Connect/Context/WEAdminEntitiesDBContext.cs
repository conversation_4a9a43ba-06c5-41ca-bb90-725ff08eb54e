﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using OnePage.Connect.Migrations;
using OnePage.Models;
using ValueType = OnePage.Connect.Migrations.ValueType;

namespace OnePage.Connect.Context;

public partial class WEAdminEntitiesDBContext : DbContext
{
    public WEAdminEntitiesDBContext()
    {
    }

    public WEAdminEntitiesDBContext(DbContextOptions<WEAdminEntitiesDBContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Abuse> Abuses { get; set; }

    public virtual DbSet<Activation> Activations { get; set; }

    public virtual DbSet<AdView> AdViews { get; set; }

    public virtual DbSet<App> Apps { get; set; }

    public virtual DbSet<AppLanguage> AppLanguages { get; set; }

    public virtual DbSet<AppLanguageTemplate> AppLanguageTemplates { get; set; }

    public virtual DbSet<AppRoleTemplate> AppRoleTemplates { get; set; }

    public virtual DbSet<ApplicationConfiguration> ApplicationConfigurations { get; set; }

    public virtual DbSet<ApplicationLog> ApplicationLogs { get; set; }

    public virtual DbSet<BotDatum> BotData { get; set; }

    public virtual DbSet<Category> Categories { get; set; }

    public virtual DbSet<Company> Companies { get; set; }

    public virtual DbSet<CompanyDomain> CompanyDomains { get; set; }

    public virtual DbSet<CompanyGic> CompanyGics { get; set; }

    public virtual DbSet<CompanyLocation> CompanyLocations { get; set; }

    public virtual DbSet<CompanyPhone> CompanyPhones { get; set; }

    public virtual DbSet<CompanyProduct> CompanyProducts { get; set; }

    public virtual DbSet<CompanySocial> CompanySocials { get; set; }

    public virtual DbSet<CompanyStory> CompanyStories { get; set; }

    public virtual DbSet<CompanyTag> CompanyTags { get; set; }

    public virtual DbSet<ContactPerson> ContactPeople { get; set; }

    public virtual DbSet<CopilotAad> CopilotAads { get; set; }

    public virtual DbSet<Country> Countries { get; set; }

    public virtual DbSet<Coupon> Coupons { get; set; }

    public virtual DbSet<CouponType> CouponTypes { get; set; }

    public virtual DbSet<CreditActivity> CreditActivities { get; set; }

    public virtual DbSet<CreditTask> CreditTasks { get; set; }

    public virtual DbSet<DatabaseVersionHistory> DatabaseVersionHistories { get; set; }

    public virtual DbSet<DemoRequest> DemoRequests { get; set; }

    public virtual DbSet<DeploymentUrl> DeploymentUrls { get; set; }

    public virtual DbSet<Discover> Discovers { get; set; }

    public virtual DbSet<Domain> Domains { get; set; }

    public virtual DbSet<DomainBlacklist> DomainBlacklists { get; set; }

    public virtual DbSet<EmailTemplate> EmailTemplates { get; set; }

    public virtual DbSet<EmailWhiteList> EmailWhiteLists { get; set; }

    public virtual DbSet<Event> Events { get; set; }

    public virtual DbSet<Evolution> Evolutions { get; set; }

    public virtual DbSet<GetToKnow> GetToKnows { get; set; }

    public virtual DbSet<InApp> InApps { get; set; }

    public virtual DbSet<Industry> Industries { get; set; }

    public virtual DbSet<IndustryGroup> IndustryGroups { get; set; }

    public virtual DbSet<KeyRotation> KeyRotations { get; set; }

    public virtual DbSet<KnownUser> KnownUsers { get; set; }

    public virtual DbSet<Lang> Langs { get; set; }

    public virtual DbSet<LastSeen> LastSeens { get; set; }

    public virtual DbSet<Location> Locations { get; set; }

    public virtual DbSet<MeteredAuditLog> MeteredAuditLogs { get; set; }

    public virtual DbSet<MeteredDimension> MeteredDimensions { get; set; }

    public virtual DbSet<Offer> Offers { get; set; }

    public virtual DbSet<Offer1> Offers1 { get; set; }

    public virtual DbSet<OfferAttribute> OfferAttributes { get; set; }

    public virtual DbSet<OfferSetting> OfferSettings { get; set; }

    public virtual DbSet<Person> People { get; set; }

    public virtual DbSet<PersonAward> PersonAwards { get; set; }

    public virtual DbSet<PersonCompany> PersonCompanies { get; set; }

    public virtual DbSet<PersonDomain> PersonDomains { get; set; }

    public virtual DbSet<PersonEmail> PersonEmails { get; set; }

    public virtual DbSet<PersonLanguage> PersonLanguages { get; set; }

    public virtual DbSet<PersonLocation> PersonLocations { get; set; }

    public virtual DbSet<PersonPhone> PersonPhones { get; set; }

    public virtual DbSet<PersonSocial> PersonSocials { get; set; }

    public virtual DbSet<PersonStory> PersonStories { get; set; }

    public virtual DbSet<PersonTag> PersonTags { get; set; }

    public virtual DbSet<PersonUniversity> PersonUniversities { get; set; }

    public virtual DbSet<PhoneConfig> PhoneConfigs { get; set; }

    public virtual DbSet<Plan> Plans { get; set; }

    public virtual DbSet<PlanAttributeMapping> PlanAttributeMappings { get; set; }

    public virtual DbSet<PlanAttributeOutput> PlanAttributeOutputs { get; set; }

    public virtual DbSet<PlanEventsMapping> PlanEventsMappings { get; set; }

    public virtual DbSet<PlanEventsOutPut> PlanEventsOutPuts { get; set; }

    public virtual DbSet<Product> Products { get; set; }

    public virtual DbSet<ProductCategory> ProductCategories { get; set; }

    public virtual DbSet<Purchase> Purchases { get; set; }

    public virtual DbSet<Referral> Referrals { get; set; }

    public virtual DbSet<RelatedStory> RelatedStories { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<Role1> Roles1 { get; set; }

    public virtual DbSet<Sector> Sectors { get; set; }

    public virtual DbSet<Server> Servers { get; set; }

    public virtual DbSet<Setting> Settings { get; set; }

    public virtual DbSet<Signal> Signals { get; set; }

    public virtual DbSet<SignalMode> SignalModes { get; set; }

    public virtual DbSet<SignalType> SignalTypes { get; set; }

    public virtual DbSet<Social> Socials { get; set; }

    public virtual DbSet<Source> Sources { get; set; }

    public virtual DbSet<Sso> Ssos { get; set; }

    public virtual DbSet<Stack> Stacks { get; set; }

    public virtual DbSet<Story> Stories { get; set; }

    public virtual DbSet<StorySource> StorySources { get; set; }

    public virtual DbSet<StoryTag> StoryTags { get; set; }

    public virtual DbSet<SubIndustry> SubIndustries { get; set; }

    public virtual DbSet<Subscription> Subscriptions { get; set; }

    public virtual DbSet<SubscriptionAttributeValue> SubscriptionAttributeValues { get; set; }

    public virtual DbSet<SubscriptionAuditLog> SubscriptionAuditLogs { get; set; }

    public virtual DbSet<SubscriptionEmailOutput> SubscriptionEmailOutputs { get; set; }

    public virtual DbSet<SubscriptionParametersOutput> SubscriptionParametersOutputs { get; set; }

    public virtual DbSet<Sync> Syncs { get; set; }

    public virtual DbSet<Tag> Tags { get; set; }

    public virtual DbSet<Theme> Themes { get; set; }

    public virtual DbSet<Token> Tokens { get; set; }

    public virtual DbSet<University> Universities { get; set; }

    public virtual DbSet<UniversityDomain> UniversityDomains { get; set; }

    public virtual DbSet<UniversityLocation> UniversityLocations { get; set; }

    public virtual DbSet<UniversityPhone> UniversityPhones { get; set; }

    public virtual DbSet<UniversitySocial> UniversitySocials { get; set; }

    public virtual DbSet<UniversityStory> UniversityStories { get; set; }

    public virtual DbSet<UniversityTag> UniversityTags { get; set; }

    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<User1> Users1 { get; set; }

    public virtual DbSet<UserCredit> UserCredits { get; set; }

    public virtual DbSet<UserDevice> UserDevices { get; set; }

    public virtual DbSet<UserEmail> UserEmails { get; set; }

    public virtual DbSet<UserIm> UserIms { get; set; }

    public virtual DbSet<UserPhone> UserPhones { get; set; }

    public virtual DbSet<UserProvider> UserProviders { get; set; }

    public virtual DbSet<UserProviderIdentifier> UserProviderIdentifiers { get; set; }

    public virtual DbSet<UserRole> UserRoles { get; set; }

    public virtual DbSet<UserSetting> UserSettings { get; set; }

    public virtual DbSet<UserSocial> UserSocials { get; set; }

    public virtual DbSet<UserStatus> UserStatuses { get; set; }

    public virtual DbSet<UserStorage> UserStorages { get; set; }

    public virtual DbSet<UserTask> UserTasks { get; set; }

    public virtual DbSet<Usersettingview> Usersettingviews { get; set; }

    public virtual DbSet<VUserDevice> VUserDevices { get; set; }

    public virtual DbSet<ValueType> ValueTypes { get; set; }

    public virtual DbSet<WebJobSubscriptionStatus> WebJobSubscriptionStatuses { get; set; }

//     protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
// #warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
//         => optionsBuilder.UseSqlServer("Server=eu-ne-sd.database.windows.net;Database=eu-fc-ss-ad;User=eu-ne-sd;Password=**************;MultipleActiveResultSets=true;");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Abuse>(entity =>
        {
            entity.ToTable("Abuse");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Ipaddress)
                .HasMaxLength(50)
                .HasColumnName("IPAddress");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.NewUserAgent).HasMaxLength(2000);
            entity.Property(e => e.Reason).HasMaxLength(100);

            entity.HasOne(d => d.Token).WithMany(p => p.Abuses)
                .HasForeignKey(d => d.TokenId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Abuse_Token");
        });

        modelBuilder.Entity<Activation>(entity =>
        {
            entity.ToTable("Activation");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.ActivatedDate).HasColumnType("datetime");
            entity.Property(e => e.ActivationStatus).HasMaxLength(100);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.DeActivatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<AdView>(entity =>
        {
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<App>(entity =>
        {
            entity.ToTable("App");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AndroidOneSignalApiKey).HasMaxLength(500);
            entity.Property(e => e.AndroidOneSignalId).HasMaxLength(500);
            entity.Property(e => e.AndroidProjectId).HasMaxLength(500);
            entity.Property(e => e.AndroidProjectKey).HasMaxLength(1000);
            entity.Property(e => e.Createddate).HasColumnType("datetime");
            entity.Property(e => e.IosoneSignalApiKey)
                .HasMaxLength(500)
                .HasColumnName("IOSOneSignalApiKey");
            entity.Property(e => e.IosoneSignalId)
                .HasMaxLength(500)
                .HasColumnName("IOSOneSignalId");
            entity.Property(e => e.LanguagesToShow).HasMaxLength(50);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.PrivacyUrl).HasMaxLength(2000);
            entity.Property(e => e.ProxyServer).HasMaxLength(1000);
            entity.Property(e => e.ShowAds).HasDefaultValue(true);
            entity.Property(e => e.ShowImportContacts).HasDefaultValue(true);
            entity.Property(e => e.ShowLanguage).HasDefaultValue(true);
            entity.Property(e => e.ShowNotifications).HasDefaultValue(true);
            entity.Property(e => e.ShowPermissions).HasDefaultValue(true);
            entity.Property(e => e.ShowPushNotifications).HasDefaultValue(true);
            entity.Property(e => e.ShowRecentCalls).HasDefaultValue(true);
            entity.Property(e => e.ShowRedeem).HasDefaultValue(true);
            entity.Property(e => e.ShowSocialProviders).HasDefaultValue(true);
            entity.Property(e => e.ShowTheme).HasDefaultValue(true);
            entity.Property(e => e.ShowUnknownCalls).HasDefaultValue(true);
            entity.Property(e => e.SupportEmail).HasMaxLength(150);
            entity.Property(e => e.SupportName).HasMaxLength(500);
            entity.Property(e => e.SupportPhone).HasMaxLength(50);
            entity.Property(e => e.Tocurl)
                .HasMaxLength(2000)
                .HasColumnName("TOCUrl");
            entity.Property(e => e.WebOneSignalApiKey).HasMaxLength(500);
            entity.Property(e => e.WebOneSignalId).HasMaxLength(500);
        });

        modelBuilder.Entity<AppLanguage>(entity =>
        {
            entity.ToTable("AppLanguage");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Createddate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.SendGridKey).HasMaxLength(250);

            entity.HasOne(d => d.App).WithMany(p => p.AppLanguages)
                .HasForeignKey(d => d.AppId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AppLanguage_App");
        });

        modelBuilder.Entity<AppLanguageTemplate>(entity =>
        {
            entity.ToTable("AppLanguageTemplate");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Createddate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.TemplateId).HasMaxLength(1000);
            entity.Property(e => e.TemplateName).HasMaxLength(500);

            entity.HasOne(d => d.AppLanguage).WithMany(p => p.AppLanguageTemplates)
                .HasForeignKey(d => d.AppLanguageId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AppLanguageTemplate_AppLanguage");
        });

        modelBuilder.Entity<AppRoleTemplate>(entity =>
        {
            entity.ToTable("AppRoleTemplate");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Actions).HasMaxLength(1000);
            entity.Property(e => e.Cards).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.ShowSequence).HasDefaultValue(0);
            entity.Property(e => e.Triggers).HasMaxLength(1000);
        });

        modelBuilder.Entity<ApplicationConfiguration>(entity =>
        {
            entity.ToTable("ApplicationConfiguration");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Description).HasMaxLength(255);
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<ApplicationLog>(entity =>
        {
            entity.ToTable("ApplicationLog");

            entity.Property(e => e.ActionTime).HasColumnType("datetime");
            entity.Property(e => e.LogDetail)
                .HasMaxLength(4000)
                .IsUnicode(false);
        });

        modelBuilder.Entity<BotDatum>(entity =>
        {
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Data).HasMaxLength(1000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<Category>(entity =>
        {
            entity.ToTable("Category");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<Company>(entity =>
        {
            entity.ToTable("Company");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Address).HasMaxLength(500);
            entity.Property(e => e.Aka)
                .HasMaxLength(1000)
                .HasColumnName("AKA");
            entity.Property(e => e.Country).HasMaxLength(500);
            entity.Property(e => e.Employees).HasMaxLength(100);
            entity.Property(e => e.Founded)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.Industry).HasMaxLength(500);
            entity.Property(e => e.Locality).HasMaxLength(500);
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.RefId).HasMaxLength(50);
            entity.Property(e => e.Ticker).HasMaxLength(50);
        });

        modelBuilder.Entity<CompanyDomain>(entity =>
        {
            entity.ToTable("CompanyDomain");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Company).WithMany(p => p.CompanyDomains)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyDomain_Company");

            
        });

        modelBuilder.Entity<CompanyGic>(entity =>
        {
            entity.ToTable("CompanyGICS");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Industry)
                .HasMaxLength(6)
                .IsFixedLength();
            entity.Property(e => e.IndustryGroup)
                .HasMaxLength(4)
                .IsFixedLength();
            entity.Property(e => e.SectorId)
                .HasMaxLength(2)
                .IsFixedLength();
            entity.Property(e => e.SubIndustry)
                .HasMaxLength(8)
                .IsFixedLength();

            entity.HasOne(d => d.Company).WithMany(p => p.CompanyGics)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyGICS_Company");
        });

        modelBuilder.Entity<CompanyLocation>(entity =>
        {
            entity.ToTable("CompanyLocation");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            entity.HasOne(d => d.Company).WithMany(p => p.CompanyLocations)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyLocation_Company");

            entity.HasOne(d => d.Location).WithMany(p => p.CompanyLocations)
                .HasForeignKey(d => d.LocationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyLocation_Location");
        });

        modelBuilder.Entity<CompanyPhone>(entity =>
        {
            entity.ToTable("CompanyPhone");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CountryId).HasMaxLength(100);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Extn).HasMaxLength(100);
            entity.Property(e => e.IsPastPhone).HasDefaultValue(false);
            entity.Property(e => e.IsPreferred).HasDefaultValue(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProvidedNumber).HasMaxLength(100);
            entity.Property(e => e.SanitizedNumber).HasMaxLength(100);

            entity.HasOne(d => d.Company).WithMany(p => p.CompanyPhones)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyPhone_Company");
        });

        modelBuilder.Entity<CompanyProduct>(entity =>
        {
            entity.ToTable("CompanyProduct");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");

            entity.HasOne(d => d.Company).WithMany(p => p.CompanyProducts)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyProduct_Company");

            entity.HasOne(d => d.Product).WithMany(p => p.CompanyProducts)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyProduct_Product");
        });

        modelBuilder.Entity<CompanySocial>(entity =>
        {
            entity.ToTable("CompanySocial");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsValid).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.RefId).HasMaxLength(50);
            entity.Property(e => e.Url).HasMaxLength(500);
            entity.Property(e => e.Username).HasMaxLength(100);

            entity.HasOne(d => d.Company).WithMany(p => p.CompanySocials)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanySocial_Company");

            entity.HasOne(d => d.Social).WithMany(p => p.CompanySocials)
                .HasForeignKey(d => d.SocialId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanySocial_Social");

            entity.HasOne(d => d.Source).WithMany(p => p.CompanySocials)
                .HasForeignKey(d => d.SourceId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanySocial_Source");
        });

        modelBuilder.Entity<CompanyStory>(entity =>
        {
            entity.ToTable("CompanyStory");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            entity.HasOne(d => d.Company).WithMany(p => p.CompanyStories)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyStory_Company");

            entity.HasOne(d => d.Story).WithMany(p => p.CompanyStories)
                .HasForeignKey(d => d.StoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyStory_Story");
        });

        modelBuilder.Entity<CompanyTag>(entity =>
        {
            entity.ToTable("CompanyTag");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            entity.HasOne(d => d.Company).WithMany(p => p.CompanyTags)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyTag_Company");

            entity.HasOne(d => d.Tag).WithMany(p => p.CompanyTags)
                .HasForeignKey(d => d.TagId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CompanyTag_Tag");
        });

        modelBuilder.Entity<ContactPerson>(entity =>
        {
            entity.ToTable("ContactPerson");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AddedBy).HasMaxLength(500);
            entity.Property(e => e.AddedDateTime).HasColumnType("datetime");
            entity.Property(e => e.CompanyName).HasMaxLength(500);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Designation).HasMaxLength(500);
            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.FirstName).HasMaxLength(500);
            entity.Property(e => e.IsPdlcalled).HasColumnName("IsPDLCalled");
            entity.Property(e => e.LastName).HasMaxLength(500);
            entity.Property(e => e.LinkedIn).HasMaxLength(500);
            entity.Property(e => e.NeededBy).HasColumnType("datetime");
            entity.Property(e => e.Phone).HasMaxLength(50);
            entity.Property(e => e.SecondaryVerificationBy).HasMaxLength(500);
            entity.Property(e => e.SecondaryVerificationComments).HasMaxLength(500);
            entity.Property(e => e.SecondaryVerificationDateTime).HasColumnType("datetime");
            entity.Property(e => e.Suggestion).HasMaxLength(100);
            entity.Property(e => e.Twitter).HasMaxLength(500);
            entity.Property(e => e.UpdatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.VerificationComments).HasMaxLength(500);
            entity.Property(e => e.VerificationDateTime).HasColumnType("datetime");

            entity.HasOne(d => d.Person).WithMany(p => p.ContactPeople)
                .HasForeignKey(d => d.PersonId)
                .HasConstraintName("FK_ContactPerson_Person");
        });

        modelBuilder.Entity<CopilotAad>(entity =>
        {
            entity.ToTable("CopilotAad");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<Country>(entity =>
        {
            entity.ToTable("Country");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Alpha2).HasMaxLength(2);
            entity.Property(e => e.Alpha3).HasMaxLength(3);
            entity.Property(e => e.Continent).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IntlPrefix).HasMaxLength(10);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Prefix).HasMaxLength(10);
            entity.Property(e => e.ServerZone).HasDefaultValue((byte)1);
        });

        modelBuilder.Entity<Coupon>(entity =>
        {
            entity.ToTable("Coupon");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Count).HasDefaultValue(1);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);

            entity.HasOne(d => d.Offer).WithMany(p => p.Coupons)
                .HasForeignKey(d => d.OfferId)
                .HasConstraintName("FK_Coupon_Offer");
        });

        modelBuilder.Entity<CouponType>(entity =>
        {
            entity.ToTable("CouponType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Name).HasMaxLength(1000);
            entity.Property(e => e.Provider).HasMaxLength(2500);
            entity.Property(e => e.ProviderType).HasMaxLength(2500);
        });

        modelBuilder.Entity<CreditActivity>(entity =>
        {
            entity.ToTable("CreditActivity");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Credit).HasDefaultValue(0);
            entity.Property(e => e.CreditRedeemedDate).HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Payload).HasMaxLength(500);
            entity.Property(e => e.ProductId).HasMaxLength(100);
            entity.Property(e => e.ReceiptId).HasMaxLength(100);
            entity.Property(e => e.Redeemdate)
                .HasDefaultValueSql("((1))")
                .HasColumnType("datetime");
            entity.Property(e => e.RedeemedNonVanishngCredit).HasDefaultValue(0);
            entity.Property(e => e.RedeemedVanishngCredit).HasDefaultValue(0);
            entity.Property(e => e.TransactionDateUtc)
                .HasColumnType("datetime")
                .HasColumnName("TransactionDateUTC");
            entity.Property(e => e.VanishingCredits).HasDefaultValue(0);
        });

        modelBuilder.Entity<CreditTask>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Task");

            entity.ToTable("CreditTask");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Credits).HasDefaultValue(1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsOneTime).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
        });

        modelBuilder.Entity<DatabaseVersionHistory>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("DatabaseVersionHistory");

            entity.Property(e => e.CreateBy).HasMaxLength(100);
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.VersionNumber).HasColumnType("decimal(6, 2)");
        });

        modelBuilder.Entity<DemoRequest>(entity =>
        {
            entity.ToTable("DemoRequest");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CompanyName).HasMaxLength(50);
            entity.Property(e => e.CountryId).HasMaxLength(2);
            entity.Property(e => e.Createddate).HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.FirstName).HasMaxLength(500);
            entity.Property(e => e.IdpproviderUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderUrl");
            entity.Property(e => e.IdpsandboxUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPSandboxUrl");
            entity.Property(e => e.LastName).HasMaxLength(500);
            entity.Property(e => e.MiddleName).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.Ptype)
                .HasDefaultValue(1)
                .HasColumnName("PType");
            entity.Property(e => e.Salutation).HasMaxLength(100);
        });

        modelBuilder.Entity<DeploymentUrl>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsPaid).HasDefaultValue(true);
            entity.Property(e => e.RedirectUrl).HasMaxLength(200);
        });

        modelBuilder.Entity<Discover>(entity =>
        {
            entity.ToTable("Discover");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Action1).HasMaxLength(50);
            entity.Property(e => e.Action2).HasMaxLength(50);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Detail).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Title).HasMaxLength(500);
            entity.Property(e => e.TriggerValue).HasMaxLength(50);
            entity.Property(e => e.Video).HasMaxLength(50);

            entity.HasOne(d => d.Setting).WithMany(p => p.Discovers)
                .HasForeignKey(d => d.SettingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Discover_Setting");
        });

        // modelBuilder.Entity<Domain>(entity =>
        // {
        //     entity.ToTable("Domain");
        //
        //     entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
        //     entity.Property(e => e.Url).HasMaxLength(100);
        //     entity.Property(e => e.ReadCount).HasDefaultValue(1L);
        //     entity.Property(e => e.RefCount).HasDefaultValue(1L);
        //     entity.Property(e => e.Url).HasMaxLength(100);
        // });

        modelBuilder.Entity<DomainBlacklist>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_FreeDomain");

            entity.ToTable("DomainBlacklist");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.DomainName).HasMaxLength(1000);
        });

        modelBuilder.Entity<EmailTemplate>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__EmailTem__3214EC27B6B6AEC3");

            entity.ToTable("EmailTemplate");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Bcc)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("BCC");
            entity.Property(e => e.Cc)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("CC");
            entity.Property(e => e.Description)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.InsertDate).HasColumnType("datetime");
            entity.Property(e => e.Status)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.Subject)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.TemplateBody).IsUnicode(false);
            entity.Property(e => e.ToRecipients)
                .HasMaxLength(1000)
                .IsUnicode(false);
        });

        modelBuilder.Entity<EmailWhiteList>(entity =>
        {
            entity.ToTable("EmailWhiteList");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(100);
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Phone).HasMaxLength(50);
        });

        modelBuilder.Entity<Event>(entity =>
        {
            entity.HasKey(e => e.EventsId).HasName("PK__Events__11F369A2D389D3A1");

            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.EventsName)
                .HasMaxLength(225)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Evolution>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK___evoluti__3213E83FD0F9F7EC");

            entity.ToTable("_evolutions");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Batch).HasColumnName("batch");
            entity.Property(e => e.Checksum)
                .HasMaxLength(255)
                .HasColumnName("checksum");
            entity.Property(e => e.Created).HasColumnName("created");
            entity.Property(e => e.CreatedAt).HasColumnName("created_at");
            entity.Property(e => e.Description)
                .HasMaxLength(255)
                .HasColumnName("description");
            entity.Property(e => e.Status).HasColumnName("status");
            entity.Property(e => e.Title)
                .HasMaxLength(255)
                .HasColumnName("title");
            entity.Property(e => e.TitleDown)
                .HasMaxLength(255)
                .HasColumnName("titleDown");
            entity.Property(e => e.UpdatedAt).HasColumnName("updated_at");
        });

        modelBuilder.Entity<GetToKnow>(entity =>
        {
            entity.ToTable("GetToKnow");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsSelected).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Value).HasMaxLength(500);
        });

        modelBuilder.Entity<InApp>(entity =>
        {
            entity.ToTable("InApp");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Description).HasMaxLength(500);
            entity.Property(e => e.IconName).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.ProductId).HasMaxLength(100);
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);
        });

        modelBuilder.Entity<Industry>(entity =>
        {
            entity.ToTable("Industry");

            entity.Property(e => e.Id)
                .HasMaxLength(6)
                .IsFixedLength();
            entity.Property(e => e.IndustryGroupId)
                .HasMaxLength(4)
                .IsFixedLength();
            entity.Property(e => e.Text).HasMaxLength(200);

            entity.HasOne(d => d.IndustryGroup).WithMany(p => p.Industries)
                .HasForeignKey(d => d.IndustryGroupId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Industry_IndustryGroup");
        });

        modelBuilder.Entity<IndustryGroup>(entity =>
        {
            entity.ToTable("IndustryGroup");

            entity.Property(e => e.Id)
                .HasMaxLength(4)
                .IsFixedLength();
            entity.Property(e => e.SectorId)
                .HasMaxLength(2)
                .IsFixedLength();
            entity.Property(e => e.Text).HasMaxLength(200);

            entity.HasOne(d => d.Sector).WithMany(p => p.IndustryGroups)
                .HasForeignKey(d => d.SectorId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_IndustryGroup_Sector");
        });

        modelBuilder.Entity<KeyRotation>(entity =>
        {
            entity.ToTable("KeyRotation");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ResetDate).HasColumnType("datetime");
            entity.Property(e => e.Source).HasMaxLength(50);
        });

        modelBuilder.Entity<KnownUser>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__KnownUse__3214EC07D4BF7CBB");

            entity.Property(e => e.UserEmail)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.HasOne(d => d.Role).WithMany(p => p.KnownUsers)
                .HasForeignKey(d => d.RoleId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__KnownUser__RoleI__113584D1");
        });

        modelBuilder.Entity<Lang>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Language");

            entity.ToTable("Lang");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Locale).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<LastSeen>(entity =>
        {
            entity.ToTable("LastSeen");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.LastSeen1)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnName("LastSeen");
        });

        modelBuilder.Entity<Location>(entity =>
        {
            entity.ToTable("Location");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AddressLine2).HasMaxLength(50);
            entity.Property(e => e.Continent).HasMaxLength(50);
            entity.Property(e => e.Country).HasMaxLength(50);
            entity.Property(e => e.LastUpdated).HasColumnType("datetime");
            entity.Property(e => e.Locality).HasMaxLength(50);
            entity.Property(e => e.Metro).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.PostalCode).HasMaxLength(50);
            entity.Property(e => e.Region).HasMaxLength(50);
            entity.Property(e => e.StreetAddress).HasMaxLength(50);
        });

        modelBuilder.Entity<MeteredAuditLog>(entity =>
        {
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.RequestJson)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.ResponseJson)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.StatusCode)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.SubscriptionUsageDate).HasColumnType("datetime");

            entity.HasOne(d => d.Subscription).WithMany(p => p.MeteredAuditLogs)
                .HasForeignKey(d => d.SubscriptionId)
                .HasConstraintName("FK__MeteredAu__Subsc__1229A90A");
        });

        modelBuilder.Entity<MeteredDimension>(entity =>
        {
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Description)
                .HasMaxLength(250)
                .IsUnicode(false);
            entity.Property(e => e.Dimension)
                .HasMaxLength(150)
                .IsUnicode(false);

            entity.HasOne(d => d.Plan).WithMany(p => p.MeteredDimensions)
                .HasForeignKey(d => d.PlanId)
                .HasConstraintName("FK__MeteredDi__PlanI__74643BF9");
        });

        modelBuilder.Entity<Offer>(entity =>
        {
            entity.ToTable("Offer");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsSingleUse).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.Provider).HasMaxLength(2500);
            entity.Property(e => e.ProviderType).HasMaxLength(2500);
        });

        modelBuilder.Entity<Offer1>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Offers__3214EC07DD5D8F91");

            entity.ToTable("Offers");

            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.OfferGuid).HasColumnName("OfferGUId");
            entity.Property(e => e.OfferId)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.OfferName)
                .HasMaxLength(225)
                .IsUnicode(false);
        });

        modelBuilder.Entity<OfferAttribute>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__OfferAtt__3214EC27A472E3D6");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.Description)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.DisplayName)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.ParameterId)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.Type)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.ValuesList).IsUnicode(false);
        });

        modelBuilder.Entity<OfferSetting>(entity =>
        {
            entity.ToTable("OfferSetting");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataType).HasDefaultValue((byte)1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Value).HasMaxLength(2000);

            entity.HasOne(d => d.Offer).WithMany(p => p.OfferSettings)
                .HasForeignKey(d => d.OfferId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OfferSetting_Offer");

            entity.HasOne(d => d.Setting).WithMany(p => p.OfferSettings)
                .HasForeignKey(d => d.SettingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OfferSetting_Setting");
        });

        modelBuilder.Entity<Person>(entity =>
        {
            entity.ToTable("Person");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Aka)
                .HasMaxLength(1000)
                .HasColumnName("AKA");
            entity.Property(e => e.BirthDate).HasMaxLength(50);
            entity.Property(e => e.BirthYear).HasMaxLength(50);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.FullName).HasMaxLength(250);
            entity.Property(e => e.Gender)
                .HasMaxLength(100)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.MiddleName).HasMaxLength(100);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Pdldata).HasColumnName("PDLData");
            entity.Property(e => e.RefId).HasMaxLength(50);
            entity.Property(e => e.Salutation).HasMaxLength(50);
        });

        modelBuilder.Entity<PersonAward>(entity =>
        {
            entity.ToTable("PersonAward");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AwardDate).HasColumnType("datetime");
            entity.Property(e => e.Awarder).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Title).HasMaxLength(500);

            entity.HasOne(d => d.Person).WithMany(p => p.PersonAwards)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonAward_Person");
        });

        modelBuilder.Entity<PersonCompany>(entity =>
        {
            entity.ToTable("PersonCompany");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.EndDate).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsCurrent).HasDefaultValue(true);
            entity.Property(e => e.IsPrimary).HasMaxLength(50);
            entity.Property(e => e.JobLevel).HasMaxLength(50);
            entity.Property(e => e.Notes).HasMaxLength(2000);
            entity.Property(e => e.Role).HasMaxLength(200);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1);
            entity.Property(e => e.StartDate).HasMaxLength(50);
            entity.Property(e => e.Title).HasMaxLength(200);

            entity.HasOne(d => d.Company).WithMany(p => p.PersonCompanies)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonCompany_Company");

            entity.HasOne(d => d.Person).WithMany(p => p.PersonCompanies)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonCompany_Person");
        });

        modelBuilder.Entity<PersonDomain>(entity =>
        {
            entity.ToTable("PersonDomain");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsWork).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            // entity.HasOne(d => d.Domain).WithMany(p => p.PersonDomains)
            //     .HasForeignKey(d => d.DomainId)
            //     .OnDelete(DeleteBehavior.ClientSetNull)
            //     .HasConstraintName("FK_PersonDomain_Domain");

            entity.HasOne(d => d.Person).WithMany(p => p.PersonDomains)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonDomain_Person");
        });

        modelBuilder.Entity<PersonEmail>(entity =>
        {
            entity.ToTable("PersonEmail");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(200);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsGuessed).HasDefaultValue(true);
            entity.Property(e => e.IsPastEmail).HasDefaultValue(false);
            entity.Property(e => e.IsPreferred).HasDefaultValue(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Person).WithMany(p => p.PersonEmails)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonEmail_Person");
        });

        modelBuilder.Entity<PersonLanguage>(entity =>
        {
            entity.ToTable("PersonLanguage");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            entity.HasOne(d => d.Language).WithMany(p => p.PersonLanguages)
                .HasForeignKey(d => d.LanguageId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonLanguage_Language");

            entity.HasOne(d => d.Person).WithMany(p => p.PersonLanguages)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonLanguage_Person");
        });

        modelBuilder.Entity<PersonLocation>(entity =>
        {
            entity.ToTable("PersonLocation");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            entity.HasOne(d => d.Location).WithMany(p => p.PersonLocations)
                .HasForeignKey(d => d.LocationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonLocation_Location");

            entity.HasOne(d => d.Person).WithMany(p => p.PersonLocations)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonLocation_Person");
        });

        modelBuilder.Entity<PersonPhone>(entity =>
        {
            entity.ToTable("PersonPhone");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CountryId).HasMaxLength(100);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Extn).HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsPastPhone).HasDefaultValue(false);
            entity.Property(e => e.IsPreferred).HasDefaultValue(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProvidedNumber).HasMaxLength(100);
            entity.Property(e => e.SanitizedNumber).HasMaxLength(100);

            entity.HasOne(d => d.Person).WithMany(p => p.PersonPhones)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonPhone_Person");
        });

        modelBuilder.Entity<PersonSocial>(entity =>
        {
            entity.ToTable("PersonSocial");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FullName).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsValid).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.RefId).HasMaxLength(50);
            entity.Property(e => e.Url).HasMaxLength(500);
            entity.Property(e => e.Username).HasMaxLength(100);

            entity.HasOne(d => d.Person).WithMany(p => p.PersonSocials)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonSocial_Person");

            entity.HasOne(d => d.Social).WithMany(p => p.PersonSocials)
                .HasForeignKey(d => d.SocialId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonSocial_Social");

            entity.HasOne(d => d.Source).WithMany(p => p.PersonSocials)
                .HasForeignKey(d => d.SourceId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonSocial_Source");
        });

        modelBuilder.Entity<PersonStory>(entity =>
        {
            entity.ToTable("PersonStory");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            entity.HasOne(d => d.Person).WithMany(p => p.PersonStories)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonStory_Person");

            entity.HasOne(d => d.Story).WithMany(p => p.PersonStories)
                .HasForeignKey(d => d.StoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonStory_Story");
        });

        modelBuilder.Entity<PersonTag>(entity =>
        {
            entity.ToTable("PersonTag");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            entity.HasOne(d => d.Person).WithMany(p => p.PersonTags)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonTag_Person");

            entity.HasOne(d => d.Tag).WithMany(p => p.PersonTags)
                .HasForeignKey(d => d.TagId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonTag_Tag");
        });

        modelBuilder.Entity<PersonUniversity>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_PersonStudy");

            entity.ToTable("PersonUniversity");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Degrees).HasMaxLength(500);
            entity.Property(e => e.EndDate).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsCurrent).HasDefaultValue(true);
            entity.Property(e => e.Majors).HasMaxLength(500);
            entity.Property(e => e.Notes).HasMaxLength(2000);
            entity.Property(e => e.StartDate).HasMaxLength(50);
            entity.Property(e => e.Title).HasMaxLength(200);

            entity.HasOne(d => d.Person).WithMany(p => p.PersonUniversities)
                .HasForeignKey(d => d.PersonId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonStudy_Person");

            entity.HasOne(d => d.Univerisity).WithMany(p => p.PersonUniversities)
                .HasForeignKey(d => d.UniverisityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PersonStudy_Study");
        });

        modelBuilder.Entity<PhoneConfig>(entity =>
        {
            entity.ToTable("PhoneConfig");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ApiKey)
                .HasMaxLength(50)
                .HasDefaultValue("");
            entity.Property(e => e.Did)
                .HasMaxLength(50)
                .HasColumnName("DID");
            entity.Property(e => e.Extn).HasMaxLength(50);
            entity.Property(e => e.InstanceUrl)
                .HasMaxLength(50)
                .HasDefaultValue("");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .HasDefaultValue("Virtual Number");
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.Prefix)
                .HasMaxLength(10)
                .HasDefaultValue("");
            entity.Property(e => e.Sipaddress)
                .HasMaxLength(50)
                .HasColumnName("SIPAddress");
        });

        modelBuilder.Entity<Plan>(entity =>
        {
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.DisplayName)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.OfferId).HasColumnName("OfferID");
            entity.Property(e => e.PlanGuid).HasColumnName("PlanGUID");
            entity.Property(e => e.PlanId)
                .HasMaxLength(100)
                .IsUnicode(false);
        });

        modelBuilder.Entity<PlanAttributeMapping>(entity =>
        {
            entity.HasKey(e => e.PlanAttributeId).HasName("PK__PlanAttr__8B476A98D1C9A78B");

            entity.ToTable("PlanAttributeMapping");

            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.OfferAttributeId).HasColumnName("OfferAttributeID");
        });

        modelBuilder.Entity<PlanAttributeOutput>(entity =>
        {
            entity.HasKey(e => e.RowNumber).HasName("PK__PlanAttr__AAAC09D827000881");

            entity.ToTable("PlanAttributeOutput");

            entity.Property(e => e.RowNumber).ValueGeneratedNever();
            entity.Property(e => e.DisplayName)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.Type)
                .HasMaxLength(225)
                .IsUnicode(false);
        });

        modelBuilder.Entity<PlanEventsMapping>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__PlanEven__3214EC07AD582879");

            entity.ToTable("PlanEventsMapping");

            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.FailureStateEmails)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.SuccessStateEmails)
                .HasMaxLength(225)
                .IsUnicode(false);
        });

        modelBuilder.Entity<PlanEventsOutPut>(entity =>
        {
            entity.HasKey(e => e.RowNumber).HasName("PK__PlanEven__AAAC09D877DE431D");

            entity.ToTable("PlanEventsOutPut");

            entity.Property(e => e.RowNumber).ValueGeneratedNever();
            entity.Property(e => e.EventsName)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.FailureStateEmails).IsUnicode(false);
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SuccessStateEmails).IsUnicode(false);
        });

        modelBuilder.Entity<Product>(entity =>
        {
            entity.ToTable("Product");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Aka)
                .HasMaxLength(500)
                .HasColumnName("AKA");
            entity.Property(e => e.EarlierName)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.Name).HasMaxLength(100);
        });

        modelBuilder.Entity<ProductCategory>(entity =>
        {
            entity.ToTable("ProductCategory");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");

            entity.HasOne(d => d.Category).WithMany(p => p.ProductCategories)
                .HasForeignKey(d => d.CategoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProductCategory_Category");

            entity.HasOne(d => d.Product).WithMany(p => p.ProductCategories)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProductCategory_Product");
        });

        modelBuilder.Entity<Purchase>(entity =>
        {
            entity.ToTable("Purchase");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IdpappId).HasColumnName("IDPAppId");
            entity.Property(e => e.Idpinstance).HasColumnName("IDPInstance");
            entity.Property(e => e.Idpkey).HasColumnName("IDPKey");
            entity.Property(e => e.IdpproviderUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderUrl");
            entity.Property(e => e.IdpsandboxUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPSandboxUrl");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.PurchaseCount).HasDefaultValue(0);
            entity.Property(e => e.PurchasedOn)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ValidTill).HasColumnType("datetime");
        });

        modelBuilder.Entity<Referral>(entity =>
        {
            entity.ToTable("Referral");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ShowSequence).HasDefaultValue(10);
        });

        modelBuilder.Entity<RelatedStory>(entity =>
        {
            entity.ToTable("RelatedStory");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ReportedDate).HasMaxLength(50);
            entity.Property(e => e.UpdatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Story1).WithMany(p => p.RelatedStories)
                .HasForeignKey(d => d.Story1Id)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RelatedStory_Story");
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.ToTable("Role");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsDefaultRole).HasDefaultValue(true);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ShowSequence).HasDefaultValue(200);
        });

        modelBuilder.Entity<Role1>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Roles__3214EC0769F8033D");

            entity.ToTable("Roles");

            entity.Property(e => e.Name)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Sector>(entity =>
        {
            entity.ToTable("Sector");

            entity.Property(e => e.Id)
                .HasMaxLength(2)
                .IsFixedLength();
            entity.Property(e => e.Text).HasMaxLength(200);
        });

        modelBuilder.Entity<Server>(entity =>
        {
            entity.ToTable("Server");

            entity.Property(e => e.Id)
                .HasDefaultValueSql("(newid())")
                .HasComment("\r\nservertype    url\r\n1                   authapi\r\n2                   dataapi\r\n3                   callapi\r\n4                   syncapi\r\n5                   fileapi\r\n6                   catchallapi\r\n7                   Provider Img Download Url\r\n");
            entity.Property(e => e.Notes).HasMaxLength(500);
            entity.Property(e => e.OrgId).HasMaxLength(50);
            entity.Property(e => e.Url).HasColumnName("URL");

            entity.HasOne(d => d.App).WithMany(p => p.Servers)
                .HasForeignKey(d => d.AppId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Server_App");
        });

        modelBuilder.Entity<Setting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Setting_1");

            entity.ToTable("Setting");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataType).HasDefaultValue((byte)1);
            entity.Property(e => e.DefaultValue).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(200);
        });

        modelBuilder.Entity<Signal>(entity =>
        {
            entity.ToTable("Signal");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AdditionalData)
                .HasMaxLength(1000)
                .IsFixedLength();
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Iteration).HasDefaultValue(1);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.TriggerOn).HasColumnType("datetime");

            entity.HasOne(d => d.SignalType).WithMany(p => p.Signals)
                .HasForeignKey(d => d.SignalTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Signal_SignalType");
        });

        modelBuilder.Entity<SignalMode>(entity =>
        {
            entity.ToTable("SignalMode");

            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Template)
                .HasMaxLength(10)
                .IsFixedLength();
        });

        modelBuilder.Entity<SignalType>(entity =>
        {
            entity.ToTable("SignalType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Condition).HasMaxLength(500);
            entity.Property(e => e.ExitCondition).HasMaxLength(500);
            entity.Property(e => e.Iteration).HasDefaultValue(1);
            entity.Property(e => e.LocalNotificationMessage).HasMaxLength(500);
            entity.Property(e => e.MailModoCampaignId).HasMaxLength(500);
            entity.Property(e => e.MinutesLater)
                .HasMaxLength(50)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.OneSignalMessage).HasMaxLength(500);
            entity.Property(e => e.SignalRroomId)
                .HasMaxLength(500)
                .HasColumnName("SignalRRoomId");
            entity.Property(e => e.Smsmessage)
                .HasMaxLength(500)
                .HasColumnName("SMSMessage");
            entity.Property(e => e.Type).HasMaxLength(50);
            entity.Property(e => e.UpdateSetting).HasMaxLength(500);
        });

        modelBuilder.Entity<Social>(entity =>
        {
            entity.ToTable("Social");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);
        });

        modelBuilder.Entity<Source>(entity =>
        {
            entity.ToTable("Source");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Name).HasMaxLength(50);
        });

        modelBuilder.Entity<Sso>(entity =>
        {
            entity.ToTable("SSO");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Cert).HasMaxLength(2000);
            entity.Property(e => e.ClientId).HasMaxLength(50);
            entity.Property(e => e.ClientSecret).HasMaxLength(100);
            entity.Property(e => e.Domain).HasMaxLength(50);
            entity.Property(e => e.Project).HasMaxLength(50);
            entity.Property(e => e.RedirectUrl)
                .HasMaxLength(500)
                .HasColumnName("RedirectURL");
            entity.Property(e => e.Type).HasMaxLength(50);
            entity.Property(e => e.Url)
                .HasMaxLength(500)
                .HasColumnName("URL");
        });

        modelBuilder.Entity<Stack>(entity =>
        {
            entity.ToTable("Stack");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");

            entity.HasOne(d => d.Company).WithMany(p => p.Stacks)
                .HasForeignKey(d => d.CompanyId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Stack_Company");

            entity.HasOne(d => d.Product).WithMany(p => p.Stacks)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Stack_Product");
        });

        modelBuilder.Entity<Story>(entity =>
        {
            entity.ToTable("Story");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Author).HasMaxLength(500);
            entity.Property(e => e.Cons).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Pros).HasMaxLength(1000);
            entity.Property(e => e.ReportedBy).HasMaxLength(50);
            entity.Property(e => e.ReportedDate).HasMaxLength(50);
            entity.Property(e => e.Summary).HasMaxLength(1000);
            entity.Property(e => e.Title).HasMaxLength(500);
            entity.Property(e => e.UpdatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.StorySourceNavigation).WithMany(p => p.Stories)
                .HasForeignKey(d => d.StorySource)
                .HasConstraintName("FK_Story_StorySource");
        });

        modelBuilder.Entity<StorySource>(entity =>
        {
            entity.ToTable("StorySource");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.UpdatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Url).HasMaxLength(500);
        });

        modelBuilder.Entity<StoryTag>(entity =>
        {
            entity.ToTable("StoryTag");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.UpdatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Story).WithMany(p => p.StoryTags)
                .HasForeignKey(d => d.StoryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_StoryTag_Story");

            entity.HasOne(d => d.Tag).WithMany(p => p.StoryTags)
                .HasForeignKey(d => d.TagId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_StoryTag_Tag");
        });

        modelBuilder.Entity<SubIndustry>(entity =>
        {
            entity.ToTable("SubIndustry");

            entity.Property(e => e.Id)
                .HasMaxLength(8)
                .IsFixedLength();
            entity.Property(e => e.IndustryId)
                .HasMaxLength(6)
                .IsFixedLength();
            entity.Property(e => e.Text).HasMaxLength(200);

            entity.HasOne(d => d.Industry).WithMany(p => p.SubIndustries)
                .HasForeignKey(d => d.IndustryId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_SubIndustry_Industry");
        });

        modelBuilder.Entity<Subscription>(entity =>
        {
            entity.Property(e => e.AmpplanId)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("AMPPlanId");
            entity.Property(e => e.Ampquantity).HasColumnName("AMPQuantity");
            entity.Property(e => e.AmpsubscriptionId)
                .HasDefaultValueSql("(newid())")
                .HasColumnName("AMPSubscriptionId");
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.ModifyDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.PurchaserEmail)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.SubscriptionStatus)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<SubscriptionAttributeValue>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__Subscrip__3214EC279AC4A566");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.OfferId).HasColumnName("OfferID");
            entity.Property(e => e.PlanId).HasColumnName("PlanID");
            entity.Property(e => e.Value)
                .HasMaxLength(225)
                .IsUnicode(false);
        });

        modelBuilder.Entity<SubscriptionAuditLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_SubscriptionAuditLog");

            entity.Property(e => e.Attribute)
                .HasMaxLength(20)
                .IsUnicode(false);
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.NewValue).IsUnicode(false);
            entity.Property(e => e.OldValue)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.SubscriptionId).HasColumnName("SubscriptionID");

            entity.HasOne(d => d.Subscription).WithMany(p => p.SubscriptionAuditLogs)
                .HasForeignKey(d => d.SubscriptionId)
                .HasConstraintName("FK__Subscript__Subsc__338A9CD5");
        });

        modelBuilder.Entity<SubscriptionEmailOutput>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("SubscriptionEmailOutput");

            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.Name)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.Value).IsUnicode(false);
        });

        modelBuilder.Entity<SubscriptionParametersOutput>(entity =>
        {
            entity.HasKey(e => e.RowNumber).HasName("PK__Subscrip__AAAC09D8BE9B4581");

            entity.ToTable("SubscriptionParametersOutput");

            entity.Property(e => e.RowNumber).ValueGeneratedNever();
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.DisplayName)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.Htmltype)
                .HasMaxLength(225)
                .IsUnicode(false)
                .HasColumnName("HTMLType");
            entity.Property(e => e.OfferAttributeId).HasColumnName("OfferAttributeID");
            entity.Property(e => e.Type)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.Value).IsUnicode(false);
            entity.Property(e => e.ValueType)
                .HasMaxLength(225)
                .IsUnicode(false);
            entity.Property(e => e.ValuesList)
                .HasMaxLength(225)
                .IsUnicode(false);
        });

        modelBuilder.Entity<Sync>(entity =>
        {
            entity.ToTable("Sync");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ReferenceId).HasMaxLength(200);
            entity.Property(e => e.TotalCount).HasDefaultValue(1);
            entity.Property(e => e.Updated).HasDefaultValue(1);
        });

        modelBuilder.Entity<Tag>(entity =>
        {
            entity.ToTable("Tag");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.ReadCount).HasDefaultValue(1L);
            entity.Property(e => e.RefCount).HasDefaultValue(1L);
            entity.Property(e => e.TagTypeId).HasDefaultValue(1);
        });

        modelBuilder.Entity<Theme>(entity =>
        {
            entity.ToTable("Theme");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Createddate).HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);

            entity.HasOne(d => d.App).WithMany(p => p.Themes)
                .HasForeignKey(d => d.AppId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Theme_App");
        });

        modelBuilder.Entity<Token>(entity =>
        {
            entity.ToTable("Token");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.BrowserData).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Ip)
                .HasMaxLength(50)
                .HasColumnName("IP");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Ttl)
                .HasColumnType("datetime")
                .HasColumnName("TTL");
            entity.Property(e => e.UserAgent).HasMaxLength(2000);
        });

        modelBuilder.Entity<University>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_Study");

            entity.ToTable("University");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Address).HasMaxLength(500);
            entity.Property(e => e.Aka)
                .HasMaxLength(1000)
                .HasColumnName("AKA");
            entity.Property(e => e.Country).HasMaxLength(500);
            entity.Property(e => e.Employees).HasMaxLength(100);
            entity.Property(e => e.Founded)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.Locality).HasMaxLength(500);
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.RefId).HasMaxLength(50);
        });

        modelBuilder.Entity<UniversityDomain>(entity =>
        {
            entity.ToTable("UniversityDomain");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Domain).WithMany(p => p.UniversityDomains)
                .HasForeignKey(d => d.DomainId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversityDomain_Domain");

            entity.HasOne(d => d.University).WithMany(p => p.UniversityDomains)
                .HasForeignKey(d => d.UniversityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversityDomain_University");
        });

        modelBuilder.Entity<UniversityLocation>(entity =>
        {
            entity.ToTable("UniversityLocation");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            entity.HasOne(d => d.Location).WithMany(p => p.UniversityLocations)
                .HasForeignKey(d => d.LocationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversityLocation_Location");

            entity.HasOne(d => d.University).WithMany(p => p.UniversityLocations)
                .HasForeignKey(d => d.UniversityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversityLocation_University");
        });

        modelBuilder.Entity<UniversityPhone>(entity =>
        {
            entity.ToTable("UniversityPhone");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CountryId).HasMaxLength(100);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Extn).HasMaxLength(100);
            entity.Property(e => e.IsPastPhone).HasDefaultValue(false);
            entity.Property(e => e.IsPreferred).HasDefaultValue(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProvidedNumber).HasMaxLength(100);
            entity.Property(e => e.SanitizedNumber).HasMaxLength(100);

            entity.HasOne(d => d.University).WithMany(p => p.UniversityPhones)
                .HasForeignKey(d => d.UniversityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversityPhone_University");
        });

        modelBuilder.Entity<UniversitySocial>(entity =>
        {
            entity.ToTable("UniversitySocial");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsValid).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.RefId).HasMaxLength(50);
            entity.Property(e => e.Url).HasMaxLength(500);
            entity.Property(e => e.Username).HasMaxLength(100);

            entity.HasOne(d => d.Social).WithMany(p => p.UniversitySocials)
                .HasForeignKey(d => d.SocialId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversitySocial_Social");

            entity.HasOne(d => d.Source).WithMany(p => p.UniversitySocials)
                .HasForeignKey(d => d.SourceId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversitySocial_Source");

            entity.HasOne(d => d.University).WithMany(p => p.UniversitySocials)
                .HasForeignKey(d => d.UniversityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversitySocial_University");
        });

        modelBuilder.Entity<UniversityStory>(entity =>
        {
            entity.ToTable("UniversityStory");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);

            entity.HasOne(d => d.University).WithMany(p => p.UniversityStories)
                .HasForeignKey(d => d.UniversityId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversityStory_University");
        });

        modelBuilder.Entity<UniversityTag>(entity =>
        {
            entity.ToTable("UniversityTag");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.UniversityTag1).HasColumnName("UniversityTag");

            entity.HasOne(d => d.Tag).WithMany(p => p.UniversityTags)
                .HasForeignKey(d => d.TagId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversityTag_Tag");

            entity.HasOne(d => d.UniversityTag1Navigation).WithMany(p => p.UniversityTags)
                .HasForeignKey(d => d.UniversityTag1)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UniversityTag_University");
        });

        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("User");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Alias).HasMaxLength(500);
            entity.Property(e => e.AnalyticsId).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ChargebeeCustomerId).HasMaxLength(500);
            entity.Property(e => e.Company).HasMaxLength(200);
            entity.Property(e => e.CountryId).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CurrentTimeZone).HasMaxLength(20);
            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.FirstLogin)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FirstName).HasMaxLength(500);
            entity.Property(e => e.InviteCode).HasMaxLength(50);
            entity.Property(e => e.InvitedBy).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsTestUser).HasDefaultValue(false);
            entity.Property(e => e.LastAuthenticated).HasColumnType("datetime");
            entity.Property(e => e.LastLogin)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastName).HasMaxLength(500);
            entity.Property(e => e.MiddleName).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Password).HasMaxLength(50);
            entity.Property(e => e.PreviousLogin)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Ptype).HasColumnName("PType");
            entity.Property(e => e.UserType).HasDefaultValue((byte)1);
        });

        modelBuilder.Entity<User1>(entity =>
        {
            entity.HasKey(e => e.UserId);

            entity.ToTable("Users");

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.EmailAddress)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.FullName)
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<UserCredit>(entity =>
        {
            entity.ToTable("UserCredit");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CreditsAdded).HasDefaultValue(1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<UserDevice>(entity =>
        {
            entity.ToTable("UserDevice");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CalendarNotifications).HasDefaultValue(true);
            entity.Property(e => e.CountryId).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Crmpush)
                .HasDefaultValue(true)
                .HasColumnName("CRMPush");
            entity.Property(e => e.ImportContact).HasDefaultValue(true);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Number).HasMaxLength(50);
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.Ptype).HasColumnName("PType");
            entity.Property(e => e.PuchRequired).HasDefaultValue(true);
            entity.Property(e => e.SanitizedNumber).HasMaxLength(50);
        });

        modelBuilder.Entity<UserEmail>(entity =>
        {
            entity.ToTable("UserEmail");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DisplayName).HasMaxLength(500);
            entity.Property(e => e.Email).HasMaxLength(200);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<UserIm>(entity =>
        {
            entity.ToTable("UserIM");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Account).HasMaxLength(200);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ServiceLabel).HasMaxLength(200);
        });

        modelBuilder.Entity<UserPhone>(entity =>
        {
            entity.ToTable("UserPhone");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CountryId).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Extn).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProvidedNumber).HasMaxLength(50);
            entity.Property(e => e.Ptype).HasColumnName("PType");
            entity.Property(e => e.SanitizedNumber).HasMaxLength(50);
            entity.Property(e => e.Type).HasDefaultValue(1);
        });

        modelBuilder.Entity<UserProvider>(entity =>
        {
            entity.ToTable("UserProvider");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AccountId).HasMaxLength(500);
            entity.Property(e => e.ActiveFrom).HasColumnType("datetime");
            entity.Property(e => e.ActiveTill).HasColumnType("datetime");
            entity.Property(e => e.BulkDataUrl).HasMaxLength(1000);
            entity.Property(e => e.CeinstanceId)
                .HasMaxLength(200)
                .HasColumnName("CEInstanceId");
            entity.Property(e => e.CeinstanceUrl)
                .HasMaxLength(1000)
                .HasColumnName("CEInstanceUrl");
            entity.Property(e => e.CeorgId)
                .HasMaxLength(200)
                .HasColumnName("CEOrgId");
            entity.Property(e => e.CeuserId)
                .HasMaxLength(200)
                .HasColumnName("CEUserId");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataUrl).HasColumnName("DataURL");
            entity.Property(e => e.DisplayName).HasMaxLength(500);
            entity.Property(e => e.EmailAddress).HasMaxLength(500);
            entity.Property(e => e.IdpproviderInstance)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderInstance");
            entity.Property(e => e.IdpproviderUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderUrl");
            entity.Property(e => e.Imapserver)
                .HasMaxLength(50)
                .HasColumnName("IMAPServer");
            entity.Property(e => e.InstanceUrl).HasMaxLength(1000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsClaimed).HasDefaultValue(false);
            entity.Property(e => e.IsPaymentStoped).HasDefaultValue(false);
            entity.Property(e => e.IsProvisioned).HasDefaultValue(true);
            entity.Property(e => e.IsSsl).HasColumnName("IsSSL");
            entity.Property(e => e.IsTrial).HasDefaultValue(false);
            entity.Property(e => e.IsTrialCompleted).HasDefaultValue(false);
            entity.Property(e => e.LastCheckedDate).HasColumnType("datetime");
            entity.Property(e => e.LastFullSyncDate).HasColumnType("datetime");
            entity.Property(e => e.LastUpdateDate).HasColumnType("datetime");
            entity.Property(e => e.MailboxGuid).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Password).HasMaxLength(50);
            entity.Property(e => e.Popserver)
                .HasMaxLength(50)
                .HasColumnName("POPServer");
            entity.Property(e => e.Port).HasMaxLength(200);
            entity.Property(e => e.Priority).HasDefaultValue(100);
            entity.Property(e => e.ProductId).HasMaxLength(1500);
            entity.Property(e => e.ProfileUrl)
                .HasMaxLength(1000)
                .HasDefaultValueSql("(NULL)");
            entity.Property(e => e.PurchaseId)
                .HasMaxLength(1500)
                .HasColumnName("PurchaseID");
            entity.Property(e => e.ReferenceId).HasMaxLength(500);
            entity.Property(e => e.Smtpserver)
                .HasMaxLength(50)
                .HasColumnName("SMTPServer");
            entity.Property(e => e.Tenant).HasMaxLength(50);
            entity.Property(e => e.TrialEnd).HasColumnType("datetime");
            entity.Property(e => e.TrialStart).HasColumnType("datetime");
            entity.Property(e => e.Type).HasMaxLength(500);
            entity.Property(e => e.Url).HasColumnName("URL");
            entity.Property(e => e.WebhookExpiration).HasColumnType("datetime");
        });

        modelBuilder.Entity<UserProviderIdentifier>(entity =>
        {
            entity.ToTable("UserProviderIdentifier");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.UserProvider).WithMany(p => p.UserProviderIdentifiers)
                .HasForeignKey(d => d.UserProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserProviderIdentifier_UserProvider");
        });

        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.ToTable("UserRole");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
        });

        modelBuilder.Entity<UserSetting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_UserSetting_1");

            entity.ToTable("UserSetting");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataType).HasDefaultValue((byte)1);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Value).HasMaxLength(2000);

            entity.HasOne(d => d.Setting).WithMany(p => p.UserSettings)
                .HasForeignKey(d => d.SettingId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserSetting_Setting1");
        });

        modelBuilder.Entity<UserSocial>(entity =>
        {
            entity.ToTable("UserSocial");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DisplayName).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.SocialNetworkId).HasMaxLength(500);
            entity.Property(e => e.Type).HasDefaultValue(13);
        });

        modelBuilder.Entity<UserStatus>(entity =>
        {
            entity.ToTable("UserStatus");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.IOscontacts).HasColumnName("iOSContacts");
            entity.Property(e => e.IsDrakTheme).HasDefaultValue(true);
            entity.Property(e => e.Otp).HasColumnName("OTP");
            entity.Property(e => e.ShouldShowAds).HasDefaultValue(true);
        });

        modelBuilder.Entity<UserStorage>(entity =>
        {
            entity.ToTable("UserStorage");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(2250);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.Type).HasComment("Type bit - 0 for folder, 1 for search");
        });

        modelBuilder.Entity<UserTask>(entity =>
        {
            entity.ToTable("UserTask");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.VerificationInfo).HasMaxLength(500);

            entity.HasOne(d => d.Task).WithMany(p => p.UserTasks)
                .HasForeignKey(d => d.TaskId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserTask_Task");
        });

        modelBuilder.Entity<Usersettingview>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("usersettingview");

            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.Name).HasMaxLength(200);
            entity.Property(e => e.Value).HasMaxLength(2000);
        });

        modelBuilder.Entity<VUserDevice>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vUserDevices");

            entity.Property(e => e.Email).HasMaxLength(500);
        });

        modelBuilder.Entity<ValueType>(entity =>
        {
            entity.HasKey(e => e.ValueTypeId).HasName("PK__ValueTyp__A51E9C5AFAB054EF");
        
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.Htmltype)
                .HasMaxLength(225)
                .IsUnicode(false)
                .HasColumnName("HTMLType");
            entity.Property(e => e.ValueType1)
                .HasMaxLength(225)
                .IsUnicode(false)
                .HasColumnName("ValueType");
        });

        modelBuilder.Entity<WebJobSubscriptionStatus>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__WebJobSu__3214EC27CEE21DA8");

            entity.ToTable("WebJobSubscriptionStatus");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Description).IsUnicode(false);
            entity.Property(e => e.InsertDate).HasColumnType("datetime");
            entity.Property(e => e.SubscriptionStatus)
                .HasMaxLength(225)
                .IsUnicode(false);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
