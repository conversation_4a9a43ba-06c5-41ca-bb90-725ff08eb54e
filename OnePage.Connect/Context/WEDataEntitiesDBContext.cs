﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using OnePage.Connect.Migrations;
using Action = OnePage.Connect.Migrations.Action;

namespace OnePage.Connect.Context;

public partial class WEDataEntitiesDBContext : DbContext
{
    public WEDataEntitiesDBContext()
    {
    }

    public WEDataEntitiesDBContext(DbContextOptions<WEDataEntitiesDBContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Action> Actions { get; set; }

    public virtual DbSet<ActionUrl> ActionUrls { get; set; }

    public virtual DbSet<ActivityType> ActivityTypes { get; set; }

    public virtual DbSet<AppProvider> AppProviders { get; set; }

    public virtual DbSet<AppProviderType> AppProviderTypes { get; set; }

    public virtual DbSet<C> Cs { get; set; }

    public virtual DbSet<Card> Cards { get; set; }

    public virtual DbSet<CardUrl> CardUrls { get; set; }

    public virtual DbSet<Conference> Conferences { get; set; }

    public virtual DbSet<CopilotAadModel> CopilotAadModels { get; set; }

    public virtual DbSet<Country> Countries { get; set; }

    public virtual DbSet<DeviceType> DeviceTypes { get; set; }

    public virtual DbSet<Domain> Domains { get; set; }

    public virtual DbSet<FeatureTrigger> FeatureTriggers { get; set; }

    public virtual DbSet<FieldsToDisplay> FieldsToDisplays { get; set; }

    public virtual DbSet<FreeDomain> FreeDomains { get; set; }

    public virtual DbSet<Header> Headers { get; set; }

    public virtual DbSet<Help> Helps { get; set; }

    public virtual DbSet<I> Is { get; set; }

    public virtual DbSet<Identifier> Identifiers { get; set; }

    public virtual DbSet<Language> Languages { get; set; }

    public virtual DbSet<Lookup> Lookups { get; set; }

    public virtual DbSet<M> Ms { get; set; }

    public virtual DbSet<Module> Modules { get; set; }

    public virtual DbSet<P> Ps { get; set; }

    public virtual DbSet<Pc> Pcs { get; set; }

    public virtual DbSet<Post> Posts { get; set; }

    public virtual DbSet<Provider> Providers { get; set; }

    public virtual DbSet<ProviderIdentifier> ProviderIdentifiers { get; set; }

    public virtual DbSet<ProviderType> ProviderTypes { get; set; }

    public virtual DbSet<ProviderUrl> ProviderUrls { get; set; }

    public virtual DbSet<ProvidersLink> ProvidersLinks { get; set; }

    public virtual DbSet<Recommend> Recommends { get; set; }

    public virtual DbSet<RoleTemplate> RoleTemplates { get; set; }

    public virtual DbSet<SocialType> SocialTypes { get; set; }

    public virtual DbSet<T> Ts { get; set; }

    public virtual DbSet<Translate> Translates { get; set; }

    public virtual DbSet<Trigger> Triggers { get; set; }

    public virtual DbSet<Url> Urls { get; set; }

    public virtual DbSet<Urltype> Urltypes { get; set; }

//     protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
// #warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
//         => optionsBuilder.UseSqlServer("Server=eu-ne-sd.database.windows.net;Database=eu-fc-ss-dt;User=eu-ne-sd;Password=**************;MultipleActiveResultSets=true;");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Action>(entity =>
        {
            entity.ToTable("Action");
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Command).HasMaxLength(250);
            entity.Property(e => e.CommandParameter).HasMaxLength(250);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Icon).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsPostCallAction).HasDefaultValue(false);
            entity.Property(e => e.IsVisible).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Note).HasMaxLength(50);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.Title).HasMaxLength(100);
            entity.Property(e => e.VisibilityConditions).HasMaxLength(1000);
        });

        modelBuilder.Entity<ActionUrl>(entity =>
        {
            entity.ToTable("ActionURL");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Action).WithMany(p => p.ActionUrls)
                .HasForeignKey(d => d.ActionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ActionURL_Action");

            entity.HasOne(d => d.Url).WithMany(p => p.ActionUrls)
                .HasForeignKey(d => d.UrlId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ActionURL_URL");
        });

        modelBuilder.Entity<ActivityType>(entity =>
        {
            entity.ToTable("ActivityType");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedAt).HasDefaultValueSql("(sysutcdatetime())");
            entity.Property(e => e.Notes).HasMaxLength(300);
        });

        modelBuilder.Entity<AppProvider>(entity =>
        {
            entity.ToTable("AppProvider");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Createddate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.Provider).WithMany(p => p.AppProviders)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_AppProvider_Provider");
        });

        modelBuilder.Entity<AppProviderType>(entity =>
        {
            entity.ToTable("AppProviderType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Createddate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<C>(entity =>
        {
            entity.ToTable("C");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
        });

        modelBuilder.Entity<Card>(entity =>
        {
            entity.ToTable("Card");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ActionCommand).HasMaxLength(50);
            entity.Property(e => e.ActionName).HasMaxLength(50);
            entity.Property(e => e.ChartType).HasDefaultValue(0);
            entity.Property(e => e.CountPath).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataPath).HasMaxLength(1000);
            entity.Property(e => e.DetailPath).HasMaxLength(1000);
            entity.Property(e => e.Entity).HasMaxLength(1000);
            entity.Property(e => e.FieldsToShow).HasMaxLength(1000);
            entity.Property(e => e.HasActions).HasDefaultValue(true);
            entity.Property(e => e.Icon).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsCompany).HasDefaultValue(false);
            entity.Property(e => e.IsGlobal).HasDefaultValue(true);
            entity.Property(e => e.IsList).HasDefaultValue(false);
            entity.Property(e => e.ListToBind).HasMaxLength(50);
            entity.Property(e => e.MaxHeight).HasDefaultValue(250);
            entity.Property(e => e.ModelToBind).HasMaxLength(50);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Note).HasMaxLength(50);
            entity.Property(e => e.ShowCounter).HasDefaultValue(true);
            entity.Property(e => e.ShowFooter).HasDefaultValue(true);
            entity.Property(e => e.ShowHeader).HasDefaultValue(true);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.Title).HasMaxLength(100);
        });

        modelBuilder.Entity<CardUrl>(entity =>
        {
            entity.ToTable("CardURL");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);

            entity.HasOne(d => d.Card).WithMany(p => p.CardUrls)
                .HasForeignKey(d => d.CardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CardURL_Card");

            entity.HasOne(d => d.Url).WithMany(p => p.CardUrls)
                .HasForeignKey(d => d.UrlId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_CardURL_URL");
        });

        modelBuilder.Entity<Conference>(entity =>
        {
            entity.ToTable("Conference");

            entity.Property(e => e.DialIn).HasMaxLength(1000);
            entity.Property(e => e.Dtmf)
                .HasMaxLength(50)
                .HasColumnName("DTMF");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.OneClick).HasMaxLength(100);
            entity.Property(e => e.Sipurl)
                .HasMaxLength(50)
                .HasColumnName("SIPUrl");
            entity.Property(e => e.Source).HasMaxLength(50);
            entity.Property(e => e.Url).HasMaxLength(1000);
        });

        modelBuilder.Entity<CopilotAadModel>(entity =>
        {
            entity.ToTable("CopilotAadModel");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<Country>(entity =>
        {
            entity.ToTable("Country");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.Alpha2).HasMaxLength(2);
            entity.Property(e => e.Alpha3).HasMaxLength(3);
            entity.Property(e => e.Continent).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IntlPrefix).HasMaxLength(10);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Prefix).HasMaxLength(10);
            entity.Property(e => e.ServerZone).HasDefaultValue((byte)1);
        });

        modelBuilder.Entity<DeviceType>(entity =>
        {
            entity.ToTable("DeviceType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);
        });

        modelBuilder.Entity<Domain>(entity =>
        {
            entity.ToTable("Domain");
        
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Address).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsFree).HasDefaultValue(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
        });

        modelBuilder.Entity<FeatureTrigger>(entity =>
        {
            entity.ToTable("FeatureTrigger");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Action).HasMaxLength(500);
            entity.Property(e => e.Action2).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsFeature).HasDefaultValue(true);
            entity.Property(e => e.Message).HasMaxLength(500);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.PageName).HasMaxLength(400);
        });

        modelBuilder.Entity<FieldsToDisplay>(entity =>
        {
            entity.ToTable("FieldsToDisplay");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Condition).HasMaxLength(500);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DisplayName).HasMaxLength(1000);
            entity.Property(e => e.Entity).HasMaxLength(500);
            entity.Property(e => e.ExtLink).HasMaxLength(1000);
            entity.Property(e => e.Icon).HasMaxLength(50);
            entity.Property(e => e.IntLink).HasMaxLength(1000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsDirect).HasDefaultValue(false);
            entity.Property(e => e.IsPhone).HasDefaultValue(false);
            entity.Property(e => e.ModelAttribute).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.NameMatch).HasMaxLength(1500);
            entity.Property(e => e.OrgId).HasMaxLength(1000);
            entity.Property(e => e.OrgRoleId).HasMaxLength(1000);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.Type).HasDefaultValue((byte)1);
            entity.Property(e => e.ValueMatch).HasMaxLength(1500);

            entity.HasOne(d => d.Card).WithMany(p => p.FieldsToDisplays)
                .HasForeignKey(d => d.CardId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_FieldsToDisplay_Card");
        });

        modelBuilder.Entity<FreeDomain>(entity =>
        {
            entity.ToTable("FreeDomain");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.DomainName).HasMaxLength(1000);
        });

        modelBuilder.Entity<Header>(entity =>
        {
            entity.ToTable("Header");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Prefix).HasMaxLength(1000);
            entity.Property(e => e.Urlid).HasColumnName("URLId");

            entity.HasOne(d => d.Url).WithMany(p => p.Headers)
                .HasForeignKey(d => d.Urlid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Header_URL");
        });

        modelBuilder.Entity<Help>(entity =>
        {
            entity.ToTable("Help");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.DeviceType).HasMaxLength(50);
            entity.Property(e => e.Locale).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.OrgId).HasMaxLength(1000);
            entity.Property(e => e.ProviderId).HasMaxLength(1000);
            entity.Property(e => e.ProviderTypeId).HasMaxLength(1000);
            entity.Property(e => e.Url).HasMaxLength(100);
            entity.Property(e => e.UserId).HasMaxLength(1000);
        });

        modelBuilder.Entity<I>(entity =>
        {
            entity.ToTable("I");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Mid).HasColumnName("MId");
            entity.Property(e => e.Pid).HasColumnName("PId");

            entity.HasOne(d => d.MidNavigation).WithMany(p => p.Is)
                .HasForeignKey(d => d.Mid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_I_M");

            entity.HasOne(d => d.PidNavigation).WithMany(p => p.Is)
                .HasForeignKey(d => d.Pid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_I_P");
        });

        modelBuilder.Entity<Identifier>(entity =>
        {
            entity.ToTable("Identifier");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsBeforeAuth).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);
            entity.Property(e => e.Type).HasDefaultValue((byte)1);
            entity.Property(e => e.Urlid).HasColumnName("URLId");

            entity.HasOne(d => d.Provider).WithMany(p => p.Identifiers)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Identifier_Provider");
        });

        modelBuilder.Entity<Language>(entity =>
        {
            entity.ToTable("Language");

            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FileName)
                .HasMaxLength(50)
                .HasDefaultValue("Resources.resx");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Locale).HasMaxLength(50);
            entity.Property(e => e.Ltr)
                .HasDefaultValue(true)
                .HasColumnName("LTR");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ShowSequence).HasDefaultValue(10);
        });

        modelBuilder.Entity<Lookup>(entity =>
        {
            entity.ToTable("Lookup");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.LookUpId).HasMaxLength(100);
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Value)
                .HasMaxLength(100)
                .HasDefaultValueSql("((1))");
        });

        modelBuilder.Entity<M>(entity =>
        {
            entity.ToTable("M");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.D).HasColumnType("datetime");
            entity.Property(e => e.Pid).HasColumnName("PId");
            entity.Property(e => e.Tid).HasColumnName("TId");

            entity.HasOne(d => d.PidNavigation).WithMany(p => p.Ms)
                .HasForeignKey(d => d.Pid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_M_P");

            entity.HasOne(d => d.TidNavigation).WithMany(p => p.Ms)
                .HasForeignKey(d => d.Tid)
                .HasConstraintName("FK_M_T");
        });

        modelBuilder.Entity<Module>(entity =>
        {
            entity.ToTable("Module");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ReferenceId).HasMaxLength(1000);

            entity.HasOne(d => d.Provider).WithMany(p => p.Modules)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Module_Provider");
        });

        modelBuilder.Entity<P>(entity =>
        {
            entity.ToTable("P");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
        });

        modelBuilder.Entity<Pc>(entity =>
        {
            entity.ToTable("PC");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Cid).HasColumnName("CId");
            entity.Property(e => e.Pid).HasColumnName("PId");

            entity.HasOne(d => d.CidNavigation).WithMany(p => p.Pcs)
                .HasForeignKey(d => d.Cid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PC_C");

            entity.HasOne(d => d.PidNavigation).WithMany(p => p.Pcs)
                .HasForeignKey(d => d.Pid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PC_P");
        });

        modelBuilder.Entity<Post>(entity =>
        {
            entity.ToTable("Post");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Urlid).HasColumnName("URLId");

            entity.HasOne(d => d.Url).WithMany(p => p.Posts)
                .HasForeignKey(d => d.Urlid)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Post_URL");
        });

        modelBuilder.Entity<Provider>(entity =>
        {
            entity.ToTable("Provider");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AdvancedSearch).HasMaxLength(500);
            entity.Property(e => e.AndroidApp).HasMaxLength(500);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IOsapp)
                .HasMaxLength(500)
                .HasColumnName("iOSApp");
            entity.Property(e => e.InShort).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsCustomizationRequired).HasDefaultValue(true);
            entity.Property(e => e.IsVisible).HasDefaultValue(true);
            entity.Property(e => e.LogInType).HasDefaultValue((byte)1);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.PageUrl).HasMaxLength(200);
            entity.Property(e => e.Prefix).HasMaxLength(10);
            entity.Property(e => e.Price).HasDefaultValue(0.0);
            entity.Property(e => e.Reference).HasMaxLength(50);
            entity.Property(e => e.RelatedProviders).HasMaxLength(2500);
            entity.Property(e => e.RequestMonthlyRange)
                .HasMaxLength(500)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.RequestYearlyRange)
                .HasMaxLength(500)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.ShowSequence).HasDefaultValue(10000);
            entity.Property(e => e.WebSite).HasMaxLength(500);
            entity.Property(e => e.Windows10).HasMaxLength(500);
            entity.Property(e => e.WindowsPhone).HasMaxLength(500);
            entity.Property(e => e.YearlyPrice).HasDefaultValue(0.0);

            entity.HasOne(d => d.ProviderType).WithMany(p => p.Providers)
                .HasForeignKey(d => d.ProviderTypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Provider_ProviderType");
        });

        modelBuilder.Entity<ProviderIdentifier>(entity =>
        {
            entity.ToTable("ProviderIdentifier");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Provider).WithMany(p => p.ProviderIdentifiers)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProviderIdentifier_Provider");
        });

        modelBuilder.Entity<ProviderType>(entity =>
        {
            entity.ToTable("ProviderType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Priority).HasDefaultValue(100);
        });

        modelBuilder.Entity<ProviderUrl>(entity =>
        {
            entity.ToTable("ProviderURL");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Provider).WithMany(p => p.ProviderUrls)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProviderURL_Provider");

            entity.HasOne(d => d.Url).WithMany(p => p.ProviderUrls)
                .HasForeignKey(d => d.UrlId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ProviderURL_URL");
        });

        modelBuilder.Entity<ProvidersLink>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("providers_link");

            entity.Property(e => e.AppprovidertypeAppid).HasColumnName("appprovidertype_appid");
        });

        modelBuilder.Entity<Recommend>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("Recommend");

            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DarkImage).HasMaxLength(100);
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ImageHeight).HasDefaultValue(50.0);
            entity.Property(e => e.ImageWidth).HasDefaultValue(300.0);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LightImage)
                .HasMaxLength(100)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.Link).HasMaxLength(100);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.SettingValue).HasMaxLength(50);
        });

        modelBuilder.Entity<RoleTemplate>(entity =>
        {
            entity.ToTable("RoleTemplate");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Actions).HasMaxLength(1000);
            entity.Property(e => e.Cards).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.Triggers).HasMaxLength(1000);

            entity.HasOne(d => d.Provider).WithMany(p => p.RoleTemplates)
                .HasForeignKey(d => d.ProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_RoleTemplate_Provider");
        });

        modelBuilder.Entity<SocialType>(entity =>
        {
            entity.ToTable("SocialType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Icon).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LookUpUrl).HasMaxLength(1500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.SearchFor).HasMaxLength(500);
            entity.Property(e => e.SearchUrl).HasMaxLength(1500);
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);
        });

        modelBuilder.Entity<T>(entity =>
        {
            entity.ToTable("T");
        });

        modelBuilder.Entity<Translate>(entity =>
        {
            entity.ToTable("Translate");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsAutoTranslated).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ReferenceId).HasMaxLength(50);
            entity.Property(e => e.ShowSequence).HasDefaultValue(10);

            entity.HasOne(d => d.Language).WithMany(p => p.Translates)
                .HasForeignKey(d => d.LanguageId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Translate_Language");
        });

        modelBuilder.Entity<Trigger>(entity =>
        {
            entity.ToTable("Trigger");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataUrl).HasMaxLength(2000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(1000);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.TriggerType).HasDefaultValue((byte)1);
        });

        modelBuilder.Entity<Url>(entity =>
        {
            entity.ToTable("URL");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataPath).HasMaxLength(2000);
            entity.Property(e => e.HasHeaders).HasDefaultValue(true);
            entity.Property(e => e.Httptype)
                .HasDefaultValue((byte)1)
                .HasColumnName("HTTPType");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsApi)
                .HasDefaultValue(true)
                .HasColumnName("IsAPI");
            entity.Property(e => e.IsBeforeAuth).HasDefaultValue(false);
            entity.Property(e => e.IsMobile).HasDefaultValue(true);
            entity.Property(e => e.IsWeb).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(1000);
            entity.Property(e => e.ShowOrder).HasDefaultValue(1000);
            entity.Property(e => e.UrltypeId).HasColumnName("URLTypeId");

            entity.HasOne(d => d.Urltype).WithMany(p => p.Urls)
                .HasForeignKey(d => d.UrltypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_URL_URLType");
        });

        modelBuilder.Entity<Urltype>(entity =>
        {
            entity.ToTable("URLType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ShowOrder).HasDefaultValue(1000);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
