﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using OnePage.Connect.Migrations;

namespace OnePage.Connect.Context;

public partial class WEOrgEntitiesDBContext : DbContext
{
    public WEOrgEntitiesDBContext()
    {
    }

    public WEOrgEntitiesDBContext(DbContextOptions<WEOrgEntitiesDBContext> options)
        : base(options)
    {
    }

    public virtual DbSet<IdPtype> IdPtypes { get; set; }

    public virtual DbSet<Org> Orgs { get; set; }

    public virtual DbSet<OrgCredit> OrgCredits { get; set; }

    public virtual DbSet<OrgDirectory> OrgDirectories { get; set; }

    public virtual DbSet<OrgIdP> OrgIdPs { get; set; }

    public virtual DbSet<OrgIdentifier> OrgIdentifiers { get; set; }

    public virtual DbSet<OrgProvider> OrgProviders { get; set; }

    public virtual DbSet<OrgProviderTigger> OrgProviderTiggers { get; set; }

    public virtual DbSet<OrgRole> OrgRoles { get; set; }

    public virtual DbSet<OrgRoleTemplate> OrgRoleTemplates { get; set; }

    public virtual DbSet<OrgUser> OrgUsers { get; set; }

    public virtual DbSet<PhoneConfig> PhoneConfigs { get; set; }

    public virtual DbSet<Provision> Provisions { get; set; }

    public virtual DbSet<Sysdiagram> Sysdiagrams { get; set; }

    public virtual DbSet<UserProvider> UserProviders { get; set; }

    public virtual DbSet<UserProviderIdentifier> UserProviderIdentifiers { get; set; }

    public virtual DbSet<UserRequest> UserRequests { get; set; }

    public virtual DbSet<UserStorage> UserStorages { get; set; }

//     protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
// #warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
//         => optionsBuilder.UseSqlServer("Server=eu-ne-sd.database.windows.net;Database=eu-fc-ss-or;User=eu-ne-sd;Password=**************;MultipleActiveResultSets=true;");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<IdPtype>(entity =>
        {
            entity.ToTable("IdPType");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Name).HasMaxLength(100);
        });

        modelBuilder.Entity<Org>(entity =>
        {
            entity.ToTable("Org");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ActionCount).HasDefaultValue(10);
            entity.Property(e => e.BetaValidTill).HasColumnType("datetime");
            entity.Property(e => e.CardCount).HasDefaultValue(3);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FreeAccounts).HasDefaultValue(1);
            entity.Property(e => e.Idpinstance).HasColumnName("IDPInstance");
            entity.Property(e => e.Idpkey).HasColumnName("IDPKey");
            entity.Property(e => e.IdpproviderId).HasColumnName("IDPProviderId");
            entity.Property(e => e.IdpproviderUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderUrl");
            entity.Property(e => e.IdpsandboxUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPSandboxUrl");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsPaid).HasDefaultValue(false);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.PurchasedOn)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.TriggerCount).HasDefaultValue(2);
            entity.Property(e => e.ValidTill).HasColumnType("datetime");
        });

        modelBuilder.Entity<OrgCredit>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__OrgCredi__3214EC070EE7C66D");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValueSql("('0')");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.NoofCredits).HasMaxLength(10);
        });

        modelBuilder.Entity<OrgDirectory>(entity =>
        {
            entity.ToTable("OrgDirectory");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CountryId).HasMaxLength(50);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Email).HasMaxLength(500);
            entity.Property(e => e.FirstName).HasMaxLength(500);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastName).HasMaxLength(500);
            entity.Property(e => e.MiddleName).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProvidedNumber).HasMaxLength(50);
            entity.Property(e => e.Salutation).HasMaxLength(100);
            entity.Property(e => e.SanitizedNumber).HasMaxLength(50);

            entity.HasOne(d => d.Org).WithMany(p => p.OrgDirectories)
                .HasForeignKey(d => d.OrgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgDirectory_Org");
        });

        modelBuilder.Entity<OrgIdP>(entity =>
        {
            entity.ToTable("OrgIdP");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Certificate).HasMaxLength(4000);
            entity.Property(e => e.CertificatePath).HasMaxLength(100);
            entity.Property(e => e.EmailDomain).HasMaxLength(50);
            entity.Property(e => e.IdPtypeId).HasColumnName("IdPTypeId");
            entity.Property(e => e.IdentityProviderIssuer).HasMaxLength(1000);
            entity.Property(e => e.Name).HasMaxLength(1000);
            entity.Property(e => e.Saml20endPoint)
                .HasMaxLength(1000)
                .HasColumnName("SAML20EndPoint");
            entity.Property(e => e.SingleLogoutServiceUrl).HasMaxLength(1000);
            entity.Property(e => e.SingleSignOnServiceUrl).HasMaxLength(1000);

            entity.HasOne(d => d.IdPtype).WithMany(p => p.OrgIdPs)
                .HasForeignKey(d => d.IdPtypeId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgIdP_IdPType");

            entity.HasOne(d => d.Org).WithMany(p => p.OrgIdPs)
                .HasForeignKey(d => d.OrgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgIdP_Org");
        });

        modelBuilder.Entity<OrgIdentifier>(entity =>
        {
            entity.ToTable("OrgIdentifier");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ShowSequence).HasDefaultValue(100);

            entity.HasOne(d => d.OrgProvider).WithMany(p => p.OrgIdentifiers)
                .HasForeignKey(d => d.OrgProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgIdentifier_OrgProvider");
        });

        modelBuilder.Entity<OrgProvider>(entity =>
        {
            entity.ToTable("OrgProvider");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CanSaveForOffline).HasDefaultValue(true);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Crmcontact).HasColumnName("CRMContact");
            entity.Property(e => e.Crmlead).HasColumnName("CRMLead");
            entity.Property(e => e.Imapport)
                .HasMaxLength(10)
                .HasColumnName("IMAPPort");
            entity.Property(e => e.Imapserver)
                .HasMaxLength(50)
                .HasColumnName("IMAPServer");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsCustomized).HasDefaultValue(false);
            entity.Property(e => e.IsSsl)
                .HasDefaultValue(1)
                .HasColumnName("IsSSL");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Org).WithMany(p => p.OrgProviders)
                .HasForeignKey(d => d.OrgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgProvider_Org");
        });

        modelBuilder.Entity<OrgProviderTigger>(entity =>
        {
            entity.ToTable("OrgProviderTigger");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Org).WithMany(p => p.OrgProviderTiggers)
                .HasForeignKey(d => d.OrgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgProviderTigger_Org");

            entity.HasOne(d => d.OrgProvider).WithMany(p => p.OrgProviderTiggers)
                .HasForeignKey(d => d.OrgProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgProviderTigger_OrgProvider");
        });

        modelBuilder.Entity<OrgRole>(entity =>
        {
            entity.ToTable("OrgRole");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Actions).HasMaxLength(1000);
            entity.Property(e => e.Cards).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.Triggers).HasMaxLength(1000);

            entity.HasOne(d => d.Org).WithMany(p => p.OrgRoles)
                .HasForeignKey(d => d.OrgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgRole_Org");
        });

        modelBuilder.Entity<OrgRoleTemplate>(entity =>
        {
            entity.ToTable("OrgRoleTemplate");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Actions).HasMaxLength(1000);
            entity.Property(e => e.Cards).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.ShowSequence).HasDefaultValue(0);
            entity.Property(e => e.Triggers).HasMaxLength(1000);

            entity.HasOne(d => d.Org).WithMany(p => p.OrgRoleTemplates)
                .HasForeignKey(d => d.OrgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgRoleTemplate_Org");
        });

        modelBuilder.Entity<OrgUser>(entity =>
        {
            entity.ToTable("OrgUser");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ActivatedOn).HasColumnType("datetime");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.DeactivatedOn).HasColumnType("datetime");
            entity.Property(e => e.Designation).HasMaxLength(1000);
            entity.Property(e => e.IsAdmin).HasDefaultValue(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.Org).WithMany(p => p.OrgUsers)
                .HasForeignKey(d => d.OrgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_OrgUser_Org");
        });

        modelBuilder.Entity<PhoneConfig>(entity =>
        {
            entity.ToTable("PhoneConfig");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Did)
                .HasMaxLength(50)
                .HasColumnName("DID");
            entity.Property(e => e.Extn).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.Sipaddress)
                .HasMaxLength(50)
                .HasColumnName("SIPAddress");
        });

        modelBuilder.Entity<Provision>(entity =>
        {
            entity.ToTable("Provision");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.Comment).HasMaxLength(500);
            entity.Property(e => e.CountryId).HasMaxLength(2);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DisabledDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DueDate).HasColumnType("datetime");
            entity.Property(e => e.Duration).HasDefaultValue(0);
            entity.Property(e => e.EmailAddress).HasMaxLength(500);
            entity.Property(e => e.EnterpriseUserId).HasMaxLength(500);
            entity.Property(e => e.FirstName).HasMaxLength(500);
            entity.Property(e => e.IdpappId)
                .HasMaxLength(500)
                .HasColumnName("IDPAppId");
            entity.Property(e => e.IdpproviderId).HasColumnName("IDPProviderId");
            entity.Property(e => e.IsAccepted).HasDefaultValue(true);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsClaimed).HasDefaultValue(false);
            entity.Property(e => e.IsPayed).HasDefaultValue(true);
            entity.Property(e => e.IsPaymentStoped).HasDefaultValue(false);
            entity.Property(e => e.IsPurchasedByUser).HasDefaultValue(false);
            entity.Property(e => e.IsPurchasedOnAndroid).HasDefaultValue(false);
            entity.Property(e => e.IsPurchasedOnIos).HasDefaultValue(false);
            entity.Property(e => e.IsTrial).HasDefaultValue(false);
            entity.Property(e => e.IsTrialCompleted).HasDefaultValue(false);
            entity.Property(e => e.LastName).HasMaxLength(500);
            entity.Property(e => e.LastPaymentDate).HasColumnType("datetime");
            entity.Property(e => e.MiddleName).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.OrgCustomerId).HasMaxLength(500);
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.ProductId).HasMaxLength(1500);
            entity.Property(e => e.Ptype)
                .HasDefaultValue(1)
                .HasColumnName("PType");
            entity.Property(e => e.PurchaseId)
                .HasMaxLength(1500)
                .HasColumnName("PurchaseID");
            entity.Property(e => e.PurchasedDate).HasColumnType("datetime");
            entity.Property(e => e.Salutation).HasMaxLength(100);
            entity.Property(e => e.SanitizedNumber).HasMaxLength(50);
            entity.Property(e => e.SubscriptionId).HasMaxLength(1000);
            entity.Property(e => e.UserCustomerId).HasMaxLength(500);

            entity.HasOne(d => d.OrgDirectory).WithMany(p => p.Provisions)
                .HasForeignKey(d => d.OrgDirectoryId)
                .HasConstraintName("FK_Provision_OrgDirectory");

            entity.HasOne(d => d.Org).WithMany(p => p.Provisions)
                .HasForeignKey(d => d.OrgId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Provision_Org");
        });

        modelBuilder.Entity<Sysdiagram>(entity =>
        {
            entity.HasKey(e => e.DiagramId).HasName("PK__sysdiagr__C2B05B61E10B59B5");

            entity.ToTable("sysdiagrams");

            entity.HasIndex(e => new { e.PrincipalId, e.Name }, "UK_principal_name").IsUnique();

            entity.Property(e => e.DiagramId).HasColumnName("diagram_id");
            entity.Property(e => e.Definition).HasColumnName("definition");
            entity.Property(e => e.Name)
                .HasMaxLength(128)
                .HasColumnName("name");
            entity.Property(e => e.PrincipalId).HasColumnName("principal_id");
            entity.Property(e => e.Version).HasColumnName("version");
        });

        modelBuilder.Entity<UserProvider>(entity =>
        {
            entity.ToTable("UserProvider");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.AccountId).HasMaxLength(500);
            entity.Property(e => e.ActiveFrom).HasColumnType("datetime");
            entity.Property(e => e.ActiveTill).HasColumnType("datetime");
            entity.Property(e => e.BulkDataUrl).HasMaxLength(1000);
            entity.Property(e => e.CeinstanceId)
                .HasMaxLength(200)
                .HasColumnName("CEInstanceId");
            entity.Property(e => e.CeinstanceUrl)
                .HasMaxLength(1000)
                .HasColumnName("CEInstanceUrl");
            entity.Property(e => e.CeorgId)
                .HasMaxLength(200)
                .HasColumnName("CEOrgId");
            entity.Property(e => e.CeuserId)
                .HasMaxLength(200)
                .HasColumnName("CEUserId");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.DataUrl).HasColumnName("DataURL");
            entity.Property(e => e.DisplayName).HasMaxLength(500);
            entity.Property(e => e.EmailAddress).HasMaxLength(500);
            entity.Property(e => e.IdpproviderInstance)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderInstance");
            entity.Property(e => e.IdpproviderUrl)
                .HasMaxLength(500)
                .HasColumnName("IDPProviderUrl");
            entity.Property(e => e.Imapserver)
                .HasMaxLength(50)
                .HasColumnName("IMAPServer");
            entity.Property(e => e.InstanceUrl).HasMaxLength(1000);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsClaimed).HasDefaultValue(false);
            entity.Property(e => e.IsPaymentStoped).HasDefaultValue(false);
            entity.Property(e => e.IsProvisioned).HasDefaultValue(true);
            entity.Property(e => e.IsSsl).HasColumnName("IsSSL");
            entity.Property(e => e.IsTrial).HasDefaultValue(false);
            entity.Property(e => e.IsTrialCompleted).HasDefaultValue(false);
            entity.Property(e => e.MailboxGuid).HasMaxLength(500);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Password).HasMaxLength(50);
            entity.Property(e => e.Popserver)
                .HasMaxLength(50)
                .HasColumnName("POPServer");
            entity.Property(e => e.Port).HasMaxLength(200);
            entity.Property(e => e.Priority).HasDefaultValue(100);
            entity.Property(e => e.ProductId).HasMaxLength(1500);
            entity.Property(e => e.ProfileUrl)
                .HasMaxLength(1000)
                .HasDefaultValueSql("(NULL)");
            entity.Property(e => e.PurchaseId)
                .HasMaxLength(1500)
                .HasColumnName("PurchaseID");
            entity.Property(e => e.ReferenceId).HasMaxLength(500);
            entity.Property(e => e.Smtpserver)
                .HasMaxLength(50)
                .HasColumnName("SMTPServer");
            entity.Property(e => e.Tenant).HasMaxLength(50);
            entity.Property(e => e.TrialEnd).HasColumnType("datetime");
            entity.Property(e => e.TrialStart).HasColumnType("datetime");
            entity.Property(e => e.Type).HasMaxLength(500);
            entity.Property(e => e.Url).HasColumnName("URL");
            entity.Property(e => e.WebhookExpiration).HasColumnType("datetime");
        });

        modelBuilder.Entity<UserProviderIdentifier>(entity =>
        {
            entity.ToTable("UserProviderIdentifier");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.UserProvider).WithMany(p => p.UserProviderIdentifiers)
                .HasForeignKey(d => d.UserProviderId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_UserProviderIdentifier_UserProvider");
        });

        modelBuilder.Entity<UserRequest>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("UserRequest");

            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<UserStorage>(entity =>
        {
            entity.ToTable("UserStorage");

            entity.Property(e => e.Id).ValueGeneratedNever();
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(2250);
            entity.Property(e => e.ShowSequence).HasDefaultValue(1000);
            entity.Property(e => e.Type).HasComment("Type bit - 0 for folder, 1 for search");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
