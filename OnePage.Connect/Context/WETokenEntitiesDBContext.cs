﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using OnePage.Connect.Migrations;

namespace OnePage.Connect.Context;

public partial class WETokenEntitiesDBContext : DbContext
{
    public WETokenEntitiesDBContext()
    {
    }

    public WETokenEntitiesDBContext(DbContextOptions<WETokenEntitiesDBContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Abuse> Abuses { get; set; }

    public virtual DbSet<ActivityCall> ActivityCalls { get; set; }

    public virtual DbSet<ActivityLog> ActivityLogs { get; set; }

    public virtual DbSet<CalendarGroup> CalendarGroups { get; set; }

    public virtual DbSet<CalendarModel> CalendarModels { get; set; }

    public virtual DbSet<Contact> Contacts { get; set; }

    public virtual DbSet<ContactEmail> ContactEmails { get; set; }

    public virtual DbSet<ContactHistory> ContactHistories { get; set; }

    public virtual DbSet<ContactPhone> ContactPhones { get; set; }

    public virtual DbSet<ContactSocial> ContactSocials { get; set; }

    public virtual DbSet<EventAttendee> EventAttendees { get; set; }

    public virtual DbSet<EventModel> EventModels { get; set; }

    public virtual DbSet<Note> Notes { get; set; }

    public virtual DbSet<Token> Tokens { get; set; }

    // protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        // => optionsBuilder.UseSqlServer("Server=eu-ne-sd.database.windows.net;Database=eu-fc-ss-tk;User=eu-ne-sd;Password=**************;MultipleActiveResultSets=true;");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Abuse>(entity =>
        {
            entity.ToTable("Abuse");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Ipaddress)
                .HasMaxLength(50)
                .HasColumnName("IPAddress");
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.NewUserAgent).HasMaxLength(2000);
            entity.Property(e => e.Reason).HasMaxLength(100);

            entity.HasOne(d => d.Token).WithMany(p => p.Abuses)
                .HasForeignKey(d => d.TokenId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Abuse_Token");
        });

        modelBuilder.Entity<ActivityCall>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ActivityCall");

            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.CallId).HasMaxLength(50);
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.LastSync).HasColumnType("datetime");
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.Sone)
                .HasMaxLength(50)
                .HasColumnName("SOne");
            entity.Property(e => e.Stwo)
                .HasMaxLength(50)
                .HasColumnName("STwo");
            entity.Property(e => e.Url).HasMaxLength(50);
        });

        modelBuilder.Entity<ActivityLog>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ActivityLog");

            entity.Property(e => e.Bfour).HasColumnName("BFour");
            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Bthree).HasColumnName("BThree");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.CallEndTime).HasColumnType("datetime");
            entity.Property(e => e.CallId).HasMaxLength(50);
            entity.Property(e => e.CallStartTime).HasColumnType("datetime");
            entity.Property(e => e.ContactName).HasMaxLength(50);
            entity.Property(e => e.ConvId).HasMaxLength(50);
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.IsCcall).HasColumnName("IsCCall");
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.Notes).HasMaxLength(50);
            entity.Property(e => e.PhoneNumber).HasMaxLength(50);
            entity.Property(e => e.Reference).HasMaxLength(50);
            entity.Property(e => e.Sfour)
                .HasMaxLength(50)
                .HasColumnName("SFour");
            entity.Property(e => e.Sone)
                .HasMaxLength(50)
                .HasColumnName("SOne");
            entity.Property(e => e.Sthree)
                .HasMaxLength(50)
                .HasColumnName("SThree");
            entity.Property(e => e.Stwo)
                .HasMaxLength(50)
                .HasColumnName("STwo");
            entity.Property(e => e.Transcription).HasMaxLength(50);
            entity.Property(e => e.TranscriptionData).HasMaxLength(50);
            entity.Property(e => e.Url).HasMaxLength(50);
        });

        modelBuilder.Entity<CalendarGroup>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("CalendarGroup");

            entity.Property(e => e.ClassId).HasMaxLength(50);
            entity.Property(e => e.Desc).HasMaxLength(100);
            entity.Property(e => e.Fcolor)
                .HasMaxLength(50)
                .HasColumnName("FColor");
            entity.Property(e => e.Loc).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.RefId).HasMaxLength(50);
            entity.Property(e => e.TimeZone).HasMaxLength(50);
        });

        modelBuilder.Entity<CalendarModel>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("CalendarModel");

            entity.Property(e => e.ClassId).HasMaxLength(50);
            entity.Property(e => e.Dcolor)
                .HasMaxLength(50)
                .HasColumnName("DColor");
            entity.Property(e => e.Desc).HasMaxLength(100);
            entity.Property(e => e.Lcolor)
                .HasMaxLength(50)
                .HasColumnName("LColor");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.RefId).HasMaxLength(50);
            entity.Property(e => e.TimeZone).HasMaxLength(50);
        });

        modelBuilder.Entity<Contact>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("Contact");

            entity.Property(e => e.Alias).HasMaxLength(50);
            entity.Property(e => e.Bfour).HasColumnName("BFour");
            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Bthree).HasColumnName("BThree");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.Company).HasMaxLength(50);
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.Crmid)
                .HasMaxLength(50)
                .HasColumnName("CRMId");
            entity.Property(e => e.Desig).HasMaxLength(50);
            entity.Property(e => e.DirIds).HasMaxLength(50);
            entity.Property(e => e.Display).HasMaxLength(50);
            entity.Property(e => e.EmailDisplay).HasMaxLength(50);
            entity.Property(e => e.FirstName).HasMaxLength(50);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.IsCcontact).HasColumnName("IsCContact");
            entity.Property(e => e.IsClead).HasColumnName("IsCLead");
            entity.Property(e => e.LastName).HasMaxLength(50);
            entity.Property(e => e.LastProcessed).HasColumnType("datetime");
            entity.Property(e => e.Linked).HasMaxLength(50);
            entity.Property(e => e.MiddleName).HasMaxLength(50);
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.Nick).HasMaxLength(50);
            entity.Property(e => e.OtherIds).HasMaxLength(50);
            entity.Property(e => e.PhoneNumbers).HasMaxLength(50);
            entity.Property(e => e.Prefix).HasMaxLength(50);
            entity.Property(e => e.Reference).HasMaxLength(50);
            entity.Property(e => e.Salutation).HasMaxLength(50);
            entity.Property(e => e.Sfour)
                .HasMaxLength(50)
                .HasColumnName("SFour");
            entity.Property(e => e.Sone)
                .HasMaxLength(50)
                .HasColumnName("SOne");
            entity.Property(e => e.Sthree)
                .HasMaxLength(50)
                .HasColumnName("SThree");
            entity.Property(e => e.Stwo)
                .HasMaxLength(50)
                .HasColumnName("STwo");
            entity.Property(e => e.Suffix).HasMaxLength(50);
            entity.Property(e => e.Websites).HasMaxLength(50);
        });

        modelBuilder.Entity<ContactEmail>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ContactEmail");

            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.DisplayName).HasMaxLength(50);
            entity.Property(e => e.Email).HasMaxLength(50);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.Sone)
                .HasMaxLength(50)
                .HasColumnName("SOne");
            entity.Property(e => e.Stwo)
                .HasMaxLength(50)
                .HasColumnName("STwo");
        });

        modelBuilder.Entity<ContactHistory>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ContactHistory");

            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.DeepLink).HasMaxLength(50);
            entity.Property(e => e.EndTime).HasColumnType("datetime");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.ReferenceId).HasMaxLength(100);
            entity.Property(e => e.Sone)
                .HasMaxLength(50)
                .HasColumnName("SOne");
            entity.Property(e => e.StartTime).HasColumnType("datetime");
            entity.Property(e => e.Stwo)
                .HasMaxLength(50)
                .HasColumnName("STwo");
            entity.Property(e => e.SubTitle).HasMaxLength(100);
            entity.Property(e => e.Title).HasMaxLength(50);
            entity.Property(e => e.UserId).HasColumnType("datetime");
        });

        modelBuilder.Entity<ContactPhone>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ContactPhone");

            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.CountryId)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.Extn).HasMaxLength(50);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.IsCowned).HasColumnName("IsCOwned");
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.Pnumber)
                .HasMaxLength(50)
                .HasColumnName("PNumber");
            entity.Property(e => e.Ptype).HasColumnName("PType");
            entity.Property(e => e.Snumber)
                .HasMaxLength(50)
                .HasColumnName("SNumber");
            entity.Property(e => e.Sone)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("SOne");
            entity.Property(e => e.Stwo)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("STwo");
            entity.Property(e => e.Token).HasMaxLength(50);
        });

        modelBuilder.Entity<ContactSocial>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ContactSocial");

            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.Sone)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("SOne");
            entity.Property(e => e.Stwo)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("STwo");
            entity.Property(e => e.Url).HasMaxLength(50);
        });

        modelBuilder.Entity<EventAttendee>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("EventAttendee");

            entity.Property(e => e.Address).HasMaxLength(50);
            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.Comment).HasMaxLength(50);
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.EventId).HasMaxLength(100);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.Name).HasMaxLength(100);
            entity.Property(e => e.Sone)
                .HasMaxLength(50)
                .HasColumnName("SOne");
            entity.Property(e => e.Status).HasMaxLength(50);
            entity.Property(e => e.Stwo)
                .HasMaxLength(50)
                .HasColumnName("STwo");
        });

        modelBuilder.Entity<EventModel>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("EventModel");

            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.CalId).HasMaxLength(50);
            entity.Property(e => e.ConvUrl)
                .HasMaxLength(50)
                .HasColumnName("ConvURL");
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.Dcolor)
                .HasMaxLength(50)
                .HasColumnName("DColor");
            entity.Property(e => e.Desc).HasMaxLength(500);
            entity.Property(e => e.DialIn).HasMaxLength(50);
            entity.Property(e => e.Dinumbers)
                .HasMaxLength(50)
                .HasColumnName("DINumbers");
            entity.Property(e => e.End).HasColumnType("datetime");
            entity.Property(e => e.EventId).HasMaxLength(100);
            entity.Property(e => e.H323numbers)
                .HasMaxLength(50)
                .HasColumnName("H323Numbers");
            entity.Property(e => e.HostPin)
                .HasMaxLength(50)
                .HasColumnName("HostPIN");
            entity.Property(e => e.ICallUid)
                .HasMaxLength(100)
                .HasColumnName("iCallUId");
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Importance).HasMaxLength(50);
            entity.Property(e => e.IsMrecorded).HasColumnName("IsMRecorded");
            entity.Property(e => e.Lcolor)
                .HasMaxLength(50)
                .HasColumnName("LColor");
            entity.Property(e => e.Lend)
                .HasMaxLength(50)
                .HasColumnName("LEnd");
            entity.Property(e => e.Link).HasMaxLength(100);
            entity.Property(e => e.Location).HasMaxLength(50);
            entity.Property(e => e.Lstart)
                .HasMaxLength(50)
                .HasColumnName("LStart");
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.Notes).HasMaxLength(50);
            entity.Property(e => e.Otmobile)
                .HasMaxLength(50)
                .HasColumnName("OTMobile");
            entity.Property(e => e.RecUrl)
                .HasMaxLength(50)
                .HasColumnName("RecURL");
            entity.Property(e => e.RepeatId).HasMaxLength(50);
            entity.Property(e => e.Sipnumbers)
                .HasMaxLength(50)
                .HasColumnName("SIPNumbers");
            entity.Property(e => e.Sone)
                .HasMaxLength(50)
                .HasColumnName("SOne");
            entity.Property(e => e.Start).HasColumnType("datetime");
            entity.Property(e => e.Status).HasMaxLength(50);
            entity.Property(e => e.Stwo)
                .HasMaxLength(50)
                .HasColumnName("STwo");
            entity.Property(e => e.Subject).HasMaxLength(100);
            entity.Property(e => e.Url).HasMaxLength(50);
        });

        modelBuilder.Entity<Note>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("Note");

            entity.Property(e => e.Bone).HasColumnName("BOne");
            entity.Property(e => e.Btwo).HasColumnName("BTwo");
            entity.Property(e => e.Created).HasColumnType("datetime");
            entity.Property(e => e.Details).HasMaxLength(50);
            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Modified).HasColumnType("datetime");
            entity.Property(e => e.NoteDate).HasColumnType("datetime");
            entity.Property(e => e.Sone)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("SOne");
            entity.Property(e => e.Stwo)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("STwo");
        });

        modelBuilder.Entity<Token>(entity =>
        {
            entity.ToTable("Token");

            entity.Property(e => e.Id).HasDefaultValueSql("(newid())");
            entity.Property(e => e.BrowserData).HasMaxLength(1000);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Ip)
                .HasMaxLength(50)
                .HasColumnName("IP");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate)
                .HasDefaultValueSql("(getutcdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Ttl)
                .HasColumnType("datetime")
                .HasColumnName("TTL");
            entity.Property(e => e.UserAgent).HasMaxLength(2000);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
