<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
      <Content Include="web.config">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
      <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
      <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.4" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.4">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.4" />
      <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.4">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Serilog.AspNetCore" Version="8.0.2" />
      <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
      <PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
      <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
      <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
      <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.2" />
      <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="7.0.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\OnePage.Models\OnePage.Models.csproj" />
      <ProjectReference Include="..\OnePage.Services\OnePage.Services.csproj" />
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\03300ff747684bbc88163577dc7b9f7a.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\034208d2fe6c49e68d7270e38598dc2e.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\10468ad55fd54fc4bed0e8f48a114df9.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\10A5E65C09674CF7B959CE4CB06C4DD7.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\194cc115f7f844bda6d9d81ec6345682.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\21900222fe90435fac939624ee38605b.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\2209bb994dab43f48c8d44c7d83fb8fa.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\232a6c90a9f44b8c98b9fc050e84e184.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\2876460546334649a65a2616db545cd8.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\327915f0677a444c99a81b4998a4623c.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\32ac5cd570244ab5af8146f03a3a7147.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\35b45aa510f5428ca2649dd5854bac26.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\37f357b836244dcb8c35d4d47a0cd5a7.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\394ce02ee7c647a6ad22e6d1226a8274.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\61d0445438a041deaed81bb044e98443.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\62c5dfa67199418387561714d2d9fa49.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\6f7398b7a5524768ad95f47b9e1e6faf.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\925c0c0caaaf4785bc20902317b6efcb.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\a45b6b989d244c33b464ac585f768752.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\b75ce275868040779455f6476cfbc67a.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\b91cbaeed9d0490e84581b5a8e4e2aac.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\bb779183e9034f55a0993ef4373a6c25.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\c893b1ccf74f4afdbba356532cb4c46b.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\cd459cdc417241fd9dd98881067b70b2.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\cef7b63032314a0b9587e7f7b56d6a1f.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\cf2f8bccf9a74aef900c32a615de4af1.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\d3bfe6cca8744253a05989734ce01d75.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\e89be003cea44bc697f0bee0c48a5fea.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\ebb3714b3f7c4022b304936607a82437.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\ecd27441f9f94eda9432cd4e86c9d027.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\ErrorMessage.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\f9695da4ae0e41a3a0e4fbec795d2de5.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\fe85816ef8514d0a962b861695ff6d76.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\Index.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\InvalidEmailId.cshtml" />
      <_ContentIncludedByDefault Remove="Views\Authenticated\Controllers\OktaLoginSuccessful.cshtml" />
    </ItemGroup>

    <ItemGroup>
      <Content Update="Views\Authenticated\a2dbf10e08c54aa5b1988bbd2a860a66.cshtml">
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </Content>
    </ItemGroup>

</Project>
