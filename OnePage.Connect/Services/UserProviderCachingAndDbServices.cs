using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;
using Newtonsoft.Json;
using OnePage.Connect.Migrations;


namespace OnePage.Connect.Services
{
    public class UserProviderCachingAndDbServices
    {
        private readonly HttpClient _httpClient;
        private readonly HttpClient _webProxyClient;
        private readonly string _baseUrl;
        private readonly string _webProxyUrl;
        // private const string BaseUrl = "http://localhost:5000"; // TODO: update this base URL ..

        public UserProviderCachingAndDbServices(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _baseUrl = configuration.GetValue<string>("BaseUrl");
            _httpClient.BaseAddress = new Uri(_baseUrl);
            _webProxyClient = new HttpClient();
            _webProxyUrl = configuration.GetValue<string>("WebproxyUrl");
            _webProxyClient.BaseAddress = new Uri(_webProxyUrl);


        }

        public async Task<UserProvider?> GetUserProvidersFromRedisOrDbAsync(Guid userId, Guid providerId)
        {
            try
            {

                var response = await _httpClient.GetAsync($"A842C2E2?userId={userId}&providerId={providerId}");
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadFromJsonAsync<UserProvider>();
            }
            catch (HttpRequestException ex)
            {
                TelegramService.SendMessageToTestBot2($"{_httpClient.BaseAddress.ToString()}");
                // TelegramService.SendMessageToTestBot2($"Request URL - A842C2E2?userId={userId}&providerId={providerId}");
                TelegramService.SendMessageToTestBot2($"Error fetching user providers: {ex.Message}");
                return null;
            }
        }

        public async Task<UserProvider?> GetUserProvidersFromRedisOrDbAsyncWithoutUserId(Guid providerId)
        {
            try
            {
                var response = await _httpClient.GetAsync($"8D55C274?userProviderId={providerId}");
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadFromJsonAsync<UserProvider>();
            }
            catch (HttpRequestException ex)
            {

                TelegramService.SendMessageToTestBot2($"{_httpClient.BaseAddress.ToString()}");
                TelegramService.SendMessageToTestBot2($"Request URL 8D55C274?userProviderId={providerId}");
                TelegramService.SendMessageToTestBot2($"Error fetching user providers: {ex.Message}");
                return null;
            }
        }

        public async Task<List<UserProviderIdentifier>?> GetUserProviderIdentifiersFromRedisOrDb(Guid userProviderId, Guid? identifierId = null)
        {
            try
            {
                // 6DC589FE?userProviderId=8c11b9e1-aa58-47a3-9fed-bd2c9a90eef5&identifierId=38fcfc81-c779-4b79-978a-1f9aa97e8ed7
                // Construct the API URL based on the parameters
                string host = _baseUrl;
                string apiUrl = $"{host}/6DC589FE?userProviderId={userProviderId}";


                // Include identifierId in the API URL if provided
                if (identifierId.HasValue)
                {
                    apiUrl += $"&identifierId={identifierId}";
                }
                TelegramService.SendMessageToTestBot2($"API URL - {apiUrl}");


                var response = await _httpClient.GetAsync(apiUrl);
                response.EnsureSuccessStatusCode();

                return await response.Content.ReadFromJsonAsync<List<UserProviderIdentifier>>();
            }
            catch (HttpRequestException ex)
            {
                TelegramService.SendMessageToTestBot2($"{_httpClient.BaseAddress.ToString()}");
                // TelegramService.SendMessageToTestBot2($"Request URL - /8D55C274?userProviderId={providerId}");
                TelegramService.SendMessageToTestBot2($"Error fetching user provider identifiers: {ex.Message}");
                return null;
            }
        }


        public async Task AddUserProviderInRedisAndDb(UserProvider userProvider)
        {
            try
            {
                var Json = JsonConvert.SerializeObject(userProvider);
                // TelegramService.SendMessageToTestBot2($"Request URL - /8D55C274?userProviderId={providerId}");
                var response = await _httpClient.PostAsJsonAsync("646FCC72", userProvider);
                response.EnsureSuccessStatusCode();
            }
            catch (HttpRequestException ex)
            {
                TelegramService.SendMessageToTestBot2($"{_httpClient.BaseAddress.ToString()}");
                TelegramService.SendMessageToTestBot2($"Error adding user provider: {ex.Message}");
            }
        }

        public async Task AddUserProviderIdentifierInRedisAndDb(List<UserProviderIdentifier> userProviderIdentifiers)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync("0988D36F", userProviderIdentifiers);
                response.EnsureSuccessStatusCode();
                TelegramService.SendMessageToTestBot2($"Rsponse code - {response.StatusCode}");
                TelegramService.SendMessageToTestBot2($"Up Count - {userProviderIdentifiers.Count}");
                //TelegramService.SendMessageToTestBot2($"UP Identifiers- {Newtonsoft.Json.JsonSerializer.Serialize(userProviderIdentifiers)}");


            }
            catch (HttpRequestException ex)
            {
                TelegramService.SendMessageToTestBot2($"{_httpClient.BaseAddress.ToString()}");
                TelegramService.SendMessageToTestBot2($"Error adding user provider identifiers: {ex.Message}");
            }
        }


        public void HitProviderDataRedisCaching(Guid userId, Guid providerId, Guid userProviderId)
        {
            try
            {
                ProviderRedisUpdateModel model = new ProviderRedisUpdateModel(userId, providerId, userProviderId);
                _ = _webProxyClient.PostAsJsonAsync("0C169104", model);

            }
            catch (HttpRequestException ex)
            {
                TelegramService.SendMessageToTestBot2($"{_webProxyClient.BaseAddress.ToString()}");
                TelegramService.SendMessageToTestBot2($"Error adding : {ex.Message}");
            }
        }

        public class ProviderRedisUpdateModel
        {
            public ProviderRedisUpdateModel(Guid UserId, Guid ProviderId, Guid UserProviderId)
            {
                userId = UserId;
                providerId = ProviderId;
                userProviderId = UserProviderId;
            }
            public Guid userId { get; set; }
            public Guid providerId { get; set; }
            public Guid userProviderId { get; set; }
        }

        public async Task PostEmailToQueue(string email)
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                {
                    throw new ArgumentException("Email cannot be empty", nameof(email));
                }

                var response = await _httpClient.PostAsync($"4883E473?email={email}", null);
                response.EnsureSuccessStatusCode();
                
            }
            catch (HttpRequestException ex)
            {
                TelegramService.SendMessageToTestBot2($"{_httpClient.BaseAddress}");
                TelegramService.SendMessageToTestBot2($"Request URL 4883E473?email={email}");
                TelegramService.SendMessageToTestBot2($"Error posting email to provider queue: {ex.Message}");
               
            }
        }




    }
}
