﻿using OnePage.Connect.Models;
using System.Text.Json;

namespace OnePage.Connect.Services
{
    public class TranslationService
    {
        private readonly string _resourcePath;
        public TranslationService(IConfiguration configuration)
        {
            _resourcePath = configuration["ResourcesPath"] ?? "Resources";
        }
        public Dictionary<string, string> GetTranslations(string langCode)
        {
            var fileName = $"{langCode}.json";
            var filePath = Path.Combine(_resourcePath, fileName);

            if (!File.Exists(filePath))
            {
                filePath = Path.Combine(_resourcePath, "en-US.json");
            }
            var json = File.ReadAllText(filePath);
            var translations = JsonSerializer.Deserialize<Dictionary<string, string>>(json);

            return translations ?? new Dictionary<string, string>();
        }
        public string GetTranslationByKey(string langCode, string key)
        {
            var translations = GetTranslations(langCode);

            if (translations.TryGetValue(key, out var translationEntry))
            {
                return translationEntry;
            }

            throw new KeyNotFoundException($"Key not found");
        }
    }
}
