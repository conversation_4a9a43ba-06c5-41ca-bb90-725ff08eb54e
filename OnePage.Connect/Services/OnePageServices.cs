// using System.Net;
// using System.Net.Http.Headers;
// using System.Text;
// using Azure.Core;
// using Azure.Storage.Blobs;
// using Newtonsoft.Json;
// using Newtonsoft.Json.Linq;
// using OnePage.Common;
// using OnePage.Connect.Context;
// using OnePage.Connect.Controllers;
// using OnePage.Connect.Migrations;
// using OnePage.Models;
// using OnePage.Services;
// using RestSharp;

// namespace OnePage.Connect.Services;

// public class OnePageServices
// {
//     private readonly IHttpContextAccessor _httpContextAccessor;
//     public OnePageServices(IHttpContextAccessor httpContextAccessor)
//     {
//         _httpContextAccessor = httpContextAccessor;

//     }

//     private WETokenEntitiesDBContext wetdb = new WETokenEntitiesDBContext();
//     private WEOrgEntitiesDBContext weodb = new WEOrgEntitiesDBContext();

//     private WEPeopleEntitiesDBContext wepdb = new WEPeopleEntitiesDBContext();

//     //private WESyncEntities wesdb = new WESyncEntities();
//     private WEDataEntitiesDBContext weddb = new WEDataEntitiesDBContext();
//     private WEAdminEntitiesDBContext weadb = new WEAdminEntitiesDBContext();
//     private List<Identifier> identifierList = new List<Identifier>();
//     private List<OrgIdentifier> orgProviderIdentifierList = new List<OrgIdentifier>();
//     private List<OrgIdentifier> orgProviderIdentifierList2 = new List<OrgIdentifier>();
//     private List<Url> providerUrlList = new List<Url>();
//     private string appToken = "";
//     private string appUserId = "";
//     private string appProvision = "";
//     public string eId = "";

//     private static List<AuthenticatedController.TempList> nameValueList = new List<AuthenticatedController.TempList>();
//     private static List<AuthenticatedController.TempList> headersList = new List<AuthenticatedController.TempList>();
//     private static List<AuthenticatedController.TempList> urlsCompleted = new List<AuthenticatedController.TempList>();

//     #region GetClient

//     public async Task<bool> GetClient(string finalURL, Url providerUrl, string dataToReplace)
//     {
//         try
//         {
//             var urlPart1 = finalURL;
//             HttpClient client = new HttpClient();
//             HttpContent content = new StringContent(String.Empty);
//             content.Headers.ContentType =
//                 new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
//             foreach (var item in headersList)
//             {
//                 if (item.Value.Contains('{'))
//                 {
//                     item.Value = FindAndReplaceIndentifiers(item.Value);
//                 }

//                 if (item.Name == "Content-Type")
//                     content.Headers.ContentType =
//                         new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
//                 else
//                     client.DefaultRequestHeaders.Add(item.Name, item.Value);
//             }

//             TelegramService.SendMessageToTestBot("Get Method calll!");
//             TelegramService.SendMessageToTestBot("GET url: " + urlPart1);
//             using (HttpResponseMessage response1 = await client.GetAsync(urlPart1))
//             {
//                 TelegramService.SendMessageToTestBot(response1.StatusCode.ToString());
//                 TelegramService.SendMessageToTestBot(response1.RequestMessage.ToString());
//                 if (response1.IsSuccessStatusCode)
//                 {
//                     try
//                     {
//                         var json = await response1.Content.ReadAsStringAsync();
//                         CommonData.sbnew.AppendLine("me api response: " + json);
//                         TelegramService.SendMessageToTestBot(json.ToString());
//                         JToken jtoken = JToken.Parse(json);
//                         var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
//                         foreach (var outputIdentifier in outputIndentifiers)
//                         {
//                             JToken selectedToken;
//                             List<JToken> tokenList = new List<JToken>();
//                             var keyData = outputIdentifier.Split('|');
//                             if (keyData[1].Contains(".."))
//                             {
//                                 var list = jtoken.SelectTokens(keyData[1]).Values<string>().ToList();
//                                 string tokenValues = "";
//                                 foreach (var item in list.ToList())
//                                 {
//                                     string tokenValue = item;
//                                     tokenValues += tokenValue + ",";
//                                 }

//                                 if (tokenValues.Length > 0)
//                                     tokenValues = tokenValues.Substring(0, tokenValues.Length - 1);
//                                 foreach (var identifier in identifierList)
//                                 {
//                                     if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                                     {
//                                         identifier.Value = tokenValues;
//                                     }
//                                 }
//                             }
//                             else
//                             {
//                                 selectedToken = jtoken.SelectToken(keyData[1]);
//                                 if (selectedToken != null)
//                                 {
//                                     if (selectedToken.Type == JTokenType.Array)
//                                     {
//                                         var tokens = selectedToken.Values();
//                                         string tokenValues = "";
//                                         foreach (var item in tokens)
//                                         {
//                                             string tokenValue = item.Value<string>();
//                                             tokenValues += tokenValue + ",";
//                                         }

//                                         if (tokenValues.Length > 0)
//                                             tokenValues = tokenValues.Substring(0, tokenValues.Length - 1);
//                                         foreach (var identifier in identifierList)
//                                         {
//                                             if (keyData[0].ToLower() == identifier.Name.ToLower() &&
//                                                 identifier.Value == null)
//                                             {
//                                                 identifier.Value = tokenValues;
//                                             }
//                                         }
//                                     }
//                                     else
//                                     {
//                                         string tokenValue = selectedToken.Value<string>();
//                                         foreach (var identifier in identifierList)
//                                         {
//                                             if (keyData[0].ToLower() == identifier.Name.ToLower() &&
//                                                 identifier.Value == null)
//                                             {
//                                                 identifier.Value = tokenValue;
//                                             }
//                                         }
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                     catch (Exception ex)
//                     {
//                     }
//                 }
//                 else
//                 {
//                     PropertyModels.DataModels.ErrorMailModel errorMailModel =
//                         new PropertyModels.DataModels.ErrorMailModel();
//                     errorMailModel.Url = "server" + finalURL;
//                     errorMailModel.Count = 0;
//                     errorMailModel.CreatedDate = DateTime.UtcNow;
//                     errorMailModel.ErrorCode = response1.StatusCode.ToString();
//                     if (response1.RequestMessage.Content == null)
//                     {
//                         errorMailModel.ErrorMessage = errorMailModel.ErrorCode;
//                     }
//                     else
//                     {
//                         errorMailModel.ErrorMessage = response1.RequestMessage.Content.ToString();
//                     }

//                     errorMailModel.UserProviderId = Guid.Empty;
//                     errorMailModel.ProvisionId = Guid.Parse(appProvision);
//                     errorMailModel.Payload = dataToReplace ?? "";
//                     errorMailModel.Email = "Didn't find emailid";
//                     var mail = EmailServices.SendGridErrorMail(errorMailModel);
//                 }
//             }
//         }
//         catch (Exception ex)
//         {
//             TelegramService.SendMessageToTestBot(ex.ToString());
//             TelegramService.SendMessageToTestBot(ex.StackTrace.ToString());
//             return false;
//         }

//         return true;
//     }

//     #endregion

//     public async Task<bool> PostClient(string finalURL, Url providerUrl, string dataToReplace)
//     {
//         var urlPart1 = "";
//         var urlPart2 = "";
//         if (finalURL.Contains("?") == true)
//         {
//             urlPart1 = finalURL.Split('?')[0];
//             urlPart2 = finalURL.Split('?')[1];
//         }
//         else if (finalURL.Contains("|") == true)
//         {
//             urlPart1 = finalURL.Split('|')[0];
//             ;
//             urlPart2 = finalURL.Split('|')[1];
//         }
//         else
//         {
//             urlPart1 = finalURL;
//         }

//         HttpClient client = new HttpClient();
//         HttpContent content = new StringContent(String.Format(urlPart2), Encoding.UTF8);

//         TelegramService.SendMessageToTestBot("Header list: " + headersList.Count);
//         content.Headers.ContentType =
//             new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");

//         foreach (var item in headersList)
//         {
//             if (item.Value.Contains('{'))
//             {
//                 item.Value = FindAndReplaceIndentifiers(item.Value);
//                 TelegramService.SendMessageToTestBot("item value: " + item.Value);
//             }

//             if (item.Name == "Content-Type")
//             {
//                 content.Headers.ContentType =
//                     new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
//             }
//             else if (item.Name == "Authorization")
//             {
//                 client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", item.Value);
//                 TelegramService.SendMessageToTestBot("item value: " + item.Value);
//             }
//             else
//             {
//                 content.Headers.Add(item.Name, item.Value);
//                 TelegramService.SendMessageToTestBot("item value: " + item.Value);
//             }
//         }

//         content.Headers.Add("grant_type", "authorization_code");
//         TelegramService.SendMessageToTestBot("url value: " + urlPart1);

//         using (HttpResponseMessage response1 = await client.PostAsync(urlPart1, content))
//         {
//             if (response1.IsSuccessStatusCode)
//             {
//                 TelegramService.SendMessageToTestBot("POST response: " + response1.StatusCode.ToString());
//                 try
//                 {
//                     var json = await response1.Content.ReadAsStringAsync();
//                     // TelegramService.SendMessageToTestBot(json.ToString());
//                     JToken jtoken = JToken.Parse(json);
//                     var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
//                     foreach (var outputIdentifier in outputIndentifiers)
//                     {
//                         var keyData = outputIdentifier.Split('|');
//                         var selectedToken = jtoken.SelectToken(keyData[1]);
//                         if (selectedToken != null)
//                         {
//                             string tokenValue = selectedToken.Value<string>();
//                             foreach (var identifier in identifierList)
//                             {
//                                 if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                                 {
//                                     identifier.Value = tokenValue;
//                                     TelegramService.SendMessageToTestBot("for " + identifier.Name + " : " +
//                                                                          identifier.Value);
//                                     //TelegramService.SendMessageToTestBot(tokenValue.ToString());
//                                 }
//                             }
//                         }
//                     }
//                 }
//                 catch (Exception ex)
//                 {
//                     TelegramService.SendMessageToTestBot(ex.StackTrace.ToString());
//                     TelegramService.SendMessageToTestBot(ex.ToString());
//                     return false;
//                 }
//             }
//             else
//             {
//                 TelegramService.SendMessageToTestBot("POST response: " + response1.StatusCode.ToString());
//             }
//         }

//         return true;
//     }


//     public async Task<bool> IntercomPostClient(string finalURL, Url providerUrl, string dataToReplace)
//     {
//         var urlPart1 = "";
//         var urlPart2 = "";
//         if (finalURL.Contains("|") == true)
//         {
//             urlPart1 = finalURL.Split('|')[0];
//             ;
//             urlPart2 = finalURL.Split('|')[1];
//         }
//         else
//         {
//             urlPart1 = finalURL;
//         }

//         HttpClient client = new HttpClient();
//         HttpContent content = new StringContent(String.Format(urlPart2), Encoding.UTF8);

//         foreach (var item in headersList)
//         {
//             if (item.Value.Contains('{'))
//             {
//                 item.Value = FindAndReplaceIndentifiers(item.Value);
//             }

//             if (item.Name == "Content-Type")
//             {
//                 content.Headers.ContentType =
//                     new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
//             }
//             else if (item.Name == "Authorization")
//             {
//                 client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", item.Value);
//             }
//             else if (item.Name == "Accept")
//             {
//                 client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
//             }
//             else
//             {
//                 content.Headers.Add(item.Name, item.Value);
//             }
//         }

//         //content.Headers.Add("grant_type", "authorization_code");

//         using (HttpResponseMessage response1 = await client.PostAsync(urlPart1, content))
//         {
//             if (response1.IsSuccessStatusCode)
//             {
//                 try
//                 {
//                     var json = await response1.Content.ReadAsStringAsync();
//                     JToken jtoken = JToken.Parse(json);
//                     var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
//                     foreach (var outputIdentifier in outputIndentifiers)
//                     {
//                         var keyData = outputIdentifier.Split('|');
//                         var selectedToken = jtoken.SelectToken(keyData[1]);
//                         if (selectedToken != null)
//                         {
//                             string tokenValue = selectedToken.Value<string>();
//                             foreach (var identifier in identifierList)
//                             {
//                                 if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                                 {
//                                     identifier.Value = tokenValue;
//                                 }
//                             }
//                         }
//                     }
//                 }
//                 catch (Exception ex)
//                 {
//                     return false;
//                 }
//             }
//             else
//             {
//                 PropertyModels.DataModels.ErrorMailModel
//                     errorMailModel = new PropertyModels.DataModels.ErrorMailModel();
//                 errorMailModel.Url = "server" + finalURL;
//                 errorMailModel.Count = 0;
//                 errorMailModel.CreatedDate = DateTime.UtcNow;
//                 errorMailModel.ErrorCode = response1.StatusCode.ToString();
//                 errorMailModel.ErrorMessage = response1.RequestMessage.Content.ToString();
//                 errorMailModel.UserProviderId = Guid.Empty;
//                 errorMailModel.ProvisionId = Guid.Parse(appProvision);
//                 errorMailModel.Payload = dataToReplace ?? "";
//                 errorMailModel.Email = "Didn't found emailid";
//                 var mail = EmailServices.SendGridErrorMail(errorMailModel);
//             }
//         }

//         return true;
//     }

//     public async Task<bool> PostClientDynamicCRM(string finalURL, Url providerUrl, string dataToReplace)
//     {
//         var urlPart1 = "";
//         var urlPart2 = "";
//         if (finalURL.Contains("?") == true)
//         {
//             urlPart1 = finalURL.Split('?')[0];
//             urlPart2 = finalURL.Split('?')[1];
//         }
//         else if (finalURL.Contains("|") == true)
//         {
//             urlPart1 = finalURL.Split('|')[0];
//             ;
//             urlPart2 = finalURL.Split('|')[1];
//         }
//         else
//         {
//             urlPart1 = finalURL;
//         }

//         HttpClient client = new HttpClient();
//         var dict = new Dictionary<string, string>();
//         var keyValues = urlPart2.Split('&');
//         foreach (var keyValue in keyValues)
//         {
//             var split = keyValue.Split('=');
//             dict.Add(split[0], split[1]);
//         }

//         //content.Headers.Add("grant_type", "authorization_code");

//         var req = new HttpRequestMessage(HttpMethod.Post, urlPart1) { Content = new FormUrlEncodedContent(dict) };
//         var res = await client.SendAsync(req);
//         TelegramService.SendMessageToTestBot(res.StatusCode.ToString());
//         if (res.StatusCode == HttpStatusCode.OK)
//         {
//             var response1 = res.Content.ReadAsStringAsync().Result;
//             try
//             {
//                 var json = response1;
//                 JToken jtoken = JToken.Parse(json);
//                 var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
//                 foreach (var outputIdentifier in outputIndentifiers)
//                 {
//                     var keyData = outputIdentifier.Split('|');
//                     var selectedToken = jtoken.SelectToken(keyData[1]);
//                     if (selectedToken != null)
//                     {
//                         string tokenValue = selectedToken.Value<string>();
//                         foreach (var identifier in identifierList)
//                         {
//                             if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                             {
//                                 identifier.Value = tokenValue;
//                             }
//                         }
//                     }
//                 }
//             }
//             catch (Exception ex)
//             {
//                 return false;
//             }
//         }
//         else
//         {
//             PropertyModels.DataModels.ErrorMailModel errorMailModel = new PropertyModels.DataModels.ErrorMailModel();
//             errorMailModel.Url = "server" + finalURL;
//             errorMailModel.Count = 0;
//             errorMailModel.CreatedDate = DateTime.UtcNow;
//             errorMailModel.ErrorCode = res.StatusCode.ToString();
//             errorMailModel.ErrorMessage = res.RequestMessage.Content.ToString();
//             errorMailModel.UserProviderId = Guid.Empty;
//             errorMailModel.ProvisionId = Guid.Parse(appProvision);
//             errorMailModel.Payload = dataToReplace ?? "";
//             errorMailModel.Email = "Didn't found emailid";
//             var mail = EmailServices.SendGridErrorMail(errorMailModel);
//         }

//         return true;
//     }

//     public async Task<bool> DropBoxGetClient(string finalURL, Url providerUrl, string dataToReplace)
//     {
//         try
//         {
//             var url5 = finalURL;
//             HttpClient client5 = new HttpClient();
//             var values5 = new JObject();
//             foreach (var item in headersList)
//             {
//                 if (item.Value.Contains('{'))
//                 {
//                     item.Value = FindAndReplaceIndentifiers(item.Value);
//                 }

//                 if (item.Name == "Authorization")
//                 {
//                     //client5.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(item.Value);
//                     client5.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", item.Value);
//                 }
//                 //else
//                 //    values5.Add(item.Name, item.Value);
//             }

//             //var post = new JObject();
//             //dataToReplace = dataToReplace.Replace('}', ' ');
//             //dataToReplace = dataToReplace.Replace('{', ' ');
//             //dataToReplace = dataToReplace.Replace('\r', ' ').Replace('\n', ' ');
//             //var postData = dataToReplace.Split(',');
//             //foreach (var data in postData)
//             //{
//             //    var item = data.Split('|');
//             //    post.Add(item[0].Trim(), item[1].Trim());
//             //}
//             HttpContent content5 = new StringContent(dataToReplace, Encoding.UTF8, "application/json");

//             using (HttpResponseMessage response1 = await client5.PostAsync(url5, content5))
//             {
//                 if (response1.IsSuccessStatusCode)
//                 {
//                     try
//                     {
//                         var json = await response1.Content.ReadAsStringAsync();
//                         JToken jtoken = JToken.Parse(json);
//                         var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
//                         foreach (var outputIdentifier in outputIndentifiers)
//                         {
//                             JToken selectedToken;
//                             List<JToken> tokenList = new List<JToken>();
//                             var keyData = outputIdentifier.Split('|');
//                             if (keyData[1].Contains(".."))
//                             {
//                                 var list = jtoken.SelectTokens(keyData[1]).Values<string>().ToList();
//                                 string tokenValues = "";
//                                 foreach (var item in list.ToList())
//                                 {
//                                     string tokenValue = item;
//                                     tokenValues += tokenValue + ",";
//                                 }

//                                 if (tokenValues.Length > 0)
//                                     tokenValues = tokenValues.Substring(0, tokenValues.Length - 1);
//                                 foreach (var identifier in identifierList)
//                                 {
//                                     if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                                     {
//                                         identifier.Value = tokenValues;
//                                     }
//                                 }
//                             }
//                             else
//                             {
//                                 selectedToken = jtoken.SelectToken(keyData[1]);
//                                 if (selectedToken != null)
//                                 {
//                                     if (selectedToken.Type == JTokenType.Array)
//                                     {
//                                         var tokens = selectedToken.Values();
//                                         string tokenValues = "";
//                                         foreach (var item in tokens)
//                                         {
//                                             string tokenValue = item.Value<string>();
//                                             tokenValues += tokenValue + ",";
//                                         }

//                                         if (tokenValues.Length > 0)
//                                             tokenValues = tokenValues.Substring(0, tokenValues.Length - 1);
//                                         foreach (var identifier in identifierList)
//                                         {
//                                             if (keyData[0].ToLower() == identifier.Name.ToLower() &&
//                                                 identifier.Value == null)
//                                             {
//                                                 identifier.Value = tokenValues;
//                                             }
//                                         }
//                                     }
//                                     else
//                                     {
//                                         string tokenValue = selectedToken.Value<string>();
//                                         foreach (var identifier in identifierList)
//                                         {
//                                             if (keyData[0].ToLower() == identifier.Name.ToLower() &&
//                                                 identifier.Value == null)
//                                             {
//                                                 identifier.Value = tokenValue;
//                                             }
//                                         }
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                     catch (Exception ex)
//                     {
//                     }
//                 }
//                 else
//                 {
//                     PropertyModels.DataModels.ErrorMailModel errorMailModel =
//                         new PropertyModels.DataModels.ErrorMailModel();
//                     errorMailModel.Url = "server" + finalURL;
//                     errorMailModel.Count = 0;
//                     errorMailModel.CreatedDate = DateTime.UtcNow;
//                     errorMailModel.ErrorCode = response1.StatusCode.ToString();
//                     errorMailModel.ErrorMessage = response1.RequestMessage.Content.ToString();
//                     errorMailModel.UserProviderId = Guid.Empty;
//                     errorMailModel.ProvisionId = Guid.Parse(appProvision);
//                     errorMailModel.Payload = dataToReplace ?? "";
//                     errorMailModel.Email = "Didn't found emailid";
//                     var mail = EmailServices.SendGridErrorMail(errorMailModel);
//                 }
//             }
//         }
//         catch (Exception ex)
//         {
//             throw ex;
//         }

//         return true;
//     }

//     public async Task<Tuple<int, Guid>> ProcessURLForSocial(string urlPId, string url)
//     {
//         Guid userProviderId = Guid.Empty;
//         try
//         {
//             string urlproviderId = urlPId;
//             var isAdminConsentFlow = "";
//             if (urlPId == "327915f0-677a-444c-99a8-1b4998a4623c" || urlPId == "cef7b630-3231-4a0b-9587-e7f7b56d6a1f" ||
//                 urlPId == "5ad6cf77-b8f0-4499-a60f-e53b62472732" || urlPId == "a45b6b98-9d24-4c33-b464-ac585f768752" ||
//                 urlPId == "37f357b8-3624-4dcb-8c35-d4d47a0cd5a7" || urlPId == "61d04454-38a0-41de-aed8-1bb044e98443")
//             {
//                 TelegramService.SendMessageToTestBot("Login startedd!");
//                 isAdminConsentFlow = "1";
//             }

//             var sourceURL = weddb.Urls.FirstOrDefault(w => w.Name.Contains(url));
//             var providerId = Guid.Parse(urlproviderId);
//             var providerUrls = weddb.ProviderUrls.FirstOrDefault(w => w.ProviderId == providerId);
//             identifierList = weddb.Identifiers.Where(w => w.ProviderId == providerUrls.ProviderId && w.IsActive)
//                 .ToList();
//             var stateValue = "";
//             var deviceId = "";
//             var deviceTypeId = "";
//             var appId = "6de53356-eb21-4830-a7de-7e2256395525";
//             string isLogin = "1";
//             string timeZone = "";
//             try
//             {
//                 if (sourceURL.Identifier.Contains("Q-")) //QueryString ofcode|Q-code,ofstate=|Q-state
//                 {
//                     foreach (var urlIdentifier in
//                              sourceURL.Identifier.Replace("Q-", "").Split(',')) // ofcode|code,ofstate=|state
//                     {
//                         var nv = urlIdentifier.Split('|'); //splitting ofcode & code
//                         {
//                             var localIdentifier =
//                                 identifierList.FirstOrDefault(w => w.Name == nv[0]); //checking for ofcode or ofstate
//                             if (localIdentifier != null)
//                             {
//                                 if (localIdentifier.Value == null) //{apptoken}_{appprovivsionid} for state
//                                 {
//                                     TelegramService.SendMessageToTestBot(nv[1].ToString());
//                                     TelegramService.SendMessageToTestBot(_httpContextAccessor.HttpContext.Request.QueryString.ToString());
//                                     string value = _httpContextAccessor.HttpContext.Request.Query[nv[1]].FirstOrDefault();
//                                     localIdentifier.Value = value;
//                                 }
//                                 else
//                                 {
//                                     stateValue = _httpContextAccessor.HttpContext.Request.Query[nv[1]].FirstOrDefault();
//                                     var valuesplit = localIdentifier.Value.Split('_');
//                                     var querysplit = stateValue.Split('_');
//                                     for (int i = 0; i < querysplit.Length; i++)
//                                     {
//                                         if (valuesplit[i].ToString() == "{deviceid}")
//                                         {
//                                             deviceId = querysplit[i];
//                                         }
//                                         else if (valuesplit[i].ToString() == "{devicetypeid}")
//                                         {
//                                             deviceTypeId = querysplit[i];
//                                         }
//                                         else if (valuesplit[i].ToString() == "{apptoken}")
//                                         {
//                                             deviceId = querysplit[i];
//                                         }
//                                         else if (valuesplit[i].ToString() == "{appprovivsionid}")
//                                         {
//                                             deviceTypeId = querysplit[i];
//                                         }
//                                         else if (valuesplit[i].ToString() == "{appid}")
//                                         {
//                                             appId = querysplit[i];
//                                         }
//                                         else if (valuesplit[i].ToString() == "{islogin}")
//                                         {
//                                             isLogin = querysplit[i];
//                                         }
//                                         else if (valuesplit[i].ToString() == "{timezone}")
//                                         {
//                                             timeZone = querysplit[i];
//                                         }
//                                         else
//                                         {
//                                             nameValueList.Add(new AuthenticatedController.TempList()
//                                             { Name = stateValue[i].ToString(), Value = querysplit[i] });
//                                         }
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                 }
//             }
//             catch (Exception ex)
//             {
//                 TelegramService.SendMessageToTestBot("Exception: " + ex.ToString() + " Stack : " + ex.StackTrace);
//             }

//             if (string.IsNullOrEmpty(deviceId))
//             {
//                 deviceId = Guid.NewGuid().ToString();
//             }

//             TelegramService.SendMessageToTestBot("appId: " + appId);
//             TelegramService.SendMessageToTestBot("timezone: " + timeZone);
//             if (string.IsNullOrEmpty(deviceTypeId))
//                 deviceTypeId = "32372a70-84a5-4390-8071-8f89bcf9c6fa";
//             TelegramService.SendMessageToTestBot("1st step!");
//             var orgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");
//             var orgProvider = weodb.OrgProviders.FirstOrDefault(w =>
//                 w.OrgId == orgId && w.IsActive && w.ProviderId == providerId && w.IsActive);
//             orgProviderIdentifierList = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == orgProvider.Id && w.IsActive)
//                 .ToList();

//             providerUrlList = weddb.Urls.Where(w => w.ProviderUrls
//                 .Where(c => c.ProviderId == providerId && c.IsActive == true && w.ShowOrder > 2000 &&
//                             w.ShowOrder < 3000)
//                 .Count() > 0).OrderBy(w => w.ShowOrder).ToList();

//             foreach (var providerUrl in providerUrlList)
//             {
//                 TelegramService.SendMessageToTestBot("providerurl name: " + providerUrl.Name);
//                 if (providerUrl.HasHeaders)
//                 {
//                     var headerList = weddb.Headers.Where(w => w.Urlid == providerUrl.Id && w.IsActive == true);
//                     foreach (var header in headerList)
//                     {
//                         //headersList.Clear();
//                         if (header.Prefix.Contains("{") == true)
//                         {
//                             var headervalue = header.Prefix;
//                             foreach (var item in identifierList)
//                             {
//                                 if (headervalue.Contains("{" + item.Name + "}"))
//                                 {
//                                     if (item.Value != null)
//                                     {
//                                         headervalue = headervalue.Replace("{" + item.Name + "}", item.Value);
//                                         headersList.Add(new AuthenticatedController.TempList
//                                         { Name = header.Name, Value = headervalue });
//                                     }
//                                 }
//                             }

//                             foreach (var item in orgProviderIdentifierList)
//                             {
//                                 var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == item.IdentifierId)
//                                     .Name;
//                                 if (headervalue.Contains("{" + identifierName + "}"))
//                                 {
//                                     headervalue = headervalue.Replace("{" + identifierName + "}", item.Value);
//                                     headersList.Add(new AuthenticatedController.TempList
//                                     { Name = header.Name, Value = headervalue });
//                                 }
//                             }
//                         }
//                         else
//                         {
//                             headersList.Add(new AuthenticatedController.TempList
//                             { Name = header.Name, Value = header.Prefix });
//                         }
//                     }
//                 }

//                 var dataToReplace = "";
//                 if (providerUrl.NeedsData) //amper
//                 {
//                     dataToReplace = providerUrl.Posts.FirstOrDefault(w => w.IsActive = true).Name;
//                     foreach (var item in identifierList)
//                     {
//                         dataToReplace = dataToReplace.Replace("{" + item.Name + "}", item.Value);
//                     }

//                     foreach (var item in orgProviderIdentifierList)
//                     {
//                         var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == item.IdentifierId).Name;
//                         dataToReplace = dataToReplace.Replace("{" + identifierName + "}", item.Value);
//                     }
//                 }

//                 var finalURL = providerUrl.Name;
//                 finalURL = FindAndReplaceIndentifiers(finalURL);
//                 TelegramService.SendMessageToTestBot("final: " + finalURL);

//                 if (providerUrl.Httptype == 1)
//                 {
//                     bool postRetuenValue = false;
//                     //  TelegramService.SendMessageToTestBot("Final Url: " + finalURL);
//                     postRetuenValue = await PostClient(finalURL, providerUrl, dataToReplace);
//                     CommonData.sbnew.AppendLine("Linkedin Post token api response: " + postRetuenValue);
//                     TelegramService.SendMessageToTestBot("POST: " + postRetuenValue);
//                     if (postRetuenValue == false)
//                     {
//                         return new Tuple<int, Guid>(3, userProviderId);
//                     }

//                     headersList.Clear();
//                 }
//                 else if (providerUrl.Httptype == 2)
//                 {
//                     bool getRetuenValue = false;
//                     getRetuenValue = await GetClient(finalURL, providerUrl, dataToReplace);
//                     CommonData.sbnew.AppendLine("Linkedin me api response: " + getRetuenValue);
//                     TelegramService.SendMessageToTestBot("GET: " + getRetuenValue);
//                     if (getRetuenValue == false)
//                     {
//                         return new Tuple<int, Guid>(3, userProviderId);
//                     }

//                     headersList.Clear();
//                 }

//                 urlsCompleted.Add(
//                     new AuthenticatedController.TempList { Name = providerUrl.Id.ToString(), Value = "1" });
//             }

//             var provider = weddb.Providers.FirstOrDefault(w => w.Id == providerId);
//             var emailId = provider.Name ?? "";
//             bool mailExist = false;
//             if (urlproviderId == "4eb93b78-64fd-4a3e-9428-f6dd41d98584" ||
//                 urlproviderId == "5ad6cf77-b8f0-4499-a60f-e53b62472732" ||
//                 urlproviderId == "327915f0-677a-444c-99a8-1b4998a4623c".ToLower() ||
//                 urlproviderId == "37f357b8-3624-4dcb-8c35-d4d47a0cd5a7")
//             {
//                 mailExist = identifierList.Any(w =>
//                     w.Name.Contains("userprincipalname") && w.Value != null && w.Value != "");
//             }
//             else
//             {
//                 mailExist = identifierList.Any(w => w.Name.Contains("mail") && w.Value != null && w.Value != "");
//             }

//             if (mailExist == false)
//             {
//                 var nameExist = identifierList.Any(w => w.Name.Contains("name") && w.Value != null && w.Value != "");
//                 emailId = (nameExist == true)
//                     ? identifierList.FirstOrDefault(w => w.Name.Contains("name") && w.Value != null && w.Value != "")
//                         .Value.ToLower().ToString()
//                     : "";
//                 if (emailId == "")
//                 {
//                     emailId = (mailExist == true)
//                         ? identifierList
//                             .FirstOrDefault(w => w.Name.Contains("mail") && w.Value != null && w.Value != "").Value
//                             .ToLower().ToString()
//                         : ""; //;
//                 }
//             }
//             else
//             {
//                 if (urlproviderId == "4eb93b78-64fd-4a3e-9428-f6dd41d98584" ||
//                     urlproviderId == "5ad6cf77-b8f0-4499-a60f-e53b62472732" ||
//                     urlproviderId == "327915f0-677a-444c-99a8-1b4998a4623c".ToLower() ||
//                     urlproviderId == "37f357b8-3624-4dcb-8c35-d4d47a0cd5a7")
//                 {
//                     emailId = (mailExist == true)
//                         ? identifierList
//                             .FirstOrDefault(w =>
//                                 w.Name.Contains("userprincipalname") && w.Value != null && w.Value != "").Value
//                             .ToLower().ToString()
//                         : ""; //;
//                 }
//                 else
//                 {
//                     emailId = (mailExist == true)
//                         ? identifierList
//                             .FirstOrDefault(w => w.Name.Contains("mail") && w.Value != null && w.Value != "").Value
//                             .ToLower().ToString()
//                         : ""; //;
//                 }
//             }

//             if (isAdminConsentFlow == "1")
//             {
//                 Guid orgID = CommonData.WhatElseCustomerOrgId;

//                 bool isProvisioned = false;
//                 bool isAccepted = true;
//                 bool isTrail = false;
//                 bool isClaimed = true;

//                 var userDomain = emailId.Split('@');
//                 var domainName = userDomain[1].ToLower();
//                 var isfreeDomain =
//                     weddb.FreeDomains.FirstOrDefault(w => w.DomainName.ToLower() == domainName.ToLower());
//                 var orgExist = weodb.Orgs.Any(w => w.Domains == domainName);
//                 if (orgExist == true)
//                 {
//                     var org = weodb.Orgs.FirstOrDefault(w => w.Domains.ToLower() == domainName.ToLower());
//                     orgID = org.Id;
//                 }
//                 else
//                 {
//                     var country1 = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
//                     var org = new Org();
//                     org.Id = Guid.NewGuid();
//                     org.IsBeta = false;
//                     org.AppId = appId;
//                     org.ModifiedDate = DateTime.UtcNow;
//                     org.CreatedDate = DateTime.UtcNow;
//                     org.AllowedAccounts = 1;
//                     org.Name = domainName.ToLower();
//                     org.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
//                     org.Countries = country1.Name + "|" + country1.Prefix;
//                     org.Domains = domainName;
//                     org.IsActive = true;
//                     org.IsRestricted = false;
//                     org.IsProvider = false;
//                     org.IsPurchaser = false;
//                     org.IsAdminGivenFree = false;
//                     org.IsInternal = false;
//                     weodb.Orgs.Add(org);
//                     weodb.SaveChanges();

//                     orgID = org.Id;
//                     isProvisioned = true;
//                     isAccepted = true;
//                     isTrail = false;
//                     isClaimed = false;

//                     if (isfreeDomain == null)
//                     {
//                         //ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");

//                         //EntityResult result = ChargeBee.Models.Customer.Create()
//                         //             .FirstName("Company")
//                         //             .LastName("OAYAW")
//                         //             .Email(emailId)
//                         //             .Company("Company - " + domainName).Request();
//                         //ChargeBee.Models.Customer customer = result.Customer;
//                         //ChargeBee.Models.Card card = result.Card;
//                         //wepdb.SaveChanges();
//                     }
//                     else
//                     {
//                         //ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");

//                         //EntityResult result = ChargeBee.Models.Customer.Create()
//                         //             .FirstName("Company")
//                         //             .LastName("OAYAW")
//                         //             .Email(emailId)
//                         //             .Company("Freedomain - " + domainName).Request();
//                         //ChargeBee.Models.Customer customer = result.Customer;
//                         //ChargeBee.Models.Card card = result.Card;
//                         //wepdb.SaveChanges();
//                     }
//                 }

//                 var orgProviderExist = weodb.OrgProviders.FirstOrDefault(w =>
//                     w.OrgId == orgID && w.ProviderId == providerId && w.IsActive == true);
//                 if (orgProviderExist == null)
//                 {
//                     var op = new OrgProvider();
//                     op.Id = Guid.NewGuid();
//                     op.CreatedDate = DateTime.UtcNow;
//                     op.ModifiedDate = DateTime.UtcNow;
//                     op.IsActive = true;
//                     op.OrgId = orgID;
//                     op.ProviderId = provider.Id;
//                     op.CanSaveForOffline = true;
//                     op.ForceCallLog = true;
//                     weodb.OrgProviders.Add(op);

//                     var weOrgProvider = weodb.OrgProviders.FirstOrDefault(w =>
//                         w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == provider.Id &&
//                         w.IsActive == true);
//                     if (weOrgProvider != null)
//                     {
//                         var weOrgProviderIdentifiers = weodb.OrgIdentifiers
//                             .Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();

//                         foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
//                         {
//                             var orgIdentifier = new OrgIdentifier();
//                             orgIdentifier.Id = Guid.NewGuid();
//                             orgIdentifier.OrgProviderId = op.Id;
//                             orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
//                             orgIdentifier.IsActive = true;
//                             orgIdentifier.CreatedDate = DateTime.UtcNow;
//                             orgIdentifier.ModifiedDate = DateTime.UtcNow;
//                             orgIdentifier.ShowSequence = 1;
//                             orgIdentifier.Value = weOrgProviderIdentifier.Value;
//                             weodb.OrgIdentifiers.Add(orgIdentifier);
//                             weodb.SaveChanges();
//                         }
//                     }
//                 }

//                 if (urlproviderId == "327915f0-677a-444c-99a8-1b4998a4623c")
//                 {
//                     var op1 = weodb.OrgProviders.FirstOrDefault(w =>
//                         w.OrgId == orgID && w.IsActive && w.ProviderId == providerId && w.IsActive);
//                     orgProviderIdentifierList2 =
//                         weodb.OrgIdentifiers.Where(w => w.OrgProviderId == op1.Id && w.IsActive).ToList();

//                     if (orgProviderIdentifierList2.Count > 0)
//                     {
//                         try
//                         {
//                             Guid TeamsConsentIId = Guid.Parse("3A225307-704A-4E89-AC95-07169F88A008");
//                             var consentIdentifier =
//                                 orgProviderIdentifierList2.First(w => w.IdentifierId == TeamsConsentIId);
//                             consentIdentifier.Value = "1";
//                             weodb.SaveChanges();
//                         }
//                         catch (Exception ex)
//                         {
//                             TelegramService.SendMessageToTestBot("exception!");
//                             TelegramService.SendMessageToTestBot("ex: " + ex.ToString());
//                         }
//                     }
//                 }
//             }

//             Guid AppId = Guid.Parse(appId);

//             var postZoneUrl = "https://eu-fc-ap-sz.azurewebsites.net/";
//             TelegramService.SendMessageToTestBot("serverzone starts!");

//             var authUrl = weadb.Servers.FirstOrDefault(w =>
//                 w.ServerType == 17 && w.AppId.ToString().ToLower() == AppId.ToString().ToLower() && w.IsActive).Url;
//             var tempUrl = weadb.Servers.FirstOrDefault(w =>
//                 w.ServerType == 17 && w.AppId.ToString().ToLower() == AppId.ToString().ToLower() && w.IsActive).Url;
//             emailId = (emailId == "") ? provider.Name : emailId;
//             eId = emailId;
//             string firstName = "";
//             string lasttName = "";
//             string profileId = "";
//             string displayImage = "";
//             string country = "";
//             string role = "";
//             PropertyModels.AuthModels.AuthUserModel authUserModel = new PropertyModels.AuthModels.AuthUserModel();
//             PropertyModels.AuthModels.ValidationModel validationModel = new PropertyModels.AuthModels.ValidationModel();
//             if (urlproviderId == "4eb93b78-64fd-4a3e-9428-f6dd41d98584" ||
//                 urlproviderId == "232a6c90-a9f4-4b8c-98b9-fc050e84e184" ||
//                 urlproviderId == "034208d2-fe6c-49e6-8d72-70e38598dc2e" ||
//                 urlproviderId == "5ad6cf77-b8f0-4499-a60f-e53b62472732" ||
//                 urlproviderId == "327915f0-677a-444c-99a8-1b4998a4623c".ToLower() ||
//                 urlproviderId == "cef7b630-3231-4a0b-9587-e7f7b56d6a1f" ||
//                 urlproviderId == "a45b6b98-9d24-4c33-b464-ac585f768752" ||
//                 urlproviderId == "37f357b8-3624-4dcb-8c35-d4d47a0cd5a7" ||
//                 urlproviderId == "61d04454-38a0-41de-aed8-1bb044e98443")
//             {
//                 TelegramService.SendMessageToTestBot("islogin == 11");
//                 var displayname =
//                     identifierList.Any(w => w.Name.Contains("displayname") && w.Value != null && w.Value != "");
//                 if (displayname)
//                 {
//                     firstName = identifierList
//                         .First(w => w.Name.Contains("displayname") && w.Value != null && w.Value != "").Value
//                         .ToString();
//                 }

//                 var fnameExists =
//                     identifierList.Any(w => w.Name.Contains("first_name") && w.Value != null && w.Value != "");
//                 if (fnameExists)
//                 {
//                     firstName = identifierList
//                         .First(w => w.Name.Contains("first_name") && w.Value != null && w.Value != "").Value.ToString();
//                 }

//                 var nameExists = identifierList.Any(w => w.Name.Contains("gcname") && w.Value != null && w.Value != "");
//                 if (nameExists)
//                 {
//                     firstName = identifierList.First(w => w.Name.Contains("gcname") && w.Value != null && w.Value != "")
//                         .Value.ToString();
//                 }

//                 var lnameExists =
//                     identifierList.Any(w => w.Name.Contains("last_name") && w.Value != null && w.Value != "");
//                 if (lnameExists)
//                 {
//                     lasttName = identifierList
//                         .First(w => w.Name.Contains("last_name") && w.Value != null && w.Value != "").Value.ToString();
//                 }

//                 var isRole = identifierList.Any(w => w.Name.Contains("jobtitle") && w.Value != null && w.Value != "");
//                 if (isRole)
//                 {
//                     role = identifierList.First(w => w.Name.Contains("jobtitle") && w.Value != null && w.Value != "")
//                         .Value.ToString();
//                 }

//                 TelegramService.SendMessageToTestBot("firstname: " + firstName);
//                 TelegramService.SendMessageToTestBot("lastname: " + lasttName);
//                 if (isLogin == "1")
//                 {
//                     TelegramService.SendMessageToTestBot("islogin == 1");
//                     // call serverzone api
//                     // TelegramService.SendMessageToTestBot(deviceId);
//                     // TelegramService.SendMessageToTestBot(deviceTypeId);
//                     Guid dId1 = Guid.Empty;
//                     if (!string.IsNullOrEmpty(deviceId))
//                         dId1 = Guid.Parse(deviceId);
//                     Guid dtId1 = Guid.Parse(deviceTypeId);
//                     authUserModel.AppId = AppId;
//                     authUserModel.CountryId = country;
//                     authUserModel.EmailAddress = emailId;
//                     authUserModel.PhoneNumber = "";
//                     authUserModel.Password = deviceId.ToString().ToLower();
//                     authUserModel.Coupon = "";
//                     authUserModel.IsPreAuthenticated = true;
//                     authUserModel.FirstName = firstName;
//                     authUserModel.LastName = lasttName;
//                     authUserModel.LoginFlowId = providerId;
//                     authUserModel.DeviceId = dId1;
//                     authUserModel.DeviceType = dtId1;
//                     authUserModel.TimeZone = timeZone;
//                     var json1 = JsonConvert.SerializeObject(authUserModel);
//                     postZoneUrl = postZoneUrl + "06E12CFF";
//                     TelegramService.SendMessageToTestBot(json1.ToString() + " " + postZoneUrl);
//                     var client1 = new RestSharp.RestClient(postZoneUrl);
//                     //client1.Timeout = 600 * 1000;
//                     var request1 = new RestRequest();
//                     request1.AddHeader("content-type", "application/json");
//                     request1.AddJsonBody(json1);
//                     var response1 = await client1.PostAsync(request1);
//                     TelegramService.SendMessageToTestBot(response1.StatusCode.ToString());
//                     //   TelegramService.SendMessageToTestBot(JsonConvert.SerializeObject(response1).ToString());
//                     //  PreloginReturnModel2 Djson3 = JsonConvert.DeserializeObject<PreloginReturnModel2>(response1.Content);

//                     // call validate api
//                     validationModel.IsPreAuthenticated = true;
//                     validationModel.EmailAddress = emailId;
//                     validationModel.AppId = AppId;
//                     validationModel.DeviceId = dId1;
//                     validationModel.DeviceTypeId = dtId1;
//                     validationModel.OTP = deviceId.ToString().ToLower();
//                     validationModel.LanguageId = "English (US)";

//                     var validateJson1 = JsonConvert.SerializeObject(validationModel);
//                     authUrl = authUrl + "165C8F0E";
//                     TelegramService.SendMessageToTestBot(authUrl);
//                     TelegramService.SendMessageToTestBot(validateJson1);
//                     var client3 = new RestSharp.RestClient(authUrl);
//                     //client3.Timeout = 600 * 1000;
//                     var request3 = new RestRequest();
//                     request3.AddHeader("content-type", "application/json;charset=UTF-8");
//                     request3.AddJsonBody(validateJson1);
//                     var response3 = await client3.PostAsync(request3);
//                     PropertyModels.AuthModels.ValidateReturnModel Djson4 =
//                         JsonConvert.DeserializeObject<PropertyModels.AuthModels.ValidateReturnModel>(response3.Content);

//                     //TelegramService.SendMessageToTestBot(emailId);
//                     TelegramService.SendMessageToTestBot(response3.StatusCode.ToString());
//                     var uId = wepdb.Users.First(w => w.Email == emailId && w.AppId == AppId).Id;
//                     appUserId = uId.ToString();

//                     //  TelegramService.SendMessageToTestBot(uId.ToString());
//                     if (Djson4.UserStatusModel.Registration == false)
//                     {
//                         try
//                         {
//                             string registerUrl = weadb.Servers.FirstOrDefault(w =>
//                                 w.ServerType == 17 && w.AppId.ToString().ToLower() == AppId.ToString().ToLower() &&
//                                 w.IsActive).Url;
//                             PropertyModels.DataModels.UserDetailModel regModel =
//                                 new PropertyModels.DataModels.UserDetailModel();
//                             regModel.Company = "";
//                             regModel.Designation = "";
//                             regModel.Email = emailId;
//                             regModel.FirstName = firstName;
//                             regModel.LastName = lasttName;
//                             regModel.UserId = uId;
//                             regModel.AppId = AppId;

//                             var token = wetdb.Tokens.First(w => w.UserId == uId && w.IsActive == true).Id;
//                             var json2 = JsonConvert.SerializeObject(regModel);
//                             registerUrl = registerUrl + "59187A47";
//                             var client4 = new RestSharp.RestClient(registerUrl);
//                             //   client4.Timeout = 600 * 1000;
//                             var request4 = new RestRequest();
//                             request4.AddHeader("content-type", "application/json;charset=UTF-8");
//                             request4.AddHeader("authToken", token.ToString());
//                             request4.AddJsonBody(json2);
//                             var response4 = await client4.PostAsync(request4);
//                             TelegramService.SendMessageToTestBot(response4.StatusCode.ToString());
//                         }
//                         catch (Exception ex)
//                         {
//                             TelegramService.SendMessageToTestBot(ex.ToString());
//                         }
//                     }
//                 }

//                 User user;
//                 if (isLogin == "1")
//                     user = wepdb.Users.First(w => w.Email == emailId && w.AppId == AppId);
//                 else
//                 {
//                     Guid tokenValue = Guid.Parse(deviceId);
//                     var usrId = wetdb.Tokens.First(w => w.Id == tokenValue).UserId;
//                     user = wepdb.Users.First(w => w.Id == usrId);
//                 }

//                 if (user != null)
//                 {
//                     TelegramService.SendMessageToTestBot(user.Email);
//                     TelegramService.SendMessageToTestBot(user.Id.ToString());
//                     if (urlproviderId == "5ad6cf77-b8f0-4499-a60f-e53b62472732" ||
//                         urlproviderId == "327915f0-677a-444c-99a8-1b4998a4623c".ToLower() ||
//                         urlproviderId == "cef7b630-3231-4a0b-9587-e7f7b56d6a1f" ||
//                         urlproviderId == "a45b6b98-9d24-4c33-b464-ac585f768752" ||
//                         urlproviderId == "37f357b8-3624-4dcb-8c35-d4d47a0cd5a7" ||
//                         urlproviderId == "61d04454-38a0-41de-aed8-1bb044e98443")
//                     {
//                         Guid teamsProviderId = Guid.Parse(urlproviderId);
//                         var isExistingUP = wepdb.UserProviders.Any(w =>
//                             w.UserId == user.Id && w.ProviderId == teamsProviderId && w.IsActive &&
//                             w.EmailAddress.ToLower() == emailId.ToLower() && w.AppId == user.AppId);

//                         if (isExistingUP)
//                         {
//                             TelegramService.SendMessageToTestBot("IsExisting");
//                             var existingUP = wepdb.UserProviders.First(w =>
//                                 w.UserId == user.Id && w.ProviderId == teamsProviderId && w.IsActive &&
//                                 w.EmailAddress.ToLower() == emailId.ToLower() && w.AppId == user.AppId);
//                             existingUP.ModifiedDate = DateTime.UtcNow;
//                             TelegramService.SendMessageToTestBot("up id: " + existingUP.Id.ToString());
//                             foreach (var identifier in identifierList)
//                             {
//                                 try
//                                 {
//                                     if (identifier.Value != null && identifier.Value != "")
//                                     {
//                                         TelegramService.SendMessageToRahulTestBot("for identifier: " + identifier.Name +
//                                             ": " + identifier.Value);
//                                         var isExistingUI = wepdb.UserProviderIdentifiers.Any(w =>
//                                             w.UserProviderId == existingUP.Id && w.IdentifierId == identifier.Id);
//                                         if (isExistingUI)
//                                         {
//                                             TelegramService.SendMessageToRahulTestBot("is existing ui: " +
//                                                 identifier.Name + ": " + identifier.Value);
//                                             var existingUI = wepdb.UserProviderIdentifiers.First(w =>
//                                                 w.UserProviderId == existingUP.Id && w.IdentifierId == identifier.Id);
//                                             existingUI.Value = identifier.Value;
//                                             existingUI.ModifiedDate = DateTime.UtcNow;
//                                             wepdb.SaveChanges();
//                                         }
//                                         else
//                                         {
//                                             TelegramService.SendMessageToRahulTestBot("not existing ui: " +
//                                                 identifier.Name + ": " + identifier.Value);
//                                             var upIdentifiers = new UserProviderIdentifier();
//                                             upIdentifiers.Id = Guid.NewGuid();
//                                             upIdentifiers.IdentifierId = identifier.Id;
//                                             upIdentifiers.UserProviderId = existingUP.Id;
//                                             upIdentifiers.Value = identifier.Value;
//                                             upIdentifiers.IsActive = true;
//                                             upIdentifiers.CreatedDate = DateTime.UtcNow;
//                                             upIdentifiers.ModifiedDate = DateTime.UtcNow;
//                                             wepdb.UserProviderIdentifiers.Add(upIdentifiers);
//                                             wepdb.SaveChanges();
//                                         }
//                                     }
//                                 }
//                                 catch (Exception ex)
//                                 {
//                                     continue;
//                                     // throw;
//                                 }
//                             }
//                         }
//                         else
//                         {
//                             TelegramService.SendMessageToTestBot("Not Existing");
//                             var newUserProvider = new UserProvider();
//                             newUserProvider.Id = Guid.NewGuid();
//                             newUserProvider.UserId = user.Id;
//                             newUserProvider.AppId = user.AppId;
//                             newUserProvider.OrgId = orgId;
//                             newUserProvider.ProviderId = teamsProviderId;
//                             newUserProvider.EmailAddress = emailId.ToLower();
//                             newUserProvider.IsAuthenticated = true;
//                             newUserProvider.IsActive = true;
//                             newUserProvider.Code = "";
//                             newUserProvider.ActiveFrom = DateTime.UtcNow;
//                             newUserProvider.ActiveTill = DateTime.UtcNow.AddYears(1);
//                             newUserProvider.IsProvisioned = false;
//                             newUserProvider.IsFree = true;
//                             newUserProvider.IsPayed = false;
//                             newUserProvider.IsFullSyncDone = false;
//                             newUserProvider.Source = 0;
//                             newUserProvider.PurchaseId = "";
//                             newUserProvider.PurchaseState = 0;
//                             newUserProvider.ProductId = "";
//                             newUserProvider.CreatedDate = DateTime.UtcNow;
//                             newUserProvider.ModifiedDate = DateTime.UtcNow;
//                             newUserProvider.IsRelogginRequired = false;
//                             newUserProvider.Priority = 100;
//                             int trialDays = 30;
//                             newUserProvider.IsTrial = false;
//                             wepdb.UserProviders.Add(newUserProvider);
//                             wepdb.SaveChanges();

//                             foreach (var identifier in identifierList)
//                             {
//                                 try
//                                 {
//                                     if (identifier.Value != null && identifier.Value != "")
//                                     {
//                                         var upIdentifiers = new UserProviderIdentifier();
//                                         upIdentifiers.Id = Guid.NewGuid();
//                                         upIdentifiers.IdentifierId = identifier.Id;
//                                         upIdentifiers.UserProviderId = newUserProvider.Id;
//                                         upIdentifiers.Value = identifier.Value;
//                                         upIdentifiers.IsActive = true;
//                                         upIdentifiers.CreatedDate = DateTime.UtcNow;
//                                         upIdentifiers.ModifiedDate = DateTime.UtcNow;
//                                         wepdb.UserProviderIdentifiers.Add(upIdentifiers);
//                                         wepdb.SaveChanges();
//                                     }
//                                 }
//                                 catch (Exception ex)
//                                 {
//                                     continue;
//                                     // throw;
//                                 }
//                             }

//                             wepdb.SaveChanges();
//                         }
//                     }
//                 }
//                 // call signalr and onesignal

//                 //if (deviceTypeId.ToLower() != "9C329792-9519-4D6C-BE9D-F12898113416".ToLower())
//                 //{
//                 var appInfo2 = weadb.Apps.FirstOrDefault(w => w.Id == AppId && w.IsActive);
//                 var androidOneSignalId2 = appInfo2.AndroidOneSignalId;
//                 var androidOneSignalKey2 = appInfo2.AndroidOneSignalApiKey;
//                 var IosOneSignalId2 = appInfo2.IosoneSignalId;
//                 var IosOneSignalKey2 = appInfo2.IosoneSignalApiKey;
//                 var WebOneSignalId2 = appInfo2.WebOneSignalId;
//                 var WebOneSignalKey2 = appInfo2.WebOneSignalApiKey;

//                 var authMessage2 = "You are authenticated" + "\\n" + "Tap to proceed!";
//                 if (AppId == CommonData.WEAppId)
//                 {
//                     string res = PushServices.DevicePushForLogin(androidOneSignalId2, androidOneSignalKey2, "",
//                         deviceId, authMessage2, appInfo2.Name, userId: user.Id.ToString(), eventType: 34,
//                         tempAuthUrl: tempUrl, emailId: emailId);
//                 }
//                 else if (AppId == CommonData.OPAppId)
//                 {
//                     NotifyPayload payload = new NotifyPayload()
//                     {
//                         NotificationId = Guid.NewGuid().ToString(),
//                         UserId = user.Id.ToString(),
//                         DeviceId = deviceId,
//                         EmailId = emailId,
//                         EventType = 34,
//                         AuthUrl = tempUrl
//                     };
//                     string res = PushServices.SignalPush(androidOneSignalId2, androidOneSignalKey2, deviceId,
//                         authMessage2, appInfo2.Name, payload);
//                     // }
//                 }

//                 Guid provisionId = Guid.Empty;
//                 Guid userId = user.Id;
//                 Provision provision = new Provision();
//                 if (provisionId == Guid.Empty)
//                 {
//                     bool shouldCreateUserEmail =
//                         !wepdb.UserEmails.Any(w => w.Email.ToLower() == emailId.ToLower() && w.UserId == user.Id);

//                     var userEmailExists = wepdb.UserEmails.FirstOrDefault(w =>
//                         w.Email.ToLower() == emailId.ToLower() && w.UserId == user.Id);
//                     if (shouldCreateUserEmail)
//                     {
//                         userEmailExists = new UserEmail()
//                         {
//                             Id = Guid.NewGuid(),
//                             UserId = userId,
//                             AddedBy = userId.ToString(),
//                             CreatedDate = DateTime.UtcNow,
//                             DisplayName = user.FirstName + " " + user.LastName,
//                             Email = emailId.ToLower(),
//                             IsActive = true,
//                             IsEmailValidated = false,
//                             IsPersonal = true,
//                             ModifiedDate = DateTime.UtcNow,
//                             IsAccessible = false,
//                             IsMultiUser = false,
//                             IsGroupEmail = false,
//                             Status = 0,
//                             EmailType = 0,
//                             IsPreviouslyOwned = false,
//                             IsAddedByUser = true,
//                             IsAcceptedByUser = true
//                         };
//                         wepdb.UserEmails.Add(userEmailExists);
//                         wepdb.SaveChanges();
//                     }
//                     else
//                     {
//                         userEmailExists.IsActive = true;
//                         wepdb.SaveChanges();
//                     }

//                     Guid orgID = CommonData.WhatElseCustomerOrgId;
//                     bool isFree = false;
//                     bool isProvisioned = true;
//                     bool isTrail = false;
//                     bool isAccepted = true;
//                     bool isClaimed = false;
//                     Guid provisionID = Guid.Empty;
//                     userEmailExists.IsEmailValidated = true;
//                     wepdb.SaveChanges();
//                     var provisionExist = weodb.Provisions.Any(w =>
//                         w.UserId == user.Id && w.EmailAddress == userEmailExists.Email && w.ProviderId == providerId);
//                     TelegramService.SendMessageToTestBot("isProvisionExists : " + provisionExist);
//                     if (provisionID == Guid.Empty && !provisionExist)
//                     {
//                         TelegramService.SendMessageToTestBot("isProvisionExists : Not");
//                         var userDomain = userEmailExists.Email.Split('@');
//                         var domainName = userDomain[1];
//                         if (weddb.Domains.Any(w => w.Address == domainName && w.IsActive == true && w.IsFree == true) ==
//                             true)
//                         {
//                             isFree = true;
//                             isProvisioned = true;
//                             isTrail = false;
//                             isAccepted = true;
//                             isClaimed = false;
//                         }

//                         var weOrgProvider = weodb.OrgProviders.FirstOrDefault(w =>
//                             w.OrgId == CommonData.WhatElseCustomerOrgId && w.ProviderId == providerId &&
//                             w.IsActive == true);
//                         //var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive).ToList();

//                         bool isFreeDomain = weddb.FreeDomains.Any(w => w.DomainName.ToLower() == domainName.ToLower());
//                         if (isFreeDomain == false)
//                         {
//                             var orgExist = weodb.Orgs.Any(w => w.Domains == domainName);
//                             if (orgExist == true)
//                             {
//                                 var org1 = weodb.Orgs.FirstOrDefault(w => w.Domains == domainName);
//                                 orgID = org1.Id;
//                                 isFree = true;
//                                 isProvisioned = true;
//                                 isTrail = false;
//                                 isAccepted = true;
//                                 isClaimed = false;
//                                 var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w =>
//                                     w.OrgId == orgID && w.UserId == user.Id.ToString());
//                                 if (orgUserExist == null)
//                                 {
//                                     var orgUser = new OrgUser();
//                                     orgUser.Id = Guid.NewGuid();
//                                     orgUser.IsActive = true;
//                                     orgUser.CreatedDate = DateTime.UtcNow;
//                                     orgUser.ActivatedOn = DateTime.UtcNow;
//                                     orgUser.ModifiedDate = DateTime.UtcNow;
//                                     orgUser.OrgId = orgID;
//                                     orgUser.UserId = user.Id;
//                                     orgUser.IsAdmin = false;
//                                     weodb.OrgUsers.Add(orgUser);
//                                     weodb.SaveChanges();

//                                     var orgDirectory = new OrgDirectory();
//                                     orgDirectory.Id = Guid.NewGuid();
//                                     orgDirectory.OrgId = orgID;
//                                     orgDirectory.CreatedDate = DateTime.UtcNow;
//                                     orgDirectory.ModifiedDate = DateTime.UtcNow;
//                                     orgDirectory.IsActive = true;
//                                     orgDirectory.FirstName = user.FirstName ?? "";
//                                     orgDirectory.MiddleName = user.MiddleName ?? "";
//                                     orgDirectory.LastName = user.LastName ?? "";
//                                     orgDirectory.SanitizedNumber = "";
//                                     orgDirectory.ProvidedNumber = "";
//                                     orgDirectory.Email = user.Email ?? "";
//                                     orgDirectory.Designation = "";
//                                     orgDirectory.Salutation = "";
//                                     orgDirectory.UserId = user.Id.ToString();
//                                     orgDirectory.CountryId = user.CountryId;
//                                     weodb.OrgDirectories.Add(orgDirectory);
//                                     weodb.SaveChanges();
//                                 }

//                                 var orgProviderExist = weodb.OrgProviders.FirstOrDefault(w =>
//                                     w.OrgId == orgID && w.ProviderId == providerId && w.IsActive == true);
//                                 if (orgProviderExist == null)
//                                 {
//                                     var orgProvider1 = new OrgProvider();
//                                     orgProvider1.Id = Guid.NewGuid();
//                                     orgProvider1.CreatedDate = DateTime.UtcNow;
//                                     orgProvider1.ModifiedDate = DateTime.UtcNow;
//                                     orgProvider1.IsActive = true;
//                                     orgProvider1.OrgId = orgID;
//                                     orgProvider1.ProviderId = providerId;
//                                     orgProvider1.CanSaveForOffline = true;
//                                     orgProvider1.ForceCallLog = true;
//                                     weodb.OrgProviders.Add(orgProvider1);
//                                     weodb.SaveChanges();
//                                     if (weOrgProvider != null)
//                                     {
//                                         var weOrgProviderIdentifiers = weodb.OrgIdentifiers.Where(w =>
//                                             w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();

//                                         foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
//                                         {
//                                             var orgIdentifier = new OrgIdentifier();
//                                             orgIdentifier.Id = Guid.NewGuid();
//                                             orgIdentifier.OrgProviderId = orgProvider1.Id;
//                                             orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
//                                             orgIdentifier.IsActive = true;
//                                             orgIdentifier.CreatedDate = DateTime.UtcNow;
//                                             orgIdentifier.ModifiedDate = DateTime.UtcNow;
//                                             orgIdentifier.ShowSequence = 1;
//                                             orgIdentifier.Value = weOrgProviderIdentifier.Value;
//                                             weodb.OrgIdentifiers.Add(orgIdentifier);
//                                             weodb.SaveChanges();
//                                         }
//                                     }
//                                 }
//                             }
//                             else
//                             {
//                                 var Country = weddb.Countries.FirstOrDefault(w => w.Alpha2 == "IN");
//                                 var org1 = new Org();
//                                 org1.Id = Guid.NewGuid();
//                                 org1.IsBeta = false;
//                                 org1.AppId = appId;
//                                 // org1rg.IsPaid =;
//                                 org1.ModifiedDate = DateTime.UtcNow;
//                                 org1.CreatedDate = DateTime.UtcNow;
//                                 org1.AllowedAccounts = 1;
//                                 org1.Name = domainName.ToUpper();
//                                 org1.PlanId = Guid.Parse("7dc72a8d-d5fd-4809-81fe-add466cad370");
//                                 org1.Countries = Country.Name + "|" + Country.Prefix;
//                                 org1.Domains = domainName;
//                                 org1.IsActive = true;
//                                 org1.IsRestricted = false;
//                                 org1.IsProvider = false;
//                                 org1.IsPurchaser = false;
//                                 weodb.Orgs.Add(org1);
//                                 weodb.SaveChanges();
//                                 orgID = org1.Id;

//                                 var orgProvider1 = new OrgProvider();
//                                 orgProvider1.Id = Guid.NewGuid();
//                                 orgProvider1.CreatedDate = DateTime.UtcNow;
//                                 orgProvider1.ModifiedDate = DateTime.UtcNow;
//                                 orgProvider1.IsActive = true;
//                                 orgProvider1.OrgId = orgID;
//                                 orgProvider1.ProviderId = providerId;
//                                 orgProvider1.CanSaveForOffline = true;
//                                 orgProvider1.ForceCallLog = true;
//                                 weodb.OrgProviders.Add(orgProvider1);
//                                 weodb.SaveChanges();
//                                 if (weOrgProvider != null)
//                                 {
//                                     var weOrgProviderIdentifiers = weodb.OrgIdentifiers
//                                         .Where(w => w.OrgProviderId == weOrgProvider.Id && w.IsActive == true).ToList();
//                                     foreach (var weOrgProviderIdentifier in weOrgProviderIdentifiers)
//                                     {
//                                         var orgIdentifier = new OrgIdentifier();
//                                         orgIdentifier.Id = Guid.NewGuid();
//                                         orgIdentifier.OrgProviderId = orgProvider1.Id;
//                                         orgIdentifier.IdentifierId = weOrgProviderIdentifier.IdentifierId;
//                                         orgIdentifier.IsActive = true;
//                                         orgIdentifier.CreatedDate = DateTime.UtcNow;
//                                         orgIdentifier.ModifiedDate = DateTime.UtcNow;
//                                         orgIdentifier.ShowSequence = 1;
//                                         orgIdentifier.Value = weOrgProviderIdentifier.Value;
//                                         weodb.OrgIdentifiers.Add(orgIdentifier);
//                                         weodb.SaveChanges();
//                                     }
//                                 }
//                             }
//                         }
//                         else
//                         {
//                             orgID = CommonData.WhatElseCustomerOrgId;
//                         }

//                         var provider1 = weddb.Providers.FirstOrDefault(w => w.Id == providerId && w.IsActive == true);
//                         var existingProvision = weodb.Provisions.Any(w => w.AppId == user.AppId &&
//                                                                           w.EmailAddress == userEmailExists.Email
//                                                                           && w.ProviderId == providerId &&
//                                                                           w.OrgId == orgID && w.UserId == userId);
//                         if (!existingProvision)
//                         {
//                             var provision1 = new Provision();
//                             provision1.Id = Guid.NewGuid();
//                             provision1.OrgId = orgID; //CommonData.WhatElseCustomerOrgId;
//                             provision1.AppId = user.AppId;
//                             provision1.UserCustomerId = user.ChargebeeCustomerId;
//                             provision1.ProviderTypeId = provider1.ProviderTypeId;
//                             provision1.ProviderId = providerId;
//                             provision1.UserId = user.Id;
//                             provision1.UserProviderId = Guid.Empty;
//                             provision1.CreatedDate = DateTime.UtcNow;
//                             provision1.ModifiedDate = DateTime.UtcNow;
//                             provision1.IsActive = true;
//                             provision1.IsConverted = false;
//                             provision1.IsEnterpriseConverted = false;
//                             //provision.SanitizedNumber = user.SanitizedNumber;
//                             provision1.IsRedeemed = false;
//                             //provision.PhoneNumber = user.ProvidedNumber ?? "";
//                             //provision.Salutation = user.Salutation;
//                             provision1.FirstName = user.FirstName;
//                             provision1.LastName = user.LastName;
//                             provision1.MiddleName = user.MiddleName;
//                             provision1.CountryId = user.CountryId;
//                             provision1.UserId = user.Id;
//                             provision1.IsFree = isFree;
//                             provision1.IsProvisioned = isProvisioned;
//                             provision1.IsPayed = false;
//                             provision1.IsRequested = false;
//                             provision1.IsAccepted = isAccepted;
//                             provision1.IsPurchasedByUser = false;
//                             provision1.IsPurchasedOnAndroid = false;
//                             provision1.IsPurchasedOnIos = false;
//                             provision1.EmailAddress = userEmailExists.Email;
//                             provision1.IsClaimed = isClaimed;
//                             provision1.IsTrial = isTrail;
//                             weodb.Provisions.Add(provision1);
//                             weodb.SaveChanges();

//                             provision = provision1;
//                         }
//                     }
//                     else
//                     {
//                         var provision1 = weodb.Provisions.First(w =>
//                             w.UserId == user.Id && w.EmailAddress == userEmailExists.Email &&
//                             w.ProviderId == providerId);
//                         provision1.IsActive = true;
//                         weodb.SaveChanges();
//                         provision = provision1;
//                     }
//                 }

//                 if (emailId.Contains(","))
//                 {
//                     var mailIds = emailId.Split(',');
//                     var emailExist = mailIds.Contains(provision.EmailAddress);
//                     if (emailExist)
//                     {
//                         emailId = provision.EmailAddress;
//                     }
//                     else
//                     {
//                         return new Tuple<int, Guid>(3, userProviderId);
//                     }
//                 }

//                 if (provider.Id.ToString().ToLower() == CommonData.ZohoCRMProviderId.ToString().ToLower() ||
//                     provider.Id.ToString().ToLower() == CommonData.ZohoMailId.ToString().ToLower() ||
//                     provider.Id.ToString().ToLower() == CommonData.ZohoCalendarId.ToString().ToLower())
//                 {
//                     emailId = provision.EmailAddress;
//                 }

//                 if (provider.Id == CommonData.DynamicCRMProviderId ||
//                     emailId.ToString().ToLower() == provision.EmailAddress.ToLower())
//                 {
//                     TelegramService.SendMessageToTestBot("provision email id matched : " + true);
//                     var code = identifierList
//                         .FirstOrDefault(w => w.Name.Contains("code") && w.Value != null && w.Value != "").Value;
//                     TelegramService.SendMessageToTestBot(code);
//                     try
//                     {
//                         var userProvider = wepdb.UserProviders.FirstOrDefault(w =>
//                                        w.UserId == userId && w.EmailAddress == emailId.ToString().ToLower() &&
//                                        w.ProviderId == providerId && w.IsActive && w.IsRelogginRequired == false &&
//                                        w.AppId == user.AppId);
//                         if (userProvider != null)
//                         {
//                             userProvider.ProvisionId = provision.Id;
//                             userProvider.ModifiedDate = DateTime.UtcNow;
//                             wepdb.SaveChanges();
//                         }
//                         //if (userProvider != null)
//                         //{
//                         TelegramService.SendMessageToTestBot("UserProvider Id: " + userProvider.Id.ToString());
//                         TelegramService.SendMessageToTestBot("Provision Id: " + provision.Id.ToString());
//                         var userProviderProvision = wepdb.UserProviders.FirstOrDefault(w =>
//                             w.UserId == userId && w.ProvisionId == provision.Id && w.ProviderId == providerId &&
//                             w.IsActive && w.EmailAddress == emailId.ToString().ToLower() && w.AppId == user.AppId); //

//                         TelegramService.SendMessageToTestBot("UserProvider Provision: " +
//                                                              userProviderProvision.Id.ToString());
//                         bool isMonthly = true;
//                         DateTime start, end;
//                         start = DateTime.UtcNow;
//                         end = DateTime.UtcNow.AddDays(30);
//                         if (provision.IsPayed == true)
//                         {
//                             var split = provision.ProductId.Split('.');
//                             isMonthly = (split[3] == "m") ? true : false;
//                             if (isMonthly == true)
//                             {
//                                 start = DateTime.UtcNow;
//                                 end = DateTime.UtcNow.AddDays(30);
//                             }
//                             else
//                             {
//                                 start = DateTime.UtcNow;
//                                 end = DateTime.UtcNow.AddYears(1);
//                             }
//                         }

//                         if (userProvider != null || userProviderProvision != null)
//                         {
//                             var upid = (userProvider != null) ? userProvider.Id :
//                                 (userProviderProvision != null) ? userProviderProvision.Id : Guid.Empty;
//                             if (upid != Guid.Empty)
//                             {
//                                 var up = wepdb.UserProviders.FirstOrDefault(w => w.Id == upid);
//                                 userProviderId = up.Id;
//                                 up.AppId = user.AppId;
//                                 up.OrgId = provision.OrgId;
//                                 up.ModifiedDate = DateTime.UtcNow;
//                                 up.ModifiedDate = DateTime.UtcNow;
//                                 up.EmailAddress = emailId.ToString().ToLower();
//                                 up.IsProvisioned = provision.IsProvisioned;
//                                 up.IsPayed = provision.IsPayed;
//                                 up.IsFree = provision.IsFree;
//                                 up.ProvisionId = provision.Id;
//                                 up.IsAuthenticated = true;
//                                 //userProvider.AccountId = 
//                                 up.IsActive = true;
//                                 up.Source = provision.Source ?? 0;
//                                 up.PurchaseId = provision.PurchaseId ?? "";
//                                 up.PurchaseState = provision.PurchaseState ?? 0;
//                                 up.ProductId = provision.ProductId ?? "";
//                                 up.IsRelogginRequired = false;
//                                 up.Priority = 100;
//                                 wepdb.SaveChanges();
//                                 //foreach (var identifier in identifierList)
//                                 //{
//                                 //    var userProviderIdentifierExist = wepdb.UserProviderIdentifiers.FirstOrDefault(w => w.UserProviderId == userProvider.Id && w.IdentifierId==identifier.Id);
//                                 //    if (identifier.Value != null && identifier.Value != "")
//                                 //    {
//                                 //        userProviderIdentifierExist.IdentifierId = identifier.Id;
//                                 //        userProviderIdentifierExist.UserProviderId = userProvider.Id;
//                                 //        userProviderIdentifierExist.Value = identifier.Value;
//                                 //        userProviderIdentifierExist.IsActive = true;
//                                 //        userProviderIdentifierExist.ModifiedDate = DateTime.UtcNow;
//                                 //        wepdb.SaveChanges();
//                                 //    }
//                                 //}
//                                 if (provider.Name == "Pipedrive ")
//                                 {
//                                     if (identifierList.Select(w => w.Name).Contains("pdcompany_domain"))
//                                     {
//                                         var companyInstanceName = "pdcompany_domain";
//                                         var companyDomain = identifierList.Where(w => w.Name == companyInstanceName)
//                                             .FirstOrDefault().Value;
//                                         var instanceString = "pdinstanceurl";
//                                         var instaceUrl = "https://" + companyDomain + ".pipedrive.com";
//                                         identifierList.First(w => w.Name == instanceString).Value = instaceUrl;
//                                     }
//                                 }

//                                 try
//                                 {
//                                     if (provider.Id == CommonData.SalesforceProviderId)
//                                     {
//                                         if (identifierList.Any(w => w.Name == "sflightningurl") &&
//                                             !string.IsNullOrEmpty(
//                                                 identifierList.First(x => x.Name == "sfinstanceurl").Value))
//                                         {
//                                             if (string.IsNullOrEmpty(identifierList.First(w => w.Name == "sflightningurl")
//                                                     .Value))
//                                             {
//                                                 string instanceUrl = identifierList.First(x => x.Name == "sfinstanceurl")
//                                                     .Value;
//                                                 string instance = instanceUrl.Substring(instanceUrl.IndexOf("https://") + 8,
//                                                     instanceUrl.IndexOf(".my.") - 8);
//                                                 string lightningUrl = $"https://{instance}.lightning.force.com";
//                                                 identifierList.First(w => w.Name == "sflightningurl").Value = lightningUrl;
//                                             }
//                                         }
//                                     }
//                                 }
//                                 catch (Exception ex)
//                                 {
//                                     Console.WriteLine(ex.ToString());
//                                 }

//                                 wepdb.UserProviderIdentifiers.Where(w => w.UserProviderId == up.Id).ToList()
//                                     .ForEach(i => i.IsActive = false);
//                                 foreach (var identifier in identifierList)
//                                 {
//                                     try
//                                     {
//                                         //var identifierExist = weddb.Identifiers.FirstOrDefault(w => w.Id.ToString() == UPIdentifierKeyValue.Key && w.ProviderId == providerId);
//                                         if (identifier.Value != null && identifier.Value != "")
//                                         {
//                                             var upIdentifiers = new UserProviderIdentifier();
//                                             upIdentifiers.Id = Guid.NewGuid();
//                                             upIdentifiers.IdentifierId = identifier.Id;
//                                             upIdentifiers.UserProviderId = up.Id;
//                                             upIdentifiers.Value = identifier.Value;
//                                             upIdentifiers.IsActive = true;
//                                             upIdentifiers.CreatedDate = DateTime.UtcNow;
//                                             upIdentifiers.ModifiedDate = DateTime.UtcNow;
//                                             wepdb.UserProviderIdentifiers.Add(upIdentifiers);
//                                             wepdb.SaveChanges();
//                                         }
//                                     }
//                                     catch (Exception ex)
//                                     {
//                                         continue;
//                                         // throw;
//                                     }
//                                 }

//                                 var exsitingProvision = weodb.Provisions.FirstOrDefault(w => w.Id == provision.Id);
//                                 exsitingProvision.IsRedeemed = true;
//                                 exsitingProvision.UserProviderId = up.Id;
//                                 exsitingProvision.ProviderId = providerId;
//                                 exsitingProvision.EmailAddress = emailId.ToLower();
//                                 exsitingProvision.IsTrial = Convert.ToBoolean(provision.IsTrial);
//                                 weodb.SaveChanges();
//                             }
//                         }
//                         else
//                         {
//                             var newUserProvider = new UserProvider();
//                             newUserProvider.Id = Guid.NewGuid();
//                             newUserProvider.UserId = userId;
//                             newUserProvider.AppId = user.AppId;
//                             newUserProvider.OrgId = provision.OrgId;
//                             newUserProvider.ProviderId = providerId;
//                             newUserProvider.EmailAddress = emailId.ToLower();
//                             newUserProvider.IsAuthenticated = true;
//                             newUserProvider.IsActive = true;
//                             newUserProvider.Code = code ?? "";
//                             newUserProvider.ActiveFrom = DateTime.UtcNow;
//                             newUserProvider.ActiveTill = DateTime.UtcNow.AddYears(1);
//                             newUserProvider.IsProvisioned = provision.IsProvisioned;
//                             newUserProvider.ProvisionId = provision.Id;
//                             newUserProvider.IsFree = provision.IsFree;
//                             newUserProvider.IsPayed = provision.IsPayed;
//                             // newUserProvider.AccountId = token.id;
//                             newUserProvider.IsFullSyncDone = false;
//                             newUserProvider.Source = provision.Source ?? 0;
//                             newUserProvider.PurchaseId = provision.PurchaseId ?? "";
//                             newUserProvider.PurchaseState = provision.PurchaseState ?? 0;
//                             newUserProvider.ProductId = provision.ProductId ?? "";
//                             newUserProvider.CreatedDate = DateTime.UtcNow;
//                             newUserProvider.ModifiedDate = DateTime.UtcNow;
//                             newUserProvider.IsRelogginRequired = false;
//                             newUserProvider.Priority = 100;
//                             //newUserProvider.InstanceUrl = token.instance_url;
//                             int trialDays = 30;
//                             //if (provision.IsTrial == true)
//                             //{
//                             //    newUserProvider.IsTrial = true;
//                             //    newUserProvider.TrialStart = DateTime.UtcNow;
//                             //    newUserProvider.TrialEnd = DateTime.UtcNow.AddDays(trialDays);
//                             //}
//                             //else
//                             //{
//                             newUserProvider.IsTrial = false;
//                             //}
//                             wepdb.UserProviders.Add(newUserProvider);
//                             wepdb.SaveChanges();
//                             if (provider.Name == "Pipedrive ")
//                             {
//                                 if (identifierList.Select(w => w.Name).Contains("pdcompany_domain"))
//                                 {
//                                     var companyInstanceName = "pdcompany_domain";
//                                     var companyDomain = identifierList.Where(w => w.Name == companyInstanceName)
//                                         .FirstOrDefault().Value;
//                                     var instanceString = "pdinstanceurl";
//                                     var instaceUrl = "https://" + companyDomain + ".pipedrive.com";
//                                     identifierList.First(w => w.Name == instanceString).Value = instaceUrl;
//                                 }
//                             }

//                             try
//                             {
//                                 if (provider.Id == CommonData.SalesforceProviderId)
//                                 {
//                                     if (identifierList.Any(w => w.Name == "sflightningurl") &&
//                                         !string.IsNullOrEmpty(identifierList.First(x => x.Name == "sfinstanceurl").Value))
//                                     {
//                                         if (string.IsNullOrEmpty(
//                                                 identifierList.First(w => w.Name == "sflightningurl").Value))
//                                         {
//                                             string instanceUrl = identifierList.First(x => x.Name == "sfinstanceurl").Value;
//                                             string instance = instanceUrl.Substring(instanceUrl.IndexOf("https://") + 8,
//                                                 instanceUrl.IndexOf(".my.") - 8);
//                                             string lightningUrl = $"https://{instance}.lightning.force.com";
//                                             identifierList.First(w => w.Name == "sflightningurl").Value = lightningUrl;
//                                         }
//                                     }
//                                 }
//                             }
//                             catch (Exception ex)
//                             {
//                                 Console.WriteLine(ex.ToString());
//                             }

//                             foreach (var identifier in identifierList)
//                             {
//                                 try
//                                 {
//                                     if (identifier.Value != null && identifier.Value != "")
//                                     {
//                                         var upIdentifiers = new UserProviderIdentifier();
//                                         upIdentifiers.Id = Guid.NewGuid();
//                                         upIdentifiers.IdentifierId = identifier.Id;
//                                         upIdentifiers.UserProviderId = newUserProvider.Id;
//                                         upIdentifiers.Value = identifier.Value;
//                                         upIdentifiers.IsActive = true;
//                                         upIdentifiers.CreatedDate = DateTime.UtcNow;
//                                         upIdentifiers.ModifiedDate = DateTime.UtcNow;
//                                         wepdb.UserProviderIdentifiers.Add(upIdentifiers);
//                                         wepdb.SaveChanges();
//                                     }
//                                 }
//                                 catch (Exception ex)
//                                 {
//                                     continue;
//                                     // throw;
//                                 }
//                             }

//                             wepdb.SaveChanges();
//                             var exsitingProvision = weodb.Provisions.FirstOrDefault(w => w.Id == provision.Id);
//                             exsitingProvision.IsRedeemed = true;
//                             exsitingProvision.UserProviderId = newUserProvider.Id;
//                             exsitingProvision.ProviderId = providerId;
//                             exsitingProvision.EmailAddress = emailId.ToLower();
//                             exsitingProvision.IsTrial = Convert.ToBoolean(provision.IsTrial);
//                             weodb.SaveChanges();
//                             userProviderId = newUserProvider.Id;

//                             if (user.ChargebeeCustomerId != null)
//                             {
//                                 //try
//                                 //{
//                                 //    var msg = provider.Name + " LoggedIn-" + newUserProvider.EmailAddress;
//                                 //    ApiConfig.Configure("1Page", "live_Tf3jmCoHTjMZ1cdzlEzuLRK8qsQ6cudxaF");
//                                 //    EntityResult result = Customer.Retrieve(user.ChargebeeCustomerId).Request();
//                                 //    Customer customer = result.Customer;
//                                 //    if (customer != null)
//                                 //    {
//                                 //        EntityResult result1 = Comment.Create()
//                                 //           .EntityId(user.ChargebeeCustomerId)
//                                 //           .EntityType(ChargeBee.Models.Enums.EntityTypeEnum.Customer)
//                                 //           .Notes(msg)
//                                 //           .Request();
//                                 //    }
//                                 //}
//                                 //catch (Exception ex)
//                                 //{
//                                 //}
//                             }
//                         }

//                     }
//                     catch (Exception ex)
//                     {

//                     }
//                     var userEmailExist =
//                         wepdb.UserEmails.FirstOrDefault(w => w.Email.ToLower() == emailId && w.UserId == userId);
//                     if (userEmailExist == null && emailId != "")
//                     {
//                         var userEmail = new UserEmail();
//                         userEmail.Id = Guid.NewGuid();
//                         userEmail.Email = emailId ?? "";
//                         userEmail.IsActive = true;
//                         userEmail.UserId = userId;
//                         userEmail.IsPersonal = true;
//                         userEmail.CreatedDate = DateTime.UtcNow;
//                         userEmail.ModifiedDate = DateTime.UtcNow;
//                         userEmail.IsEmailValidated = true;
//                         userEmail.IsAccessible = true;
//                         userEmail.IsMultiUser = true;
//                         userEmail.IsGroupEmail = false;
//                         userEmail.Status = 1;
//                         userEmail.IsAddedByUser = true;
//                         userEmail.IsAcceptedByUser = false;
//                         wepdb.UserEmails.Add(userEmail);
//                     }
//                     else
//                     {
//                         userEmailExist.IsEmailValidated = true;
//                     }

//                     var checkForCalendarSettingId = Guid.Parse("1D9BC314-786C-40E4-9FE4-769860C58F12");
//                     var userSettings = wepdb.UserSettings
//                         .Where(w => w.UserId == userId && w.SettingId == checkForCalendarSettingId).FirstOrDefault();
//                     if (userSettings != null)
//                     {
//                         userSettings.Value = "1";
//                     }

//                     wepdb.SaveChanges();
//                     if (provision.IsProvisioned == true) // && provision.OrgId != CommonData.WhatElseOrgId)
//                     {
//                         var orgUserExist = weodb.OrgDirectories.FirstOrDefault(w =>
//                             w.OrgId == provision.OrgId && w.UserId == userId.ToString());
//                         if (orgUserExist == null)
//                         {
//                             var orgUser = new OrgUser();
//                             orgUser.Id = Guid.NewGuid();
//                             orgUser.IsActive = true;
//                             orgUser.CreatedDate = DateTime.UtcNow;
//                             // orgUser.DeactivatedOn =;
//                             orgUser.ActivatedOn = DateTime.UtcNow;
//                             orgUser.ModifiedDate = DateTime.UtcNow;
//                             orgUser.OrgId = provision.OrgId;
//                             orgUser.UserId = userId;
//                             orgUser.IsAdmin = false;
//                             weodb.OrgUsers.Add(orgUser);
//                             weodb.SaveChanges();

//                             var orgDirectory = new OrgDirectory();
//                             orgDirectory.Id = Guid.NewGuid();
//                             orgDirectory.OrgId = provision.OrgId;
//                             orgDirectory.CreatedDate = DateTime.UtcNow;
//                             orgDirectory.ModifiedDate = DateTime.UtcNow;
//                             orgDirectory.IsActive = true;
//                             orgDirectory.FirstName = user.FirstName ?? "";
//                             orgDirectory.MiddleName = user.MiddleName ?? "";
//                             orgDirectory.LastName = user.LastName ?? "";
//                             orgDirectory.Email = user.Email ?? "";
//                             orgDirectory.SanitizedNumber = "";
//                             orgDirectory.ProvidedNumber = "";
//                             orgDirectory.UserId = user.Id.ToString();
//                             orgDirectory.CountryId = user.CountryId;
//                             weodb.OrgDirectories.Add(orgDirectory);
//                             weodb.SaveChanges();
//                             weodb.Provisions.FirstOrDefault(w => w.Id == provision.Id).OrgDirectoryId = orgDirectory.Id;
//                             weodb.SaveChanges();
//                         }
//                     }

//                     wepdb.SaveChanges();
//                     weodb.SaveChanges();
//                     weadb.SaveChanges();
//                     //}
//                 }
//                 else
//                 {
//                     return new Tuple<int, Guid>(2, userProviderId);
//                 }

//                 return new Tuple<int, Guid>(1, userProviderId);
//             }

//             var isFirstName = identifierList.Any(w => w.Name.Contains("firstname") && w.Value != null && w.Value != "");
//             if (isFirstName)
//             {
//                 firstName = identifierList.First(w => w.Name.Contains("firstname") && w.Value != null && w.Value != "")
//                     .Value.ToString();
//             }

//             var isLastName = identifierList.Any(w => w.Name.Contains("lastname") && w.Value != null && w.Value != "");
//             if (isLastName)
//             {
//                 lasttName = identifierList.First(w => w.Name.Contains("lastname") && w.Value != null && w.Value != "")
//                     .Value.ToString();
//             }

//             var isProfileId = identifierList.Any(w => w.Name.Contains("profileid") && w.Value != null && w.Value != "");
//             if (isProfileId)
//             {
//                 profileId = identifierList.First(w => w.Name.Contains("profileid") && w.Value != null && w.Value != "")
//                     .Value.ToString();
//             }

//             var isDisplayImage =
//                 identifierList.Any(w => w.Name.Contains("displayimage") && w.Value != null && w.Value != "");
//             if (isDisplayImage)
//             {
//                 displayImage = identifierList
//                     .First(w => w.Name.Contains("displayimage") && w.Value != null && w.Value != "").Value.ToString();
//             }

//             var isCountry = identifierList.Any(w => w.Name.Contains("country") && w.Value != null && w.Value != "");
//             if (isCountry)
//             {
//                 country = identifierList.First(w => w.Name.Contains("country") && w.Value != null && w.Value != "")
//                     .Value.ToString();
//             }

//             //await service.Upload(user.Id, stream);

//             PropertyModels.AuthModels.SocialReturnModel socialReturnModel =
//                 new PropertyModels.AuthModels.SocialReturnModel();
//             socialReturnModel.DisplayImage = displayImage;
//             socialReturnModel.EmaiId = emailId;
//             socialReturnModel.FirstName = firstName;
//             socialReturnModel.LastName = lasttName;
//             socialReturnModel.ProfileId = profileId;
//             socialReturnModel.Country = country;

//             Guid dId = Guid.Parse(deviceId);
//             Guid dtId = Guid.Parse(deviceTypeId);
//             // call serverzone api
//             authUserModel.AppId = AppId;
//             authUserModel.CountryId = country;
//             authUserModel.EmailAddress = emailId;
//             authUserModel.PhoneNumber = "";
//             authUserModel.Password = deviceId.ToString().ToLower();
//             authUserModel.Coupon = "";
//             authUserModel.IsPreAuthenticated = true;
//             authUserModel.FirstName = firstName;
//             authUserModel.LastName = lasttName;
//             authUserModel.LoginFlowId = providerId;
//             authUserModel.DeviceId = dId;
//             authUserModel.DeviceType = dtId;
//             var json = JsonConvert.SerializeObject(authUserModel);
//             postZoneUrl = postZoneUrl + "06E12CFF";

//             var client = new RestSharp.RestClient(postZoneUrl);
//             var request = new RestRequest();
//             request.AddHeader("content-type", "application/json;charset=UTF-8");
//             //request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
//             request.AddJsonBody(json);
//             var response = await client.PostAsync(request);
//             PropertyModels.AuthModels.PreloginReturnModel2 Djson1 =
//                 JsonConvert.DeserializeObject<PropertyModels.AuthModels.PreloginReturnModel2>(response.Content);

//             // call validate api
//             validationModel.IsPreAuthenticated = true;
//             validationModel.EmailAddress = emailId;
//             validationModel.AppId = AppId;
//             validationModel.DeviceId = Guid.Parse(deviceId);
//             validationModel.DeviceTypeId = Guid.Parse(deviceTypeId);
//             validationModel.OTP = deviceId.ToString().ToLower();
//             validationModel.LanguageId = "English (US)";

//             var validateJson = JsonConvert.SerializeObject(validationModel);
//             authUrl = authUrl + "165C8F0E";

//             var client2 = new RestSharp.RestClient(authUrl);
//             var request2 = new RestRequest();
//             request2.AddHeader("content-type", "application/json;charset=UTF-8");
//             request2.AddJsonBody(validateJson);
//             var response2 = await client2.PostAsync(request2);
//             PropertyModels.AuthModels.ValidateReturnModel Djson2 =
//                 JsonConvert.DeserializeObject<PropertyModels.AuthModels.ValidateReturnModel>(response2.Content);
//             socialReturnModel.IsRegiestered = Djson2.UserStatusModel.Registration;
//             socialReturnModel.Token = Djson2.TokenId.ToString();

//             string accountname = "wcst01";

//             string accesskey =
//                 "****************************************************************************************";

//             try
//             {
//                 // StorageCredentials creden = new StorageCredentials(accountname, accesskey);
//                 // BlobServiceClient blobServiceClient = new BlobServiceClient(connectionString);
//                 //
//                 //
//                 // CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
//                 //
//                 // CloudBlobClient blobClient = acc.CreateCloudBlobClient();
//                 //
//                 // CloudBlobContainer cont = blobClient.GetContainerReference("profile");
//                 //
//                 // cont.CreateIfNotExists();
//                 // // cont.CreateAsync()
//                 //
//                 // cont.SetPermissions(new BlobContainerPermissions
//                 // {
//                 //     PublicAccess = BlobContainerPublicAccessType.Blob
//                 // });
//                 // string ImageName = Djson2.UserId + ".png";
//                 // WebClient wc = new WebClient();
//                 // MemoryStream stream = new MemoryStream(wc.DownloadData(displayImage));
//                 // CloudBlockBlob cblob = cont.GetBlockBlobReference(ImageName);
//                 // cblob.UploadFromStream(stream);

//                 // Create a BlobServiceClient instance
//                 string connectionString =
//                     $"DefaultEndpointsProtocol=https;AccountName={accountname};AccountKey={accesskey};EndpointSuffix=core.windows.net";

//                 BlobServiceClient blobServiceClient = new BlobServiceClient(connectionString);
//                 BlobContainerClient containerClient = blobServiceClient.GetBlobContainerClient("profile");
//                 containerClient.CreateIfNotExists();
//                 containerClient.SetAccessPolicy(Azure.Storage.Blobs.Models.PublicAccessType.Blob);
//                 string imageName = Djson2.UserId + ".png";
//                 using (WebClient wc = new WebClient())
//                 {
//                     MemoryStream stream = new MemoryStream(wc.DownloadData(displayImage));
//                     BlobClient blobClient = containerClient.GetBlobClient(imageName);
//                     blobClient.Upload(stream, true);
//                 }
//             }
//             catch (Exception ex)
//             {
//             }

//             // call signalr and onesignal


//             var appInfo = weadb.Apps.FirstOrDefault(w => w.Id == AppId && w.IsActive);
//             var androidOneSignalId = appInfo.AndroidOneSignalId;
//             var androidOneSignalKey = appInfo.AndroidOneSignalApiKey;
//             var IosOneSignalId = appInfo.IosoneSignalId;
//             var IosOneSignalKey = appInfo.IosoneSignalApiKey;
//             var WebOneSignalId = appInfo.WebOneSignalId;
//             var WebOneSignalKey = appInfo.WebOneSignalApiKey;

//             var authMessage = "You are authenticated" + "\\n" + "Tap to proceed!";
//             if (AppId == CommonData.WEAppId)
//             {
//                 string res = PushServices.DevicePushForLogin(androidOneSignalId, androidOneSignalKey, "", deviceId,
//                     authMessage, appInfo.Name, userId: Djson2.UserId.ToString(), eventType: 34, tempAuthUrl: tempUrl,
//                     emailId: emailId);
//             }
//             else if (AppId == CommonData.OPAppId)
//             {
//                 NotifyPayload payload = new NotifyPayload()
//                 {
//                     NotificationId = Guid.NewGuid().ToString(),
//                     UserId = Djson2.UserId.ToString(),
//                     DeviceId = deviceId,
//                     EmailId = emailId,
//                     EventType = 34,
//                     AuthUrl = tempUrl
//                 };
//                 string res = PushServices.SignalPush(androidOneSignalId, androidOneSignalKey, deviceId, authMessage,
//                     appInfo.Name, payload);
//             }
//         }
//         catch (Exception ex)
//         {
//             TelegramService.SendMessageToTestBot(ex.ToString());
//             TelegramService.SendMessageToTestBot(ex.StackTrace.ToString());
//             return new Tuple<int, Guid>(3, userProviderId);
//             //return BadRequest(ex.Source);
//         }

//         return new Tuple<int, Guid>(1, userProviderId);
//     }


//     public string FindAndReplaceIndentifiers(string finalURL)
//     {
//         foreach (var item in identifierList)
//         {
//             if (item.Value != "" && item.Value != null)
//             {
//                 finalURL = finalURL.Replace("{" + item.Name + "}", item.Value);
//             }
//         }

//         foreach (var orgIdentifier in orgProviderIdentifierList)
//         {
//             var identifierName = weddb.Identifiers.FirstOrDefault(w => w.Id == orgIdentifier.IdentifierId).Name;
//             finalURL = finalURL.Replace("{" + identifierName + "}", orgIdentifier.Value);
//         }

//         finalURL = finalURL.Replace("\r\n", "");
//         return finalURL;
//     }

//     public async Task<bool> BoxPostClient(string finalURL, Url providerUrl, string dataToReplace)
//     {
//         try
//         {
//             //if (finalURL.Contains("?") == true)
//             //{
//             var urlPart1 = "";
//             var urlPart2 = "";
//             if (finalURL.Contains("?") == true)
//             {
//                 urlPart1 = finalURL.Split('?')[0];
//                 urlPart2 = finalURL.Split('?')[1];
//             }
//             else if (finalURL.Contains("|") == true)
//             {
//                 urlPart1 = finalURL.Split('|')[0];
//                 ;
//                 urlPart2 = finalURL.Split('|')[1];
//             }
//             else
//             {
//                 urlPart1 = finalURL;
//             }

//             HttpClient client = new HttpClient();
//             //  HttpContent content = new StringContent(String.Format(urlPart2));
//             HttpContent content = new StringContent(String.Format(urlPart2), Encoding.UTF8);
//             //content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
//             foreach (var item in headersList)
//             {
//                 if (item.Value.Contains('{'))
//                 {
//                     item.Value = FindAndReplaceIndentifiers(item.Value);
//                 }

//                 if (item.Name == "Content-Type")
//                 {
//                     content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(item.Value);
//                 }
//                 else if (item.Name == "Authorization")
//                 {
//                     client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic ", item.Value);
//                 }
//                 else
//                 {
//                     content.Headers.Add(item.Name, item.Value);
//                 }
//             }

//             //content.Headers.Add("grant_type", "authorization_code");
//             //if (providerUrl.NeedsData)
//             //{
//             //    content = new StringContent(dataToReplace.ToString(), Encoding.UTF8, "application/json");
//             //}
//             //  content.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");
//             using (HttpResponseMessage response1 = await client.PostAsync(urlPart1, content))
//             {
//                 if (response1.IsSuccessStatusCode)
//                 {
//                     try
//                     {
//                         var json = await response1.Content.ReadAsStringAsync();
//                         JToken jtoken = JToken.Parse(json);
//                         var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
//                         foreach (var outputIdentifier in outputIndentifiers)
//                         {
//                             var keyData = outputIdentifier.Split('|');
//                             var selectedToken = jtoken.SelectToken(keyData[1]);
//                             if (selectedToken != null)
//                             {
//                                 string tokenValue = selectedToken.Value<string>();
//                                 foreach (var identifier in identifierList)
//                                 {
//                                     if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                                     {
//                                         identifier.Value = tokenValue;
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                     catch (Exception ex)
//                     {
//                         return false;
//                     }
//                 }
//                 else
//                 {
//                     PropertyModels.DataModels.ErrorMailModel errorMailModel =
//                         new PropertyModels.DataModels.ErrorMailModel();
//                     errorMailModel.Url = "server" + finalURL;
//                     errorMailModel.Count = 0;
//                     errorMailModel.CreatedDate = DateTime.UtcNow;
//                     errorMailModel.ErrorCode = response1.StatusCode.ToString();
//                     errorMailModel.ErrorMessage = response1.RequestMessage.Content.ToString();
//                     errorMailModel.UserProviderId = Guid.Empty;
//                     errorMailModel.ProvisionId = Guid.Parse(appProvision);
//                     errorMailModel.Payload = dataToReplace ?? "";
//                     errorMailModel.Email = "Didn't found emailid";
//                     var mail = EmailServices.SendGridErrorMail(errorMailModel);
//                 }
//             }
//         }
//         catch (Exception ex)
//         {
//             return false;
//         }

//         return true;
//     }

//     public async Task<bool> MiOfertaPostClient(string finalURL, Url providerUrl, string dataToReplace)
//     {
//         try
//         {
//             //if (finalURL.Contains("?") == true)
//             //{
//             var urlPart1 = "";
//             var urlPart2 = "";
//             if (finalURL.Contains("?") == true)
//             {
//                 urlPart1 = finalURL.Split('?')[0];
//                 urlPart2 = finalURL.Split('?')[1];
//             }
//             else if (finalURL.Contains("|") == true)
//             {
//                 urlPart1 = finalURL.Split('|')[0];
//                 ;
//                 urlPart2 = finalURL.Split('|')[1];
//             }
//             else
//             {
//                 urlPart1 = finalURL;
//             }

//             HttpClient client = new HttpClient();
//             //  HttpContent content = new StringContent(String.Format(urlPart2));
//             HttpContent content = new StringContent(String.Format(urlPart2), Encoding.UTF8);
//             //content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/x-www-form-urlencoded");
//             foreach (var item in headersList)
//             {
//                 if (item.Value.Contains('{'))
//                 {
//                     item.Value = FindAndReplaceIndentifiers(item.Value);
//                 }

//                 if (item.Name == "Content-Type")
//                 {
//                     content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(item.Value);
//                 }
//                 else if (item.Name == "Authorization")
//                 {
//                     client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", item.Value);
//                 }
//                 else
//                 {
//                     content.Headers.Add(item.Name, item.Value);
//                 }
//             }

//             //content.Headers.Add("grant_type", "authorization_code");
//             //if (providerUrl.NeedsData)
//             //{
//             //    content = new StringContent(dataToReplace.ToString(), Encoding.UTF8, "application/json");
//             //}
//             // content.Headers.ContentType = new MediaTypeHeaderValue("multipart/form-data");
//             using (HttpResponseMessage response1 = await client.PostAsync(urlPart1, content))
//             {
//                 if (response1.IsSuccessStatusCode)
//                 {
//                     try
//                     {
//                         var json = await response1.Content.ReadAsStringAsync();
//                         JToken jtoken = JToken.Parse(json);
//                         var outputIndentifiers = providerUrl.Identifier.Replace("|J-", "|").Split(',');
//                         foreach (var outputIdentifier in outputIndentifiers)
//                         {
//                             var keyData = outputIdentifier.Split('|');
//                             var selectedToken = jtoken.SelectToken(keyData[1]);
//                             if (selectedToken != null)
//                             {
//                                 string tokenValue = selectedToken.Value<string>();
//                                 foreach (var identifier in identifierList)
//                                 {
//                                     if (keyData[0].ToLower() == identifier.Name.ToLower() && identifier.Value == null)
//                                     {
//                                         identifier.Value = tokenValue;
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                     catch (Exception ex)
//                     {
//                         return false;
//                     }
//                 }
//             }
//         }
//         catch (Exception ex)
//         {
//             return false;
//         }

//         return true;
//     }
// }