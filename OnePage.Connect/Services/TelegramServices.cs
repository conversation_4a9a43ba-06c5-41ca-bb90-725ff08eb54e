using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web;

namespace OnePage.Connect.Services;

public static class TelegramService
{
    static readonly string gaganBotToken = "7136426687:AAGtnt4pTePe_k_Ny3mTsLDYRk8uRAxz2Oc";
    static readonly string testBottoken = "5277113211:AAGCRaM-0ZL25CqTOfzcPMfwlMo6V36FMBI";
    static readonly string prakashBotToken = "7168330395:AAH7KtPIyL5R6MnfpD2jdHvlnvMx-9D79m8";
    static readonly string rahulBotToken = "6566035574:AAE6cp3oPAU6qq71dmhMHYQD9BmXXMhl1Gc";


    static readonly string token = "1979617440:AAE_q-2irwpi5IdoKIersDaruxOa81uIkFk";

    // static readonly string token = "5277113211:AAGCRaM-0ZL25CqTOfzcPMfwlMo6V36FMBI";
    static readonly string chatId = "65294787";
    static readonly string gaganChatId = "7523495881";
    static readonly string testChatId = "184762097";
    static readonly string savithaChatId = "5254402108";

    static readonly string rahulChatId = "645581919";

    // static readonly string chatId = "184762097";
    static readonly string prakashChatId = "796226735";

    public static string SendMessage(string message)
    {
        string retval = string.Empty;
        string retval2 = string.Empty;
        string url = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatId}&text={message}";
        string url2 = $"https://api.telegram.org/bot{token}/sendMessage?chat_id={savithaChatId}&text={message}";
        using (var webClient = new WebClient())
        {
            retval = webClient.DownloadString(url);
            retval2 = webClient.DownloadString(url2);
        }

        return retval;
    }

    public static string SendMessageToTestBot(string message)
    {
        try
        {
            string retval = string.Empty;
            //string url = $"https://api.telegram.org/bot{gaganBotToken}/sendMessage?chat_id={gaganChatId}&text={message}";

            //using (var webClient = new WebClient())
            //{
            //    retval = webClient.DownloadString(url);
            //}

            string url = $"https://api.telegram.org/bot{gaganBotToken}/sendMessage?chat_id={gaganChatId}&text={message}";

            using (var webClient = new WebClient())
            {
                retval = webClient.DownloadString(url);

            }
            ////string url = $"https://api.telegram.org/bot{prakashBotToken}/sendMessage?chat_id={prakashChatId}&text={message}";

            ////using (var webClient = new WebClient())
            ////{
            ////    retval = webClient.DownloadString(url);
            ////}


            return retval;

        }
        catch (Exception ex)
        {
            return "";
        }
    }

    public static string SendMessageToTestBot2(string message)
    {
        try
        {
            string retval = string.Empty;
            string url = $"https://api.telegram.org/bot{gaganBotToken}/sendMessage?chat_id={gaganChatId}&text={message}";

            using (var webClient = new WebClient())
            {
                retval = webClient.DownloadString(url);
            }

            return retval;
        }
        catch (Exception ex)
        {
            return "";
        }
    }

    public static string SendMessageToRahulTestBot(string message)
    {
        string retval = string.Empty;
        //string url = $"https://api.telegram.org/bot{rahulBotToken}/sendMessage?chat_id={rahulChatId}&text={message}";

        //using (var webClient = new WebClient())
        //{
        //    retval = webClient.DownloadString(url);
        //}

        return retval;
    }
}