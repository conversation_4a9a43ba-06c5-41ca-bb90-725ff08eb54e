
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using Azure.Core;
using OnePage.Common;
using OnePage.Connect.Context;
using OnePage.Connect.Migrations;
using OnePage.Models;
using OnePage.Services;
using System.Linq;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;


namespace OnePage.Connect.Services;
    public class ProviderService
    {
        WEDataEntitiesDBContext weddb;
        WEPeopleEntitiesDBContext wepdb;
        WEOrgEntitiesDBContext weodb;

        RestClient restClient;
        HttpClient httpClient;
        //DBService db;
        CancellationTokenSource tokenSource;

        //public ProviderService(WEDataEntitiesDBContext dataEntities, WEPeopleEntitiesDBContext peopleEntities, WEOrgEntitiesDBContext orgEntities)
        //{
        //    tokenSource = new CancellationTokenSource();
        //    restClient = RestClient.Instance;
        //    httpClient = new HttpClient()
        //    {
        //        Timeout = TimeSpan.FromSeconds(360)
        //    };

        //     weddb = dataEntities;
        //    wepdb = peopleEntities;
        //    weodb = orgEntities;
        //}

        public ProviderService(WEDataEntitiesDBContext dataEntities, WEPeopleEntitiesDBContext peopleEntities, WEOrgEntitiesDBContext orgEntities)
        {
            tokenSource = new CancellationTokenSource();
            restClient = RestClient.Instance;
            httpClient = new HttpClient()
            {
                Timeout = TimeSpan.FromSeconds(360)
            };

            weddb = dataEntities;
            wepdb = peopleEntities;
            weodb = orgEntities;
        }

        private HttpRequestMessage PrepareRequest(HttpMethod method, string url, object payload, string content = "", string mediaType = "application/json", string contentType = "", bool shouldSetContent = false)
        {
            var uri = PrepareUri(url);
            var request = new HttpRequestMessage(method, uri);

            if ((contentType == "application/x-www-url-form-urlencoded" || contentType == "application/x-www-form-urlencoded") && !string.IsNullOrEmpty(content))
            {
                List<KeyValuePair<string, string>> keyPairs = new List<KeyValuePair<string, string>>();
                List<string> pairs = content.Split('&').ToList();
                char[] limiter = new char[] { '=' };
                foreach (var pair in pairs.ToList())
                {
                    if (pair.Contains('='))
                    {
                        var splitted = pair.Split(limiter, 2);
                        keyPairs.Add(new KeyValuePair<string, string>(splitted[0], splitted[1]));
                    }
                }
                //string urlContent = HttpUtility.UrlEncode(content, Encoding.UTF8);
                //HttpContent httpContent = new StringContent(String.Format(urlContent), Encoding.UTF8, contentType);
                HttpContent httpContent = new FormUrlEncodedContent(keyPairs);
                request.Content = httpContent;
            }
            else if (!string.IsNullOrEmpty(content) || shouldSetContent == true)
            {
                HttpContent httpContent = new StringContent(String.Format(content), Encoding.UTF8, contentType);
                request.Content = httpContent;
                //request.Content = new StringContent(content, Encoding.UTF8, contentType);
            }
            if (payload != null)
            {
                if (mediaType == "application/json" && contentType == "")
                {
                    string json = "";
                    if (payload is string)
                        json = payload as string;
                    else
                        json = System.Text.Json.JsonSerializer.Serialize(payload);
                    request.Content = new StringContent(json, Encoding.UTF8, mediaType);
                }
            }
            return request;
        }
        private Uri PrepareUri(string url) => new Uri(url);

        private T GetValue<T>(String value)
        {
            return (T)Convert.ChangeType(value, typeof(T));
        }

        HttpRequestMessage AddHeadersToMessage(HttpRequestMessage request, List<Header> HeaderList, List<PropertyModels.ContactDataModel.IdentifiersModel> Identifiers)
        {
            foreach (var item in HeaderList)
            {
                string header = item.Prefix;
                if (header.Contains("{"))
                    header = ReplaceIdentifiersInString(Identifiers, header);
                try
                {
                    request.Headers.TryAddWithoutValidation(item.Name, header);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                    // LoggerService.Instance.TrackError(ex);
                }
            }
            return request;
        }

        public string ReplaceIdentifiersInString(List<PropertyModels.ContactDataModel.IdentifiersModel> Identifiers, string url = "", bool overrideNull = false)
        {
            string output = url;
            foreach (var identifier in Identifiers)
            {
                if (identifier.Value != null && identifier.Value != "")
                    output = output.Replace("{" + identifier.Name + "}", identifier.Value);
                if (identifier.Value == "" && overrideNull == true)
                    output = output.Replace("{" + identifier.Name + "}", identifier.Value);
            }
            return output;
        }

        private Task<HttpResponseMessage> GenerateRequestAsync(Url url, List<PropertyModels.ContactDataModel.IdentifiersModel> Identifiers, object payload = null, string queryParams = "")
        {
            try
            {
                // url = weddb.URLs.FirstOrDefault(w => w.Id == url.Id);
                HttpMethod method = HttpMethod.Post;
                if (url.Httptype == 2)
                    method = HttpMethod.Get;
                else if (url.Httptype == 3)
                    method = HttpMethod.Put;
                else if (url.Httptype == 6)
                    method = new HttpMethod("PATCH");

                string requestUrl = "";
                string requestBody = "";
                string contentType = "";
                if (url.Name.Contains("|"))
                {
                    var urls = url.Name.Split('|');
                    requestUrl = urls[0];
                    requestBody = urls[1];
                }
                else
                    requestUrl = url.Name;
                if (requestUrl.Contains("{"))
                    requestUrl = ReplaceIdentifiersInString(Identifiers, requestUrl);

                if (requestBody.Contains("{"))
                {
                    requestBody = ReplaceIdentifiersInString(Identifiers, requestBody);
                }

                bool ShouldSetContent = false;
                if (url.Headers.Count(w => w.Name.ToLower() == "content-type" && w.Prefix == "application/x-www-form-urlencoded") > 0)
                {
                    contentType = "application/x-www-form-urlencoded";
                    ShouldSetContent = true;
                }
                else if (url.Headers.Count(w => w.Name.ToLower() == "content-type" && w.Prefix == "application/x-www-url-form-urlencoded") > 0)
                {
                    contentType = "application/x-www-url-form-urlencoded";
                    ShouldSetContent = true;
                }
                else if (url.Headers.Count(w => w.Name.ToLower() == "content-type" && w.Prefix == "multipart/form-data") > 0)
                {
                    contentType = "multipart/form-data";
                    ShouldSetContent = true;
                }

                if (!string.IsNullOrEmpty(queryParams))
                    requestUrl += queryParams;

                var request = PrepareRequest(method, requestUrl, payload, requestBody, contentType: contentType, shouldSetContent: ShouldSetContent);

                if (url.HasHeaders)
                {
                    List<Header> headers = weddb.Headers.Where(w => w.Urlid  == url.Id && w.IsActive).ToList();
                    
                    request = AddHeadersToMessage(request, headers, Identifiers);
                }
                return httpClient.SendAsync(request, HttpCompletionOption.ResponseContentRead);
            }
            catch (Exception ex)
            {
                TelegramService.SendMessageToTestBot2("exception: " + ex.Message);
                TelegramService.SendMessageToTestBot2("exception trace: " + ex.StackTrace.ToString());

                Console.WriteLine("Error while Sending Request : " + ex.Message);
                throw ex;
            }
        }

        public async Task<T> MakeRequest<T>(Url url, UserProvider userProvider = null, List<PropertyModels.ContactDataModel.IdentifiersModel> Identifiers = null, object payload = null, string queryParams = "", List<PropertyModels.ContactDataModel.IdentifiersModel> customIdentifiers = null)//, CancellationToken token
        {
            try
            {
                List<PropertyModels.ContactDataModel.IdentifiersModel> Combined = new List<PropertyModels.ContactDataModel.IdentifiersModel>();
                Combined = Combined.Concat(Identifiers).ToList();
                if (customIdentifiers != null && customIdentifiers.Count > 0)
                    Combined = Combined.Concat(customIdentifiers).ToList();

                HttpResponseMessage response = await GenerateRequestAsync(url, Combined, payload, queryParams);//, token
                string GetResponse = response.ToString();
                string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                if (typeof(T) == typeof(string))
                {
                    return GetValue<T>(content);
                }
                else if (typeof(T) == typeof(byte[]))
                {
                    var array = await response.Content.ReadAsStringAsync();
                    // return array;
                }

                if (!string.IsNullOrEmpty(content))
                {
                    //var X = JsonSerializer.Deserialize<T>(content);
                    var X = JsonConvert.DeserializeObject<T>(content);
                    var result = X.ToString();
                    if (response.StatusCode == HttpStatusCode.Forbidden
                        || response.StatusCode == HttpStatusCode.Unauthorized
                        || response.StatusCode == HttpStatusCode.InternalServerError)
                    {
                        PropertyModels.ContactDataModel.ErrorMailModel2 errorModel = new PropertyModels.ContactDataModel.ErrorMailModel2
                        {
                            Url = url.ShowOrder + ":" + response.RequestMessage.ToString(),
                            Count = 0,
                            CreatedDate = DateTime.UtcNow,
                            ErrorCode = response.StatusCode.ToString(),
                            ErrorMessage = response.ReasonPhrase.ToString() ?? "",
                            UserProviderId = (userProvider == null) ? Guid.Empty : userProvider.Id,
                            ProvisionId = Guid.Empty
                        };
                        if (payload != null)
                            errorModel.Payload = payload.ToString();
                        errorModel.EmailId = (userProvider == null) ? "" : userProvider.EmailAddress;
                        errorModel.Method = "WhatElse.Data ProviderService";
                        var res = EmailServices.SMTPErrorEmail(errorModel);
                    }
                    return X;
                }
                else return default(T);
            }
            catch (WebException ex)
            {
                Console.WriteLine("Error" + ex.StackTrace);
                throw ex;
            }
            catch (HttpRequestException ex)
            {
                Console.WriteLine("Error in GET Request :" + ex.Message);
                throw ex;
            }
        }

        public string GenerateURLFromIdentifiers(Url url, List<PropertyModels.ContactDataModel.IdentifiersModel> Identifiers)
        {
            return ReplaceIdentifiersInString(Identifiers, url.Name);
        }

        public string GenerateLoginURL(Guid providerId, Guid provisionId)
        {

            return "";
        }


        public async Task GenerateAccessRefreshTokensForProviders(Guid userId, Guid WEToken, bool shouldOverride = false, string providerId = "")
        {
            try
            {
                if (providerId == "")
                {
                    var userProviders = wepdb.UserProviders.Where(w => w.UserId == userId && w.IsActive && w.ProviderId != CommonData.ContactAddedByAdmin
                  // && w.ProviderId != HttpHeader.Common.CommonData.AndroidContacts
                   && w.ProviderId != CommonData.AndroidContacts
                   && w.ProviderId != CommonData.WebContacts
                   && w.ProviderId != CommonData.ContactAddedByUser
                   && w.ProviderId != CommonData.IOSContacts
                   ).ToList();
                    foreach (var item in userProviders.ToList())
                    {
                        var provider = weddb.Providers.FirstOrDefault(w => w.Id == item.ProviderId && w.IsActive);
                        if (provider.ProviderTypeId != CommonData.EnterpriseStorageProivderTyId && provider.ProviderTypeId != CommonData.PersonalStoragePTId)
                        {
                            try
                            {
                                await GenerateAccessRefreshTokensForUserProvider(provider, item, WEToken);
                            }
                            catch (Exception ex)
                            {
                            }
                        }
                    }
                }
                else
                {
                    Guid providerid = Guid.Parse(providerId);
                    var userProviders = wepdb.UserProviders.Where(w => w.UserId == userId && w.IsActive && w.ProviderId != CommonData.ContactAddedByAdmin
                   && w.ProviderId != CommonData.AndroidContacts
                   && w.ProviderId != CommonData.WebContacts
                   && w.ProviderId != CommonData.ContactAddedByUser
                   && w.ProviderId != CommonData.IOSContacts
                   && w.ProviderId == providerid
                   ).ToList();
                    foreach (var item in userProviders.ToList())
                    {
                        try
                        {
                            var provider = weddb.Providers.FirstOrDefault(w => w.Id == item.ProviderId && w.IsActive);
                            if (provider.ProviderTypeId != CommonData.EnterpriseStorageProivderTyId && provider.ProviderTypeId != CommonData.PersonalStoragePTId)
                            {
                                await GenerateAccessRefreshTokensForUserProvider(provider, item, WEToken);
                            }
                        }
                        catch (Exception ex)
                        {
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }
            finally
            {

            }
        }

        public async Task GenerateAccessRefreshTokensByUserProviderId(Guid userId, Guid WEToken, UserProvider UP)
        {
            try
            {
                var provider = weddb.Providers.FirstOrDefault(w => w.Id == UP.ProviderId && w.IsActive == true);
                await GenerateAccessRefreshTokensForUserProvider(provider, UP, WEToken);
            }
            catch (Exception ex)
            {
            }
            finally
            {

            }
        }
        async Task GenerateAccessRefreshTokensForUserProvider(Provider provider, UserProvider redeemed, Guid WEToken, bool UpdateAsync = true)
        {
            try
            {
                List<PropertyModels.ContactDataModel.IdentifiersModel> identifierList = new List<PropertyModels.ContactDataModel.IdentifiersModel>();
                var providerUrls = weddb.ProviderUrls.Where(w => w.ProviderId == provider.Id && w.IsActive);

                if (providerUrls.Count(w => w.Url.ShowOrder == 101) > 0)
                {
                    var providerUrl = weddb.ProviderUrls.FirstOrDefault(w => w.ProviderId == provider.Id && (w.Url.ShowOrder == 101) && w.IsActive);

                    Url idUrl = providerUrl.Url;
                    identifierList = GetIdentifiersForUserProvider(redeemed);

                    var tokenObject = await MakeRequest<JToken>(idUrl, redeemed, Identifiers: identifierList, payload: "");
                    var ids = idUrl.Identifier.Split(',').ToList();
                    var up = wepdb.UserProviders.FirstOrDefault(w => w.Id == redeemed.Id);
                    up.ModifiedDate = DateTime.UtcNow;
                    wepdb.SaveChanges();
                    if (ids.Count > 0)
                    {
                        PropertyModels.DataModels.UPIdentifierModel modeltoPass = new PropertyModels.DataModels.UPIdentifierModel
                        {
                            UserProviderId = redeemed.Id
                        };

                        foreach (var id in ids.ToList())
                        {
                            try
                            {
                                var idParts = id.Split('|');
                                string name = idParts[0];
                                string path = idParts[1];
                                if (path.StartsWith("J-"))
                                {
                                    path = path.Substring(2);
                                    string token = tokenObject.SelectToken(path).Value<string>();
                                    var identifier = identifierList.First(w => w.Name == name);
                                    if (identifier == null)
                                        continue;

                                    identifierList.First(w => w.Name == name).Value = token;

                                    modeltoPass.IdentifiersModel.Add(new PropertyModels.DataModels.IdentifiersModel()
                                    {
                                        IId = identifier.IId,
                                        Name = identifier.Name,
                                        Value = token
                                    });
                                }
                            }
                            catch (Exception ex)
                            {
                                //LoggerService.Instance.TrackError(ex);
                            }
                        }
                        //provider.LoggedinAccountsModel.First(w => w.UserProviderId == redeemed.UserProviderId).IdentifiersModel = redeemed.IdentifiersModel;
                        //await db.UpdateAsyncWithChildren(provider);                        
                        var reponse = await restClient.WEPostAsync<string>("https://fcdtws02.azurewebsites.net/C963F02F", modeltoPass, WEToken, true);
                    }
                }
            }
            catch (Exception ex)
            {
                //  LoggerService.Instance.TrackError(ex);
                return;
            }
        }
        public List<PropertyModels.ContactDataModel.IdentifiersModel> GetIdentifiersForUserProvider(UserProvider userProvider)
        {
            List<PropertyModels.ContactDataModel.IdentifiersModel> identifierList = new List<PropertyModels.ContactDataModel.IdentifiersModel>();
            try
            {
                var userProviderIdentifiers = wepdb.UserProviderIdentifiers.Where(w => w.UserProviderId == userProvider.Id && w.IsActive).ToList();
                var orgProviderIdIsThere = weodb.OrgProviders.Any(w => w.OrgId == userProvider.OrgId && w.ProviderId == userProvider.ProviderId && w.IsActive);
                foreach (var userProviderIdentifier in userProviderIdentifiers)
                {
                    var identifier = weddb.Identifiers.FirstOrDefault(w => w.Id == userProviderIdentifier.IdentifierId).Name;
                    identifierList.Add(new PropertyModels.ContactDataModel.IdentifiersModel()
                    {
                        IId = userProviderIdentifier.IdentifierId,
                        Name = identifier,
                        Value = userProviderIdentifier.Value ?? ""
                    });
                }
                if (orgProviderIdIsThere == true)
                {
                    var orgProviderId = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == userProvider.OrgId && w.ProviderId == userProvider.ProviderId && w.IsActive).Id;
                    var orgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == orgProviderId && w.IsActive);

                    foreach (var orgProviderIdentifier in orgProviderIdentifiers)
                    {
                        var identifier = weddb.Identifiers.FirstOrDefault(w => w.Id == orgProviderIdentifier.IdentifierId).Name;
                        identifierList.Add(new PropertyModels.ContactDataModel.IdentifiersModel()
                        {
                            IId = orgProviderIdentifier.IdentifierId,
                            Name = identifier,
                            Value = orgProviderIdentifier.Value
                        });
                    }
                }
                var onTheFlyProviders = weddb.Identifiers.Where(w => w.ProviderId == userProvider.ProviderId && w.IsActive && w.OnTheFly == true).ToList();
                foreach (var onTheFlyProvider in onTheFlyProviders)
                {
                    if (!identifierList.Select(w => w.IId).Contains(onTheFlyProvider.Id))
                    {
                        identifierList.Add(new PropertyModels.ContactDataModel.IdentifiersModel()
                        {
                            IId = onTheFlyProvider.Id,
                            Name = onTheFlyProvider.Name,
                            Value = ""
                        });
                    }
                }
                return identifierList;
            }
            catch (Exception ex)
            {
                return identifierList;
            }
        }

        public List<PropertyModels.DataModels.IdentifiersModel> ReturnIdentifierModel(UserProvider userProvider)
        {
            List<PropertyModels.DataModels.IdentifiersModel> identifierList = new List<PropertyModels.DataModels.IdentifiersModel>();
            try
            {
                var userProviderIdentifiers = wepdb.UserProviderIdentifiers.Where(w => w.UserProviderId == userProvider.Id && w.IsActive).ToList();
                var orgProviderId = weodb.OrgProviders.FirstOrDefault(w => w.OrgId == userProvider.OrgId && w.ProviderId == userProvider.ProviderId && w.IsActive).Id;
                var orgProviderIdentifiers = weodb.OrgIdentifiers.Where(w => w.OrgProviderId == orgProviderId && w.IsActive);
                foreach (var userProviderIdentifier in userProviderIdentifiers)
                {
                    var identifier = weddb.Identifiers.FirstOrDefault(w => w.Id == userProviderIdentifier.IdentifierId).Name;
                    identifierList.Add(new PropertyModels.DataModels.IdentifiersModel()
                    {
                        IId = userProviderIdentifier.IdentifierId,
                        Name = identifier,
                        Value = userProviderIdentifier.Value ?? ""
                    });
                }
                foreach (var orgProviderIdentifier in orgProviderIdentifiers)
                {
                    var identifier = weddb.Identifiers.FirstOrDefault(w => w.Id == orgProviderIdentifier.IdentifierId).Name;
                    identifierList.Add(new PropertyModels.DataModels.IdentifiersModel()
                    {
                        IId = orgProviderIdentifier.IdentifierId,
                        Name = identifier,
                        Value = orgProviderIdentifier.Value
                    });
                }
                return identifierList;

            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
