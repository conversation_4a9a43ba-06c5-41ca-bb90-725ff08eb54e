using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using OnePage.Connect.Interfaces;


namespace OnePage.Connect.Services;

public class TelemetryTracker : ITelemetryTracker
{
    private readonly TelemetryClient _telemetry = new TelemetryClient();

    public void TrackException(Exception ex)
    {
        _telemetry.TrackException(ex);
    }

    

    public void TrackTrace(string message, SeverityLevel severity)
    {
        _telemetry.TrackTrace(message, severity);
    }

    public void TrackEvent(string eventName, Dictionary<string, string> properties)
    {
        _telemetry.TrackEvent(eventName, properties);
    }

    public Stopwatch StartTrackRequest(string requestName)
    {
        // Operation Id is attached to all telemetry and helps you identify
        // telemetry associated with one request:
        _telemetry.Context.Operation.Id = Guid.NewGuid().ToString();
        return Stopwatch.StartNew();
    }

    public void StopTrackRequest(string requestName, Stopwatch stopwatch)
    {
        stopwatch.Stop();
        _telemetry.TrackRequest(requestName, DateTime.Now,
            stopwatch.Elapsed,
            "200", true); // Response code, success
    }
}