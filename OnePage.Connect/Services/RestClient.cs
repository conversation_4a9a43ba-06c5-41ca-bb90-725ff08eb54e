using System;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace OnePage.Connect.Services;

public sealed class RestClient
{
    Guid authToken;

    public static RestClient Instance { get; } = new RestClient();

    private HttpClient GetClient(HttpClientHandler handler = null, bool longTimeout = false)
    {
        var client = handler == null ? new HttpClient() : new HttpClient(handler, true);
        if (longTimeout == true)
            client.Timeout = TimeSpan.FromMinutes(40);
        else
            client.Timeout = TimeSpan.FromSeconds(720);
        return client;
    }

    private void Dispose()
    {
    }

    private HttpRequestMessage MailModoProcessRequest(HttpMethod method, string url, object payload = null,
        bool includeZumo = false, bool longRequest = false)
    {
        try
        {
            var request = PrepareRequest(method, url, payload);

            request.Headers.Add("mmApiKey", "368HKX0-JM24RBW-N62SZXR-Z8MKGPS");

            return request;
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }

    private Task<HttpResponseMessage> MailModoRequestAsync(HttpMethod method, string url, object payload = null,
        bool includeZumo = false, bool longRequest = false)
    {
        try
        {
            var request = PrepareRequest(method, url, payload);
            request.Headers.Add("'mmApiKey", "368HKX0-JM24RBW-N62SZXR-Z8MKGPS");

            return GetClient(longTimeout: longRequest).SendAsync(request, HttpCompletionOption.ResponseContentRead);
        }
        catch (Exception ex)
        {
            Debug.WriteLine("Error while Sending Request : " + ex.Message);
            throw ex;
        }
    }

    private Task<HttpResponseMessage> RequestAsync(HttpMethod method, string url, object payload = null,
        bool includeZumo = false, bool longRequest = false)
    {
        try
        {
            var request = PrepareRequest(method, url, payload);

            if (authToken != default(Guid))
            {
                request.Headers.Add("authToken", authToken.ToString());
            }

            if (includeZumo)
            {
                request.Headers.TryAddWithoutValidation("ZUMO-API-VERSION", "2.0.0");
            }

            return GetClient(longTimeout: longRequest).SendAsync(request, HttpCompletionOption.ResponseContentRead);
        }
        catch (Exception ex)
        {
            Debug.WriteLine("Error while Sending Request : " + ex.Message);
            throw ex;
        }
    }

    private Task<HttpResponseMessage> RequestAsync(HttpMethod method, string url, CancellationToken token,
        object payload = null)
    {
        try
        {
            var request = PrepareRequest(method, url, payload);

            if (authToken != default(Guid))
            {
                request.Headers.Add("authToken", authToken.ToString());
            }

            return GetClient().SendAsync(request, HttpCompletionOption.ResponseContentRead, token);
        }
        catch (Exception ex)
        {
            Debug.WriteLine("Error while Sending Request : " + ex.Message);
            throw ex;
        }
    }

    private Task<HttpResponseMessage> RequestSalesForceAsync(HttpMethod method, string url, string authToken,
        CancellationToken token, object payload = null)
    {
        try
        {
            var request = PrepareRequest(method, url, payload);

            if (authToken != "")
            {
                request.Headers.TryAddWithoutValidation("Authorization", authToken);
            }

            return GetClient().SendAsync(request, HttpCompletionOption.ResponseContentRead, token);
        }
        catch (Exception ex)
        {
            Debug.WriteLine("Error while Sending Request : " + ex.Message);
            throw ex;
        }
    }

    private HttpRequestMessage PrepareRequest(HttpMethod method, string url, object payload)
    {
        var uri = PrepareUri(url);
        var request = new HttpRequestMessage(method, uri);
        if (payload != null)
        {
            var json = System.Text.Json.JsonSerializer.Serialize(payload);
            request.Content = new StringContent(json, Encoding.UTF8, "application/json");
        }

        return request;
    }

    private Uri PrepareUri(string url)
    {
        return new Uri(url);
    }

    private readonly Action<HttpStatusCode, string> _defaultErrorHandler = (statusCode, body) =>
    {
        if (statusCode < HttpStatusCode.OK || statusCode >= HttpStatusCode.BadRequest)
        {
            var statusCodeMsgBody = statusCode + "|" + body;
            Debug.WriteLine(string.Format("Request responded with status code={0}, response={1}", statusCode, body));
            throw new Exception(statusCodeMsgBody);
        }
    };

    private void HandleIfErrorResponse(HttpStatusCode statusCode, string content,
        Action<HttpStatusCode, string> errorHandler = null)
    {
        //if (statusCode == HttpStatusCode.Forbidden)
        //{
        //    if (App.Current != null && App.Current is PrismApplication)
        //    {
        //        if (AppConstants.ShouldLogout == false)
        //        {
        //            AppConstants.ShouldLogout = true;
        //            eventAggregator = (App.Current as PrismApplication).Container.GetContainer().Resolve<IEventAggregator>();
        //            eventAggregator.GetEvent<UserInvalidEvent>().Publish();
        //        }
        //    }
        //}
        if (errorHandler != null)
        {
            errorHandler(statusCode, content);
        }
        else
        {
            _defaultErrorHandler(statusCode, content);
        }
    }

    private T GetValue<T>(String value)
    {
        return (T)Convert.ChangeType(value, typeof(T));
    }

    string GetResponse { get; set; }

    public async Task<T> GetAsync<T>(string url, bool useAuthToken = true)
    {
        try
        {
            //#region Set AuthToken

            //if (useAuthToken && AppConstants.USER_AUTH_TOKEN != default(Guid))
            //    authToken = AppConstants.USER_AUTH_TOKEN;

            //#endregion

            HttpResponseMessage response = await RequestAsync(HttpMethod.Get, url).ConfigureAwait(false);
            GetResponse = response.ToString();
            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            HandleIfErrorResponse(response.StatusCode, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }

            //#region Reset AuthToken

            //if (useAuthToken && authToken != default(Guid))
            //    authToken = default(Guid);

            //#endregion

            var X = System.Text.Json.JsonSerializer.Deserialize<T>(content);
            return X;
        }
        catch (WebException ex)
        {
            Debug.WriteLine("Error" + ex.StackTrace);
            throw ex;
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in GET Request :" + ex.Message);
            throw ex;
        }
    }

    public async Task<T?> MailModoPostAsync<T>(string url, object payload)
    {
        try
        {
            string content = "";
            HttpStatusCode status;
            HttpRequestMessage httpRequest = MailModoProcessRequest(HttpMethod.Post, url, payload);
            using (HttpClient httpClient = GetClient())
            {
                using (HttpResponseMessage response = await httpClient
                           .SendAsync(httpRequest, HttpCompletionOption.ResponseContentRead).ConfigureAwait(false))
                {
                    content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
                    status = response.StatusCode;
                }
            }

            HandleIfErrorResponse(status, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }
            return System.Text.Json.JsonSerializer.Deserialize<T>(content);
            
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in POST Request :" + ex.Message);
            throw ex;
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }

    public async Task<T?> PostAsync<T>(string url, object payload, bool useAuthToken = true, bool longRequest = false)
    {
        try
        {
            //#region Set AuthToken

            //if (useAuthToken && AppConstants.USER_AUTH_TOKEN != default(Guid))
            //    authToken = AppConstants.USER_AUTH_TOKEN;

            //#endregion

            DateTime startTime = DateTime.Now;

            HttpResponseMessage response = await RequestAsync(HttpMethod.Post, url, payload)
                .ConfigureAwait(false);

            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            DateTime endTime = DateTime.Now;
            TimeSpan time = endTime.Subtract(startTime);
            //LoggerService.Instance.TrackEvent("Api Instrumentation", new Dictionary<string, string> {
            //    { "API", url },
            //    { "Time", time.TotalMilliseconds.ToString() + " ms" }
            //});

            HandleIfErrorResponse(response.StatusCode, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }

            //#region Reset AuthToken

            //if (useAuthToken && authToken != default(Guid))
            //    authToken = default(Guid);

            //#endregion
            return System.Text.Json.JsonSerializer.Deserialize<T>(content);
            // return JsonConvert.DeserializeObject<T>(content);
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in POST Request :" + ex.Message);
            throw ex;
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }

    public async Task<T?> WEPostAsync<T>(string url, object payload, Guid WEToken, bool useAuthToken = true,
        bool IncludeZumo = false, bool longRequest = false)
    {
        try
        {
            //#region Set AuthToken

            //if (useAuthToken && AppConstants.USER_AUTH_TOKEN != default(Guid))
            //    authToken = AppConstants.USER_AUTH_TOKEN;

            //#endregion
            //before api call
            authToken = WEToken;

            HttpResponseMessage response = await RequestAsync(HttpMethod.Post, url, payload, IncludeZumo)
                .ConfigureAwait(false);
            GetResponse = response.ToString();

            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

            authToken = Guid.Empty;

            HandleIfErrorResponse(response.StatusCode, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }

            //#region Reset AuthToken

            //if (useAuthToken && authToken != default(Guid))
            //    authToken = default(Guid);

            //#endregion
            return System.Text.Json.JsonSerializer.Deserialize<T>(content);
            // return JsonConvert.DeserializeObject<T>(content);
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in POST Request :" + ex.Message);
            throw ex;
        }
        catch (WebException ex)
        {
            Debug.WriteLine("Error" + ex.StackTrace);
            throw ex;
        }
        catch (Exception ex)
        {
            Debug.WriteLine("Error" + ex.StackTrace);
            throw ex;
        }
    }

    public async Task<T?> WEPostAsync<T>(string url, object payload, CancellationToken token, bool useAuthToken = true)
    {
        try
        {
            //#region Set AuthToken

            //if (useAuthToken && AppConstants.USER_AUTH_TOKEN != default(Guid))
            //    authToken = AppConstants.USER_AUTH_TOKEN;

            //#endregion

            HttpResponseMessage response = await RequestAsync(HttpMethod.Post, url, token, payload)
                .ConfigureAwait(false);
            GetResponse = response.ToString();

            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            //if (response.StatusCode == HttpStatusCode.Forbidden)
            //{
            //    if (App.Current != null && App.Current is PrismApplication)
            //    {
            //        if (AppConstants.ShouldLogout == false)
            //        {
            //            AppConstants.ShouldLogout = true;
            //            eventAggregator = (App.Current as PrismApplication).Container.GetContainer().Resolve<IEventAggregator>();
            //            eventAggregator.GetEvent<UserInvalidEvent>().Publish();
            //        }
            //    }
            //}
            HandleIfErrorResponse(response.StatusCode, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }

            #region Reset AuthToken

            if (useAuthToken && authToken != default(Guid))
                authToken = default(Guid);

            #endregion
            return System.Text.Json.JsonSerializer.Deserialize<T>(content);
            // return JsonConvert.DeserializeObject<T>(content);
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in POST Request :" + ex.Message);
            throw ex;
        }
        catch (WebException ex)
        {
            Debug.WriteLine("Error" + ex.StackTrace);
            throw ex;
        }
        catch (Exception ex)
        {
            Debug.WriteLine("Error" + ex.StackTrace);
            throw ex;
        }
    }

    public async Task<T?> PutAsync<T>(string url, object payload, bool useAuthToken = true)
    {
        try
        {
            //#region Set AuthToken

            //if (useAuthToken && AppConstants.USER_AUTH_TOKEN != default(Guid))
            //    authToken = AppConstants.USER_AUTH_TOKEN;

            //#endregion

            HttpResponseMessage response =
                await RequestAsync(HttpMethod.Put, url, payload).ConfigureAwait(false);
            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            HandleIfErrorResponse(response.StatusCode, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }

            #region Reset AuthToken

            if (useAuthToken && authToken != default(Guid))
                authToken = default(Guid);

            #endregion
            return System.Text.Json.JsonSerializer.Deserialize<T>(content);
            // return JsonConvert.DeserializeObject<T>(content);
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in PUT Request :" + ex.Message);
            throw ex;
        }
    }

    public async Task<T> PostAsyncWithoutReturnContent<T>(string url, object payload, bool useAuthToken = true)
    {
        try
        {
            //#region Set AuthToken
            //if (useAuthToken && AppConstants.USER_AUTH_TOKEN != default(Guid))
            //    authToken = AppConstants.USER_AUTH_TOKEN;
            //#endregion

            HttpResponseMessage response = await RequestAsync(HttpMethod.Post, url, payload).ConfigureAwait(false);
            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            HandleIfErrorResponse(response.StatusCode, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }

            #region Reset AuthToken

            if (useAuthToken && authToken != default(Guid))
                authToken = default(Guid);

            #endregion
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in POST Request :" + ex.Message);
            throw ex;
        }

        return default(T);
    }


    public async Task<T> PutAsyncWithoutReturnContent<T>(string url, object payload, bool useAuthToken = true)
    {
        try
        {
            //#region Set AuthToken
            //if (useAuthToken && AppConstants.USER_AUTH_TOKEN != default(Guid))
            //    authToken = AppConstants.USER_AUTH_TOKEN;
            //#endregion

            HttpResponseMessage response = await RequestAsync(HttpMethod.Post, url, payload).ConfigureAwait(false);
            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            HandleIfErrorResponse(response.StatusCode, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }

            #region Reset AuthToken

            if (useAuthToken && authToken != default(Guid))
                authToken = default(Guid);

            #endregion
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in POST Request :" + ex.Message);
            throw ex;
        }

        return default(T);
    }

    public async Task<T?> DeleteAsync<T>(string url, bool useAuthToken = true)
    {
        try
        {
            //#region Set AuthToken
            //if (useAuthToken && AppConstants.USER_AUTH_TOKEN != default(Guid))
            //    authToken = AppConstants.USER_AUTH_TOKEN;
            //#endregion
            HttpResponseMessage response = await RequestAsync(HttpMethod.Delete, url).ConfigureAwait(false);
            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            HandleIfErrorResponse(response.StatusCode, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }

            #region Reset AuthToken

            if (useAuthToken && authToken != default(Guid))
                authToken = default(Guid);

            #endregion
            return System.Text.Json.JsonSerializer.Deserialize<T>(content);
            //return JsonConvert.DeserializeObject<T>(content);
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in DELETE Request :" + ex.Message);
            throw ex;
        }
    }

    public async Task<T?> GetSalesForceAsync<T>(string url, string authToken, CancellationToken token,
        bool Authorization = true)
    {
        try
        {
            HttpResponseMessage response =
                await RequestSalesForceAsync(HttpMethod.Get, url, authToken, token).ConfigureAwait(false);
            GetResponse = response.ToString();
            string content = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
            HandleIfErrorResponse(response.StatusCode, content);
            if (typeof(T) == typeof(string))
            {
                return GetValue<T>(content);
            }
            return System.Text.Json.JsonSerializer.Deserialize<T>(content);
            // var X = JsonConvert.DeserializeObject<T>(content);
            // return X;
        }
        catch (WebException ex)
        {
            Debug.WriteLine("Error" + ex.StackTrace);
            throw ex;
        }
        catch (HttpRequestException ex)
        {
            Debug.WriteLine("Error in GET Request :" + ex.Message);
            throw ex;
        }
    }

    public async Task<string> GetRefreshToken(string url, string refreshToken, string clientId, string clientSecret)
    {
        HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, url)
        {
            Content = new StringContent("grant_type=refresh_token&" + "refresh_token=" + refreshToken + "&client_id=" +
                                        clientId +
                                        "&client_secret=" + clientSecret, Encoding.UTF8,
                "application/x-www-form-urlencoded")
        };
        var httpClient = new HttpClient();
        HttpResponseMessage response = await httpClient.SendAsync(request);
        string responseString = await response.Content.ReadAsStringAsync();
        return responseString;
    }
}