﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
   public class O365Models
    {
        #region CatchAll

        public class UserToken
        {
            public string token_type { get; set; }
            public string scope { get; set; }
            public int expires_in { get; set; }
            public int ext_expires_in { get; set; }
            public string access_token { get; set; }
            public string refresh_token { get; set; }
        }
        public class UserDetails
        {
            public string givenName { get; set; }
            public string surname { get; set; }
            public string displayName { get; set; }
            public string id { get; set; }
            public string userPrincipalName { get; set; }
            public object jobTitle { get; set; }
            public object mail { get; set; }
            public object mobilePhone { get; set; }
            public object officeLocation { get; set; }
            public object preferredLanguage { get; set; }
            public string AccessToken { get; set; }
            public string RefreshToken { get; set; }
        }
        #region People Api
        public class ScoredEmailAddress
        {
            public string address { get; set; }
            public double relevanceScore { get; set; }
        }

        public class PersonType
        {
            public string classa { get; set; }
            public string subclass { get; set; }
        }

       public class GraphPeopleModel
        {
            public string id { get; set; }
            public string displayName { get; set; }
            public string givenName { get; set; }
            public string surname { get; set; }
            public string birthday { get; set; }
            public string personNotes { get; set; }
            public bool isFavorite { get; set; }
            public string jobTitle { get; set; }
            public object companyName { get; set; }
            public string yomiCompany { get; set; }
            public object department { get; set; }
            public object officeLocation { get; set; }
            public string profession { get; set; }
            public string userPrincipalName { get; set; }
            public string imAddress { get; set; }
            public List<ScoredEmailAddress> scoredEmailAddresses { get; set; }
            public List<object> phones { get; set; }
            public List<object> postalAddresses { get; set; }
            public List<object> websites { get; set; }
            public PersonType personType { get; set; }

            public GraphPeopleModel()
            {
                scoredEmailAddresses = new List<ScoredEmailAddress>();
                personType = new PersonType();
            }
        }
        #endregion
        #region Contact Api

        public class Address
        {
            public string street { get; set; }
            public string city { get; set; }
            public string state { get; set; }
            public string countryOrRegion { get; set; }
            public string postalCode { get; set; }
        }


        public class GraphContactModel
        {
            public string invalidodataetag { get; set; }
            public string id { get; set; }
            public DateTime createdDateTime { get; set; }
            public DateTime lastModifiedDateTime { get; set; }
            public string changeKey { get; set; }
            public List<string> categories { get; set; }
            public string parentFolderId { get; set; }
            public DateTime? birthday { get; set; }
            public string fileAs { get; set; }
            public string displayName { get; set; }
            public string givenName { get; set; }
            public string initials { get; set; }
            public string middleName { get; set; }
            public string nickName { get; set; }
            public string surname { get; set; }
            public string title { get; set; }
            public string yomiGivenName { get; set; }
            public string yomiSurname { get; set; }
            public string yomiCompanyName { get; set; }
            public string generation { get; set; }
            public List<string> imAddresses { get; set; }
            public string jobTitle { get; set; }
            public string companyName { get; set; }
            public string department { get; set; }
            public string officeLocation { get; set; }
            public string profession { get; set; }
            public string businessHomePage { get; set; }
            public string assistantName { get; set; }
            public string manager { get; set; }
            public List<string> homePhones { get; set; }
            public string mobilePhone { get; set; }
            public List<string> businessPhones { get; set; }
            public object spouseName { get; set; }
            public string personalNotes { get; set; }
            public List<string> children { get; set; }
            public List<EmailAddress> emailAddresses { get; set; }
            public Address homeAddress { get; set; }
            public Address businessAddress { get; set; }
            public Address otherAddress { get; set; }
            public GraphContactModel()
            {
                otherAddress = new Address();
                businessAddress = new Address();
                homeAddress = new Address();
                emailAddresses = new List<EmailAddress>();

                homePhones = new List<string>();
                businessPhones = new List<string>();

            }
        }
        public class EmailAddress
        {
            public string name { get; set; }
            public string address { get; set; }
        }

        #endregion
        #endregion

        #region Data mail models
        public class O365AttachmentModel 
        {
            public Guid ProviderId { get; set; }
            public Guid UserProviderId { get; set; }
            public string AttachId { get; set; }
            public string MailId { get; set; }
            public string name { get; set; }
            public string contentType { get; set; }
            public string receivedDateTime { get; set; }
        }
        #endregion

    }
}
