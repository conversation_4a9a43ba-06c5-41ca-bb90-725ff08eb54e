﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models;

   public class ChargebeeModels
    {
        public class ChargebeeAddons
        {
            public string Id { get; set; }
            public Guid ProviderTypeId { get; set; }
            public string Name { get; set; }
            public int TotalCredits { get; set; }
            public int CreditsLeft { get; set; }
            public bool IsAuth { get; set; }
        }
        public class ChargebeeAddonsModel
        {
            public string Id { get; set; }
            public int TotalUsedCredits { get; set; }
        }

        public class SubscriptionAddonModel
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public int TotalCredits { get; set; }
            public int CreditsLeft { get; set; }
            public bool IsAuth { get; set; }
            public string RemainingBillingCycles { get; set; }
            public string TrialEnd { get; set; }
            public string UnitPrice { get; set; }
        }
    }

