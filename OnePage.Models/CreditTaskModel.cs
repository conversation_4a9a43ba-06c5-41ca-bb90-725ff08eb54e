﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
    public class CreditTaskModel
    {
        public System.Guid Id { get; set; }
        public System.Guid UserTaskId { get; set; }
        public string Name { get; set; }
        public bool IsOneTime { get; set; }
        public bool IsActive { get; set; }
        public Nullable<int> Credits { get; set; }
        public int ShowSequence { get; set; }
        public string VerificationInfo { get; set; }
        public bool IsVerfied { get; set; }
        public bool IsUserSubmitted { get; set; }
    }
    public class OfferSettingModel
    {
        public Guid Id { get; set; }
        public Guid OfferId { get; set; }
        public Guid SettingId { get; set; }
        public string Value { get; set; }
    }
}
