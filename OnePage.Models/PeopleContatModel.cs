﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
    public class PeopleContatModel
    {
        public PeopleContatModel()
        {
            EmailList = new List<string>();
            Phones = new List<string>();
        }
        public List<string> EmailList { get; set; }
        public List<string> Phones { get; set; }
        public Guid ContactId { get; set; }
        public string LinkedInUrl { get; set; }
        public string TwitterUrl { get; set; }

    }
    public class PersonDataModel
    {
       public string DisplayIcon { get; set; }
        public string DisplayName { get; set; }
        public string DisplaySubtitle { get; set; }
        public string PDLData { get; set; }
    }
}
