﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
    public class From
    {
        public string email { get; set; }
    }

    public class To
    {
        public string email { get; set; }
    }

    public class DynamicTemplateData
    {
        public string first_name { get; set; }
        public bool receipt { get; set; }
        public string name { get; set; }
        public string address01 { get; set; }
        public string address02 { get; set; }
        public string city { get; set; }
        public string state { get; set; }
        public string zip { get; set; }
    }

    public class Personalization1
    {
        public List<To> to { get; set; }
        public DynamicTemplateData dynamic_template_data { get; set; }
    }

    public class SendGridEmailModel
    {
        public From from { get; set; }
        public List<Personalization1> personalizations { get; set; }
        public string template_id { get; set; }
    }

    public class SendTo
    {
        public bool all { get; set; }
    }

    public class EmailConfig
    {
        public string design_id { get; set; }
        public string editor { get; set; }
        public int suppression_group_id { get; set; }
        public int sender_id { get; set; }
    }

    public class SingleSendEmailModel
    {
        public string name { get; set; }
        public List<string> categories { get; set; }
        public SendTo send_to { get; set; }
        public EmailConfig email_config { get; set; }
    }

    // Root myDeserializedClass = JsonConvert.DeserializeObject<Root>(myJsonResponse); 
    public class CustomFields
    {
        public string w1_T { get; set; }
    }

    public class SendGridContact
    {
        public string address_line_1 { get; set; }
        public string address_line_2 { get; set; }
        public List<string> alternate_emails { get; set; }
        public string city { get; set; }
        public string country { get; set; }
        public string email { get; set; }
        public string first_name { get; set; }
        public string last_name { get; set; }
        public string postal_code { get; set; }
        public string state_province_region { get; set; }
        public CustomFields custom_fields { get; set; }
    }

    public class AddSendGridModel
    {
        public object list_ids { get; set; }
        public List<SendGridContact> contacts { get; set; }
    }



}
