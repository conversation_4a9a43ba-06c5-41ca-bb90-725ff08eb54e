﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
    public class PropertyModels
    {
        public class WebCalendarModels
        {
            public class WebEventModel
            {
                public WebEventModel()
                {
                    AttendeeList = new List<WebEventAttendeeModel>();
                }
                public string Id { get; set; }
                public DateTime Start { get; set; }
                public DateTime End { get; set; }
                public string Description { get; set; }
                public string Subject { get; set; }
                public string Link { get; set; }
                public string ProviderId { get; set; }
                public string EventId { get; set; }
                public List<WebEventAttendeeModel> AttendeeList { get; set; }
            }
            public class WebEventAttendeeModel
            {
                public string Name { get; set; }
                public string Id { get; set; }
                public string Address { get; set; }
            }
        }
        public class AdminModels
        {
            //public class IDPUserData
            //{
            //    public string UserName { get; set; }
            //    public string Phone { get; set; }
            //    public string Email { get; set; }
            //    public Guid OrgId { get; set; }
            //    public List<IDPProvisions> IDPProvisions { get; set; }
            //    public IDPUserData()
            //    {
            //        IDPProvisions = new List<IDPProvisions>();
            //    }

            //}
            public class IDPProvisions
            {
                public bool IsProvisioned { get; set; }
                public Guid ProviderId { get; set; }
                public Guid ProvisionId { get; set; }
                public string ProviderName { get; set; }
                public string EmailAddress { get; set; }
                public bool IsSignedIn { get; set; }
                public string SignInUrl { get; set; }
                public Guid AnalyticsId { get; set; }
            }
            public class OrganizationsModel
            {
                public Guid Id { get; set; }
                public string Salutation { get; set; }
                public string FirstName { get; set; }
                public string MiddleName { get; set; }
                public string LastName { get; set; }
                public string Email { get; set; }
                public string SanitizedNumber { get; set; }
                public string Company { get; set; }
            }
            public class LoginModel
            {
                public Guid TokenId { get; set; }
                public Guid UserId { get; set; }
                public string Email { get; set; }
                public string FirstName { get; set; }
                public bool IsAdmin { get; set; }
                public bool IsRestricted { get; set; }
                public bool IsPurchaser { get; set; }
                public bool IsProvider { get; set; }
                public bool ShouldResetPassword { get; set; }
                public int UserType { get; set; }
            }
            public class ResetPasswordModel
            {
                public Guid ResetPasswordId { get; set; }
                public string Email { get; set; }
                public string OldPassword { get; set; }
                public string NewPassword { get; set; }
                public string ConfirmPassword { get; set; }
            }
            public class ActionModel
            {
                public Guid Id { get; set; }
                public string ProvidersTypeList { get; set; }
                public string ProvidersList { get; set; }
                public Guid? UserProviderId { get; set; }
                public Guid OrgId { get; set; }
                public string UserId { get; set; }
                public string Title { get; set; }
                public string Icon { get; set; }
                public bool IsAggregate { get; set; }
                public bool IsOrgSpecific { get; set; }
                public int ShowSequence { get; set; }
                public bool IsActive { get; set; }
                public string Command { get; set; }
                public bool? IsExtUrl { get; set; }
                public string Url { get; set; }
                public bool? IsCardOnly { get; set; }
                public Guid? OrgRoleId { get; set; }
                public bool? IsPostCallAction { get; set; }
                public string CommandParameter { get; set; }
                public Guid? ModuleId { get; set; }
            }
            public class OrgLoginModel
            {
                public string Email { get; set; }
                public string Password { get; set; }
            }
            public class CardModel
            {
                public Guid Id { get; set; }
                public string ProvidersTypeList { get; set; }
                public string ProvidersList { get; set; }
                public string ActionsList { get; set; }
                public Guid? UserProviderId { get; set; }
                public Guid? OrgRoleId { get; set; }
                public string UserId { get; set; }
                public string Title { get; set; }
                public bool ShowCounter { get; set; }
                public string Icon { get; set; }
                public bool ShowMore { get; set; }
                public bool IsAggregate { get; set; }
                public bool IsOrgSpecific { get; set; }
                public string DataUrl { get; set; }
                public int? ChartType { get; set; }
                public bool HasActions { get; set; }
                public bool CanExpand { get; set; }
                public int ShowSequence { get; set; }
                public bool IsPartUrl { get; set; }
                public bool? IsGlobal { get; set; }
                public string IdUrl { get; set; }
                public int? Count { get; set; }
                public bool? ShowHeader { get; set; }
                public bool? ShowFooter { get; set; }
                public string ActionName { get; set; }
                public string ActionCommand { get; set; }
                public string Description { get; set; }
                public byte CardType { get; set; }
                public int MaxHeight { get; set; }
                public bool IsHome { get; set; }
                public string AddUrl { get; set; }
                public string EditUrl { get; set; }
                public string DetailUrl { get; set; }
                public Guid? ModuleId { get; set; }
            }
            public class AdminDemoRequestModel
            {
                public System.Guid Id { get; set; }
                public string FirstName { get; set; }
                public string Salutation { get; set; }
                public string LastName { get; set; }
                public string Email { get; set; }
                public string PhoneNumber { get; set; }
                public string CompanyName { get; set; }
                public string WebSite { get; set; }
                public string MiddleName { get; set; }
                public string CountryId { get; set; }
            }
            public class NotificarCardsModel
            {
                public Guid Id { get; set; }
                public string CardName { get; set; }
                public bool IsSelected { get; set; }
            }
            public class OrgProviderDetailsModel
            {
                public Guid Id { get; set; }
                public Guid ProviderTypeId { get; set; }
                public string ProviderTypeName { get; set; }
                public string ProviderName { get; set; }
                public System.Guid ProviderId { get; set; }
                //public string ClientId { get; set; }
                //public string Secret { get; set; }
                public DateTime CreatedDate { get; set; }
                //public string ImapPort { get; set; }
                //public string ImapServer { get; set; }
                //public int ImapSSL { get; set; }
                public bool IsAuth { get; set; }
                public bool? CanSaveForOffline { get; set; }
                public bool? ForceCallLog { get; set; }
                public List<OrgIdentifierModel> OrgIdentifierModel { get; set; }
                public OrgProviderDetailsModel()
                {
                    OrgIdentifierModel = new List<OrgIdentifierModel>();
                }
            }
            public class OrgIdentifierModel
            {
                public Guid OPId { get; set; }
                public string Name { get; set; }
                public string Value { get; set; }
            }
            public class OrgProviderModel
            {
                public System.Guid ProviderId { get; set; }
                public string ClientId { get; set; }
                public string Secret { get; set; }
                public bool IsActive { get; set; }
                public int ShowSequence { get; set; }
                public string ImapPort { get; set; }
                public string ImapServer { get; set; }
                public int ImapSSL { get; set; }
                public bool CanSaveForOffline { get; set; }
                public bool LogEveryCRMContactCall { get; set; }
            }
            public class OrgRoleModel
            {
                public Guid Id { get; set; }
                public string Name { get; set; }
                public int? ShowSequence { get; set; }
                public Guid? ProviderId { get; set; }
                public string Actions { get; set; }
                public string Cards { get; set; }
                public string Triggers { get; set; }
                public bool IsActive { get; set; }
            }

            public class RoleTemplateModel
            {
                public Guid Id { get; set; }
                public Guid OrgId { get; set; }
                public Guid ProviderId { get; set; }
                public string Name { get; set; }
                public int? ShowSequence { get; set; }
                public string Actions { get; set; }
                public List<IdNameModel> CardIdNameModel { get; set; }
                public string Triggers { get; set; }
                public RoleTemplateModel()
                {
                    CardIdNameModel = new List<IdNameModel>();
                }
            }
            public class PostOrgRoleTemplateModel
            {
                public Guid Id { get; set; }
                public Guid ProviderId { get; set; }
                public string Name { get; set; }
                public int? ShowSequence { get; set; }
                public string Actions { get; set; }
                public string CardIds { get; set; }
                public string Triggers { get; set; }
            }

            public class IdNameModel
            {
                public Guid Id { get; set; }
                public string Name { get; set; }
            }
            public class CardProviderModel
            {
                public Guid Id { get; set; }
                public string Name { get; set; }
                public string ProviderIds { get; set; }
                public bool IsSelected { get; set; }

            }
            public class OrgDeatilModel
            {
                public System.Guid Id { get; set; }
                public System.Guid PlanId { get; set; }
                public string Name { get; set; }
                public string Details { get; set; }
                public bool IsActive { get; set; }
                public System.DateTime CreatedDate { get; set; }
                public Nullable<System.DateTime> PurchasedOn { get; set; }
                public Nullable<System.DateTime> ValidTill { get; set; }
                public bool IsBeta { get; set; }
                public Nullable<System.DateTime> BetaValidTill { get; set; }
                public int AllowedAccounts { get; set; }
                public Nullable<bool> IsPaid { get; set; }
                public string Countries { get; set; }
                public string IDPProviderUrl { get; set; }
                public string IDPSandboxUrl { get; set; }
                public string IDPInstance { get; set; }
                public string IDPKey { get; set; }
            }
            public class OrgModel
            {
                //public System.Guid OrgId { get; set; }
                public string IDPProviderUrl { get; set; }
                public string IDPSandboxUrl { get; set; }
                public string IDPInstance { get; set; }
                public string IDPKey { get; set; }
            }
            public class OrgUserRoleModel
            {
                public string OrgRoleId { get; set; }
                public Guid OrgUserId { get; set; }
                public bool IsActive { get; set; }
                public string Designation { get; set; }
            }
            public class ProvisionsModel
            {
                public System.Guid Id { get; set; }
                public System.Guid OrgId { get; set; }
                public string CountryId { get; set; }
                public string ProviderName { get; set; }
                public string Subscription { get; set; }
                public System.Guid? ProviderId { get; set; }
                public System.Guid? UserId { get; set; }
                public System.Guid? UserProviderId { get; set; }
                public System.DateTime CreatedDate { get; set; }
                public bool IsActive { get; set; }
                public bool IsConverted { get; set; }
                public bool IsEnterpriseConverted { get; set; }
                public bool IsRedeemed { get; set; }
                public string Email { get; set; }
                public string PhoneNumber { get; set; }
                public string Salutation { get; set; }
                public string FirstName { get; set; }
                public string MiddleName { get; set; }
                public string LastName { get; set; }
                public int? PType { get; set; }
                public string Designation { get; set; }
                public string RoleIds { get; set; }
            }

            public class OrgUserModel
            {
                public System.Guid Id { get; set; }
                public System.Guid OrgId { get; set; }
                public string CountryId { get; set; }
                public string Email { get; set; }
                public string PhoneNumber { get; set; }
                public string Salutation { get; set; }
                public string FirstName { get; set; }
                public string MiddleName { get; set; }
                public string LastName { get; set; }
                public int? PType { get; set; }
                public string Designation { get; set; }
                public string RoleIds { get; set; }
                public List<OrgUserProvisionModel> OrgUserProvisionModel { get; set; }
                public OrgUserModel()
                {
                    OrgUserProvisionModel = new List<AdminModels.OrgUserProvisionModel>();
                }
            }

            public class OrgUserProvisionModel
            {
                public string ProviderName { get; set; }
                public string Subscription { get; set; }
                public System.Guid? ProviderId { get; set; }
                public System.Guid? UserProviderId { get; set; }
                public System.DateTime CreatedDate { get; set; }
                public bool IsActive { get; set; }
                public bool IsConverted { get; set; }
                public bool IsEnterpriseConverted { get; set; }
                public bool IsRedeemed { get; set; }
            }
            public class AdminProviderModel
            {
                public System.Guid Id { get; set; }
                public System.Guid ProviderTypeId { get; set; }
                public string ProviderName { get; set; }
                public string ProviderTypeName { get; set; }
                public string Details { get; set; }
                public bool IsActive { get; set; }
                public int ShowSequence { get; set; }
                public bool IsDefaultRole { get; set; }
                public bool IsAuth { get; set; }
                public bool IsProfessional { get; set; }
                public double? Price { get; set; }
                public System.DateTime CreatedDate { get; set; }
                public Nullable<System.DateTime> ModifiedDate { get; set; }
                public string ClientId { get; set; }
                public string ClientSecret { get; set; }
                public string RedirectUrl { get; set; }
                public string Scope { get; set; }
                public string AuthorizeUrl { get; set; }
                public string AuthenticationUrl { get; set; }
                public string ResourceUrl { get; set; }
                public bool IsAdminRequired { get; set; }
                //now added
                //public string InShort { get; set; }
                //public string RequestTokenUrl { get; set; }
                //public string RefreshTokenUrl { get; set; }
                //public string MeUrl { get; set; }
                //public string ContactInfoUrl { get; set; }
                //public string DataInfoURLs { get; set; }
                //public string SentMails { get; set; }
                //public string Mails { get; set; }
                //public string Mail { get; set; }
                //public string Attachments { get; set; }
                //public string Attachment { get; set; }
                //public string Contacts { get; set; }
                //public string RequestMonthlyRange { get; set; }
                //public string RequestYearlyRange { get; set; }
                //public bool IsCloudElement { get; set; }
                //public bool IsXamarinAuth { get; set; }
                //public string WebIdUrl { get; set; }
                //public string MobileIdUrl { get; set; }
                //public string NewLead { get; set; }
                //public string NewContact { get; set; }
                //public string BulkDataUrl { get; set; }
                //public string GoogleIds { get; set; }
                //public string AppleIds { get; set; }
                //public bool IsReady { get; set; }
                //public string IdUrl { get; set; }
                //public bool IsCustomizationRequired { get; set; }
                //public int TrialDays { get; set; }
                //public byte LogInType { get; set; }
                //public byte AuthType { get; set; }
                //public string SuccessURL { get; set; }
                //public string FailureURL { get; set; }
            }
            public class ProviderTypeModel
            {
                public System.Guid Id { get; set; }
                public string Name { get; set; }
                public string Details { get; set; }
                public bool IsActive { get; set; }
                public System.DateTime CreatedDate { get; set; }
                public System.DateTime ModifiedDate { get; set; }
                public int ShowSequence { get; set; }
            }
            public class ProvisionModel
            {
                public Guid ProvisionId { get; set; }
                public string CountryId { get; set; }
                public string FirstName { get; set; }
                public string MiddleName { get; set; }
                public string LastName { get; set; }
                public string Email { get; set; }
                public string PhoneNumber { get; set; }
                public Guid ProviderId { get; set; }
                public Guid IDPProviderId { get; set; }
                public string Salutation { get; set; }
                public string EnterpriseUserId { get; set; }
                public string IDPAppId { get; set; }
                public int PType { get; set; }
            }
            public class AddonModel
            {
                public Guid? AddonId { get; set; }
                public string AddonDescription { get; set; }
            }
            public class PurchaseModel
            {
                public Guid PurchaseId { get; set; }
                public Guid OrgId { get; set; }
                public Guid ProviderId { get; set; }
                public string IDPAppId { get; set; }
                public string Details { get; set; }
                public bool IsActive { get; set; }
                public DateTime CreatedDate { get; set; }
                public DateTime ModifiedDate { get; set; }
                public DateTime PurchasedOn { get; set; }
                public DateTime ValidTill { get; set; }
                public string IDPProviderUrl { get; set; }
                public string IDPSandboxUrl { get; set; }
                public string IDPInstance { get; set; }
                public string IDPKey { get; set; }
                public int? PurchaseCount { get; set; }
                public int? AssignedCount { get; set; }
                public int? RedeemedCount { get; set; }
            }

            public class TriggerModel
            {
                public System.Guid Id { get; set; }
                public string Name { get; set; }
                public string ProviderTypeId { get; set; }
                public string ProviderId { get; set; }
                public Guid? UserProviderId { get; set; }
                public Guid? UserId { get; set; }
                public Guid? OrgRoleId { get; set; }
                public byte TriggerType { get; set; }
                public string DataUrl { get; set; }
                public int ShowSequence { get; set; }
                public bool IsActive { get; set; }
                public bool ShouldTriggerCard { get; set; }
                public Guid? TriggerCardId { get; set; }
            }

            public class ModuleModel
            {
                public Guid Id { get; set; }
                public Guid ProviderId { get; set; }
                public string Name { get; set; }
                public string ReferenceId { get; set; }
                public bool IsActive { get; set; }
            }
            public class UserDeviceModel
            {
                public Guid Id { get; set; }
                public string DeviceData { get; set; }
                public string DeviceType { get; set; }
            }
        }
        public class AuthModels
        {
            public class OrgProvisions
            {
                public Guid ProviderId { get; set; }
                public string ProviderName { get; set; }
                public Guid ProvisionId { get; set; }
                public string Email { get; set; }
                public Guid OrgId { get; set; }
                public string OrgName { get; set; }
                public string AuthUrl { get; set; }
            }
            public class PreloginReturnModel
            {
                public bool IsExisting { get; set; }
                public string AuthUrl { get; set; }
            }
            public class PreloginReturnModel2
            {
                public int UserType { get; set; }
                public string AuthUrl { get; set; }
                public string SupportPhone { get; set; }
                public string SupportEmail { get; set; }
                public bool IsLoginAllowed { get; set; }
                public bool IsPreAuthenticated { get; set; }
                public bool IsInvalidCoupon { get; set; }
                public bool IsFreeUser { get; set; }
                public bool NotificarLoginBlocked { get; set; }
                public int LoginType { get; set; }
                public bool IsSSO { get; set; }
                public string SSOURL { get; set; }
                public bool IsEmailValid { get; set; }
            }
            public class CountryModel
            {
                public int Id { get; set; }
                public string Name { get; set; }
                public string Alpha2 { get; set; }
                public string Alpha3 { get; set; }
                public string Prefix { get; set; }
                public string IntlPrefix { get; set; }
            }
            public class AuthUserModel
            {
                [Required(ErrorMessage = "required phone number")]
                public string CountryId { get; set; }
                [Required(ErrorMessage = "required phone number")]
                public string PhoneNumber { get; set; }
                [Required(ErrorMessage = "required EmailAddress")]
                public string EmailAddress { get; set; }
                public bool IsPreAuthenticated { get; set; }
                public string Password { get; set; }
                public string FirstName { get; set; }
                public string LastName { get; set; }
                public string Coupon { get; set; }
                [Required(ErrorMessage = "required AppId")]
                public Guid AppId { get; set; }
                public bool IsForced { get; set; }
                public Guid DeviceId { get; set; }
                public Guid DeviceType { get; set; }
                public Guid LoginFlowId { get; set; }
                public string TimeZone { get; set; } = "";
            }
            public class SocialReturnModel
            {
                public string FirstName { get; set; }
                public string LastName { get; set; }
                public string EmaiId { get; set; }
                public string ProfileId { get; set; }
                public string DisplayImage { get; set; }
                public string Country { get; set; }
                public bool IsRegiestered { get; set; }
                public string Token { get; set; }

            }
            public class AuthUserModel2
            {
                // [Required(ErrorMessage = "required phone number")]
                public string CountryId { get; set; }
                // [Required(ErrorMessage = "required phone number")]
                public string PhoneNumber { get; set; }
                // [Required(ErrorMessage = "required EmailAddress")]
                public string EmailAddress { get; set; }
                public bool IsPreAuthenticated { get; set; }
                public string Password { get; set; }
                public string Coupon { get; set; }
                //   [Required(ErrorMessage = "required AppId")]
                public Guid AppId { get; set; }
                public string FirstName { get; set; }
                public string LastName { get; set; }
                public bool IsForced { get; set; }
            }

            public class AuthUserModel3
            {
                public string CountryId { get; set; }
                public string PhoneNumber { get; set; }
                public string EmailAddress { get; set; }
                public string Coupon { get; set; }
                public Guid AppId { get; set; }
                public Guid DeviceId { get; set; }
                public Guid DeviceType { get; set; }
                public bool IsForced { get; set; }
                public Guid LoginFlowId { get; set; }
            }

            public class ValidationModel
            {
                public string Coupon { get; set; }
                public Guid DeviceId { get; set; }
                public string LanguageId { get; set; }
                public Guid DeviceTypeId { get; set; }
                public string OTP { get; set; }
                public string CountryId { get; set; }
                public string PhoneNumber { get; set; }
                public string EmailAddress { get; set; }
                public bool IsPrimaryDevice { get; set; }
                public bool IsPreAuthenticated { get; set; }
                public Guid AppId { get; set; }
                public string DeviceData { get; set; }
            }

            public class UserValidModel
            {
                public string Email { get; set; }
                public string AuthUrl { get; set; }
                public bool IsValid { get; set; }
                public Guid UserId { get; set; }
                public Guid AppId { get; set; }
            }

            public class IntercomModel
            {
                public string role { get; set; }
                public string email { get; set; }
                public string phone { get; set; }
                public string name { get; set; }
            }
            public class ValidateReturnModel
            {
                public Guid TokenId { get; set; }
                public Guid UserId { get; set; }
                public Guid AnalyticsId { get; set; }
                public Guid UserContactId { get; set; }
                public int NotificationCount { get; set; }
                public UserStatusModel UserStatusModel { get; set; }
                public string LanguageId { get; set; }
                public bool IsTestUser { get; set; }
                public bool? UnknownPush { get; set; }
                public bool? ContactPush { get; set; }
                public bool? CRMPush { get; set; }
                public bool? DirectoryPush { get; set; }
                public bool CallNotifications { get; set; }
                public bool CalendarNotifications { get; set; }
                public string DeviceData { get; set; }
                public bool IncomingCall { get; set; }
                public bool IncomingCallEnd { get; set; }
                public bool OutgoingCall { get; set; }
                public bool OutgoingCallEnd { get; set; }
                public string ExotelNumber { get; set; }
                public string ExotelExtension { get; set; }
                public List<ServerUrlModel> ServerUrlModel { get; set; }
                public List<PhoneConfig> phoneConfigsModel { get; set; }
                public List<Guid> OrgIds { get; set; }
                public bool IsInternal { get; set; }
                public Guid WebToken { get; set; }
                public string FirstName { get; set; }
                public string LastName { get; set; }
                public string InvitedBy { get; set; }
                public Guid WebhookToken { get; set; }
                public ValidateReturnModel()
                {
                    UserStatusModel = new UserStatusModel();
                    ServerUrlModel = new List<ServerUrlModel>();
                    phoneConfigsModel = new List<PhoneConfig>();
                    OrgIds = new List<Guid>();
                }
            }
            public class PhoneConfig
            {
                public string DID { get; set; }
                public string PhoneNumber { get; set; }
                public string Extn { get; set; }
                public Guid? ProviderId { get; set; }
                public string SIPAddress { get; set; }
                public string Prefix { get; set; }
                public string InstanceUrl { get; set; }
                public string ApiKey { get; set; }
                public string Name { get; set; }

            }
            public class ServerUrlModel
            {
                public int ServerType { get; set; }
                public string url { get; set; }
            }
            public class UserStatusModel
            {
                public System.Guid Id { get; set; }
                public System.Guid UserId { get; set; }
                public bool Walkthrough { get; set; }
                public bool MobileVerification { get; set; }
                public bool OTP { get; set; }
                public bool Registration { get; set; }
                public bool AndroidContacts { get; set; }
                public bool iOSContacts { get; set; }
                public bool FacebookContacts { get; set; }
                public bool LinkedInContacts { get; set; }
                public bool TwitterContacts { get; set; }
                public bool FreeEmail { get; set; }
                public bool FreeStorage { get; set; }
                public bool FreeFacebook { get; set; }
                public bool FreeTwitter { get; set; }
                public bool FreeLinkedIn { get; set; }
                public bool PurchaseEmail { get; set; }
                public bool PurchaseStorage { get; set; }
                public bool PurchaseEnterpriseEmail { get; set; }
                public bool PurchaseEnterpriseStorage { get; set; }
                public bool PurchaseEnterpriseApp { get; set; }
                public bool ProvisionApp { get; set; }
                public bool ShouldShowAds { get; set; }
                public bool IsVirtualOnly { get; set; }
                public bool IsFree { get; set; }
                public bool IsAppSumo { get; set; }
            }
        }

        public class CallModels
        {
            public class CallModel
            {
                public int CallType { get; set; }
                public string PhoneNumber { get; set; }
                public Guid CallId { get; set; }
                public Guid ContactId { get; set; }
                public bool IsIncoming { get; set; }
                public string Name { get; set; }
                public string GroupId { get; set; }
                public string DeviceId { get; set; }
            }
            public class PushNotificationModel
            {
                public string Id { get; set; }
                public int Recipients { get; set; }
            }
        }

        public class DataModels
        {
            public class MeetingReminderModel
            {
                public int MeetingStartReminder { get; set; }
                public int MeetingEndReminder { get; set; }
            }
            public class NewProviderModel
            {
                public System.Guid? ProviderId { get; set; }
                public Guid ProviderTypeId { get; set; }
                public string OrgName { get; set; }
                public string ProviderName { get; set; }
                public string ProviderTypeName { get; set; }
                public bool IsAdminRequired { get; set; }
                public bool IsReady { get; set; }
                public bool? IsCustomizationRequired { get; set; }
                public int TrialDays { get; set; }
                public int AuthType { get; set; }
                public int ProviderShowSequence { get; set; }
                public int ShowSequence { get; set; }
                public string PageUrl { get; set; }
                public string Prefix { get; set; }
                public bool CheckBox { get; set; }
                public string AdvancedSearch { get; set; }
                public string Domains { get; set; }
                public string RelatedProviders { get; set; }
                public bool IsUserSettingsRequired { get; set; }
                public bool ProviderVisibility { get; set; }
                public bool IsCustomLoginButton { get; set; }
                public bool IsLocal { get; set; }
                public List<URLModel> UrlModel { get; set; }
                public List<LoggedinAccountsModel> LoggedinAccountsModel { get; set; }
                public List<NotLoggedinAccountsModel> NotLoggedinAccountsModel { get; set; }
                public List<NotLoggedinAccountsModel> ClaimedAccountsModel { get; set; }
                public List<IdentifiersModel> SettingsIdentifiersModel { get; set; }
                public NewProviderModel()
                {
                    LoggedinAccountsModel = new List<LoggedinAccountsModel>();
                    NotLoggedinAccountsModel = new List<NotLoggedinAccountsModel>();
                    ClaimedAccountsModel = new List<NotLoggedinAccountsModel>();
                    SettingsIdentifiersModel = new List<IdentifiersModel>();
                    UrlModel = new List<URLModel>();
                }
            }
            public class IdentifiersModel
            {
                public Guid IId { get; set; }
                public string Name { get; set; }
                public string Value { get; set; }
            }

            public class URLModel
            {
                public Guid UrlId { get; set; }
                public string UrlTypeName { get; set; }
                public string Url { get; set; }
                public bool IsApi { get; set; }
                public int HttpType { get; set; }
                public bool HasHeaders { get; set; }
                public int ShowOrder { get; set; }
                public string SuccessUrl { get; set; }
                public string FailureUrl { get; set; }
                public string Identifier { get; set; }
                public string FieldsTopick { get; set; }
                public string DataPath { get; set; }
                public Guid CardId { get; set; }
                public bool IsMultiUrl { get; set; }
                public List<HeaderModel> HeaderModel { get; set; }
                public PostModel PostModel { get; set; }
                public URLModel()
                {
                    PostModel = new PostModel();
                    HeaderModel = new List<HeaderModel>();
                }
            }
            public class PostModel
            {
                public System.Guid Id { get; set; }
                public string Name { get; set; }
            }
            public class HeaderModel
            {
                public Guid HeaderId { get; set; }
                public string Name { get; set; }
                public string Prefix { get; set; }
            }
            public class LoggedinAccountsModel
            {
                public Guid UserProviderId { get; set; }
                public Guid OrgId { get; set; }
                public string OrgName { get; set; }
                public Guid? ProvisionId { get; set; }
                public string EmailAddress { get; set; }
                public bool IsPaid { get; set; }
                public bool IsProvisioned { get; set; }
                public bool? IsTrial { get; set; }
                public int TrialDaysLeft { get; set; }
                public string SubscriptionId { get; set; }
                public string ProductId { get; set; }
                public int count { get; set; }
                public bool IsAccountActive { get; set; }
                public bool IsReloginRequired { get; set; }
                public bool IsClaimed { get; set; }
                public List<IdentifiersModel> IdentifiersModel { get; set; }
                public List<Guid> URLModel { get; set; }
                public LoggedinAccountsModel()
                {
                    URLModel = new List<Guid>();
                    IdentifiersModel = new List<IdentifiersModel>();
                }
            }
            public class NotLoggedinAccountsModel
            {
                public Guid OrgId { get; set; }
                public string OrgName { get; set; }
                public Guid ProvisionId { get; set; }
                public string EmailAddress { get; set; }
                public bool IsPaid { get; set; }
                public bool IsProvisioned { get; set; }
                public bool? IsTrial { get; set; }
                public int TrialDaysLeft { get; set; }
                public string SubscriptionId { get; set; }
                public string ProductId { get; set; }
                public bool IsClaimed { get; set; }
                public bool IsFree { get; set; }
                public List<IdentifiersModel> IdentifiersModel { get; set; }
                public List<Guid> URLModel { get; set; }
                public NotLoggedinAccountsModel()
                {
                    URLModel = new List<Guid>();
                    IdentifiersModel = new List<IdentifiersModel>();
                }
            }

            public class ProviderModel
            {
                public Guid OrgId { get; set; }
                public string OrgName { get; set; }
                public Guid ProviderId { get; set; }
                public string ProviderName { get; set; }
                public Guid ProviderTypeId { get; set; }
                public string ProviderTypeName { get; set; }
                public int TrialDays { get; set; }
                public int LoggedinAccCount { get; set; }
                public bool? IsCustomizationRequired { get; set; }
                public int ProviderShowSequence { get; set; }
                public int ShowSequence { get; set; }
                public bool ProviderVisibility { get; set; }
            }
            public enum UserProviderStatus
            {
                NewProvider, Processing, FirstPassProcessed, NextPassProcessing, NextPassProcessed
            }
            public class CardDetailModel
            {
                public Guid Id { get; set; }
                public string Title { get; set; }
                public bool ShowCounter { get; set; }
                public string Icon { get; set; }
                public bool ShowMore { get; set; }
                public bool IsAggregate { get; set; }
                public bool IsOrgSpecific { get; set; }
                public int? ChartType { get; set; }
                public bool HasActions { get; set; }
                public bool CanExpand { get; set; }
                public int ShowSequence { get; set; }
                public bool? IsGlobal { get; set; }
                public bool? ShowHeader { get; set; }
                public bool? ShowFooter { get; set; }
                public string ActionName { get; set; }
                public string ActionCommand { get; set; }
                public List<ActionModel> Actions { get; set; }
                public int MaxHeight { get; set; }
                public int CardType { get; set; }
                public bool IsHome { get; set; }
                public string ProviderTypeId { get; set; }
                public string ProviderId { get; set; }
                public string FieldsToShow { get; set; }
                public bool IsExtUrl { get; set; }
                public string DataPath { get; set; }
                public List<Guid> UrlIds { get; set; }
                public string Entity { get; set; }
                public string DetailPath { get; set; }
                public List<FieldsToDisplayModel> FieldsToDisplay { get; set; }
                public string Model { get; set; }
                public bool IsRepetitive { get; set; }
                public bool IsDemo { get; set; }
                public bool ShouldShowDetails { get; set; }
                public string HelpUrl { get; set; }
                public int PageType { get; set; }
                public CardDetailModel()
                {
                    UrlIds = new List<Guid>();
                    Actions = new List<ActionModel>();
                    FieldsToDisplay = new List<FieldsToDisplayModel>();
                }
            }

            public class FieldsToDisplayModel
            {
                public Guid Id { get; set; }
                public string DisplayName { get; set; }
                public string Icon { get; set; }
                public string NameMatch { get; set; }
                public string ValueMatch { get; set; }
                public int Type { get; set; }
                public bool IsWearable { get; set; }
                public bool IsMobile { get; set; }
                public bool IsWebApp { get; set; }
                public bool IsLink { get; set; }
                public string Condition { get; set; }
                public string ModelAttribute { get; set; }
                public int ShowSequence { get; set; }
                public string Entity { get; set; }
                public bool IsDirect { get; set; }
                public bool IsDownload { get; set; }
            }
            public class ActionModel
            {
                public Guid Id { get; set; }
                public string Title { get; set; }
                public string Icon { get; set; }
                public bool IsAggregate { get; set; }
                public int ShowSequence { get; set; }
                public string Command { get; set; }
                public List<Guid> UrlIds { get; set; }
                public string CommandParameter { get; set; }
                public bool? IsPostCallAction { get; set; }
                public bool IsVisible { get; set; }
                public string VisibilityConditions { get; set; }
                public Guid? FollowUpAction { get; set; }
                public int Type { get; set; }
                public ActionModel()
                {
                    UrlIds = new List<Guid>();
                }
            }

            public class TriggerDetailModel
            {
                public System.Guid Id { get; set; }
                public string Name { get; set; }
                public string ProviderTypeId { get; set; }
                public string ProviderId { get; set; }
                public Guid? UserProviderId { get; set; }
                public Guid? OrgId { get; set; }
                public Guid? UserId { get; set; }
                public Guid? OrgRoleId { get; set; }
                public int TriggerType { get; set; }
                public string DataUrl { get; set; }
                public int ShowSequence { get; set; }
                public bool IsActive { get; set; }
                public bool ShouldTriggerCard { get; set; }
                public Guid? TriggerCardId { get; set; }
                public Guid? ModuleId { get; set; }
                public Guid? ActionId { get; set; }
                public List<TriggerActionModel> Actions { get; set; }
                public TriggerDetailModel()
                {
                    Actions = new List<TriggerActionModel>();
                }
            }
            public class TriggerActionModel
            {
                public Guid Id { get; set; }
                public string Title { get; set; }
                public string Icon { get; set; }
                public bool IsAggregate { get; set; }
                public int ShowSequence { get; set; }
                public string Command { get; set; }
                // public bool? IsExtUrl { get; set; }
                // public Guid UrlId { get; set; }
                public string CommandParameter { get; set; }
                public bool? IsPostCallAction { get; set; }
                public Guid? FollowUpAction { get; set; }
                public int Type { get; set; }
                public string VisibilityConditions { get; set; }
                public List<Guid> UrlIds { get; set; }
                public TriggerActionModel()
                {
                    UrlIds = new List<Guid>();
                }
            }

            public class ActivityLogModel
            {
                public string Id { get; set; }
                public System.Guid UserId { get; set; }
                public int ActivityTypeId { get; set; }
                public string ContactId { get; set; }
                public string PhoneNumber { get; set; }
                public bool IsIdentified { get; set; }
                public bool IsContact { get; set; }
                public Nullable<bool> IsCurrentCall { get; set; }
                public string Notes { get; set; }
                public bool IsActive { get; set; }
                public bool IsIncoming { get; set; }
                public string ContactName { get; set; }
                public System.DateTimeOffset CallStartTime { get; set; }
                public System.DateTimeOffset CallEndTime { get; set; }
                public byte[] Version { get; set; }
                public System.DateTimeOffset CreatedAt { get; set; }
                public Nullable<System.DateTimeOffset> UpdatedAt { get; set; }
                public bool Deleted { get; set; }
            }

            public class AllProviderListModel
            {
                public System.Guid? Id { get; set; }
                // public Guid ProvisionId { get; set; }
                public Guid ProviderTypeId { get; set; }
                public string Name { get; set; }
                public string ProviderTypeName { get; set; }
                public string RedirectUrl { get; set; }
                public string ResourceUrl { get; set; }
                public string RefreshTokenUrl { get; set; }
                public string ClientId { get; set; }
                public string AuthorizeUrl { get; set; }
                public string ClientSecret { get; set; }
                public bool IsRedeemed { get; set; }
                public bool IsFree { get; set; }
                public bool IsSocial { get; set; }
                public bool IsPaid { get; set; }
                public string EmailId { get; set; }
                public string Scope { get; set; }
                public string AccessTokenUrl { get; set; }
                public string RequestTokenUrl { get; set; }
                public string MeUrl { get; set; }
                public string RequestMonthlyRange { get; set; }
                public string RequestYearlyRange { get; set; }
                public double? YearlyPrice { get; set; }
                public double? MonthlyPrice { get; set; }
                public bool IsActive { get; set; }
                public bool IsLocked { get; set; }
                public bool IsAuth { get; set; }
                public bool IsAdminRequired { get; set; }
                public string Mails { get; set; }
                public string Attachments { get; set; }
                public string SentMails { get; set; }
                public string GoogleIds { get; set; }
                public string AppleIds { get; set; }
                public string IdUrl { get; set; }
                public bool IsReady { get; set; }
                public bool? IsCustomizationRequired { get; set; }
                public int TrialDays { get; set; }
                public byte? AuthType { get; set; }
                public string SuccessURL { get; set; }
                public string FailureURL { get; set; }
                public int ShowSequence { get; set; }
                public List<CurrentUserRedeemedModel> currentUserRedeemedModel { get; set; }
                public List<CurrentUserNotRedeemedModel> currentUserNotRedeemedModel { get; set; }
                public List<CurrentUserNotRedeemedModel> currentUserPurchaseModel { get; set; }
                public AllProviderListModel()
                {
                    currentUserRedeemedModel = new List<CurrentUserRedeemedModel>();
                    currentUserNotRedeemedModel = new List<CurrentUserNotRedeemedModel>();
                    currentUserPurchaseModel = new List<CurrentUserNotRedeemedModel>();
                }
            }
            public class CurrentUserRedeemedModel
            {
                public System.Guid Id { get; set; }
                public string Account { get; set; }
                public string AccountId { get; set; }
                public string AccessToken { get; set; }
                public string RefereshToken { get; set; }
                public string ProfileUrl { get; set; }
                public bool IsFree { get; set; }
                public bool IsPaid { get; set; }
                public bool IsProvisioned { get; set; }
                public bool IsRedeemed { get; set; }
                public Guid? ProvisionId { get; set; }
                public string SubscriptionId { get; set; }
                public string ProductId { get; set; }
                public string InstanceURL { get; set; }
                public string WebIdUrl { get; set; }
                public string MobileIdUrl { get; set; }
                public DateTime? TrialStart { get; set; }
                public DateTime? TrialEnd { get; set; }
                public bool? IsTrial { get; set; }
                public bool IsAccountActive { get; set; }
                public int count { get; set; }
            }

            public class CurrentUserNotRedeemedModel
            {
                public System.Guid Id { get; set; }
                public string Account { get; set; }
                public bool IsFree { get; set; }
                public bool IsPaid { get; set; }
                public bool IsProvisioned { get; set; }
                public bool IsRedeemed { get; set; }
                public Guid? ProvisionId { get; set; }
                public string ImapPort { get; set; } = "";
                public string ImapServer { get; set; } = "";
                public int? ImapSSL { get; set; } = 0;
                public string ProductId { get; set; }
            }
            public class AllProviderListModel2
            {
                public System.Guid? Id { get; set; }
                // public Guid ProvisionId { get; set; }
                public Guid ProviderTypeId { get; set; }
                public string Name { get; set; }
                public string ProviderTypeName { get; set; }
                public string RedirectUrl { get; set; }
                public string ResourceUrl { get; set; }
                public string RefreshTokenUrl { get; set; }
                public string ClientId { get; set; }
                public string AuthorizeUrl { get; set; }
                public string ClientSecret { get; set; }
                public bool IsRedeemed { get; set; }
                public bool IsFree { get; set; }
                public bool IsSocial { get; set; }
                public bool IsPaid { get; set; }
                public string EmailId { get; set; }
                public string Scope { get; set; }
                public string AccessTokenUrl { get; set; }
                public string RequestTokenUrl { get; set; }
                public string MeUrl { get; set; }
                public string RequestMonthlyRange { get; set; }
                public string RequestYearlyRange { get; set; }
                public double? YearlyPrice { get; set; }
                public double? MonthlyPrice { get; set; }
                public bool IsActive { get; set; }
                public bool IsLocked { get; set; }
                public bool IsAuth { get; set; }
                public bool IsAdminRequired { get; set; }
                public string Mails { get; set; }
                public string Attachments { get; set; }
                public string SentMails { get; set; }
                public string GoogleIds { get; set; }
                public string AppleIds { get; set; }
                public string IdUrl { get; set; }
                public bool IsReady { get; set; }
                public bool? IsCustomizationRequired { get; set; }
                public int TrialDays { get; set; }
                public byte? AuthType { get; set; }
                public string SuccessURL { get; set; }
                public string FailureURL { get; set; }
                public int ShowSequence { get; set; }
                public List<CurrentUserRedeemedModel> currentUserRedeemedModel { get; set; }
                public List<CurrentUserNotRedeemedModel> currentUserNotRedeemedModel { get; set; }
                public List<CurrentUserNotRedeemedModel> currentUserPurchaseModel { get; set; }
                public AllProviderListModel2()
                {
                    currentUserRedeemedModel = new List<CurrentUserRedeemedModel>();
                    currentUserNotRedeemedModel = new List<CurrentUserNotRedeemedModel>();
                    currentUserPurchaseModel = new List<CurrentUserNotRedeemedModel>();
                }
            }

            public class UserRequestModel
            {
                public Guid ProviderId { get; set; }
                public double MonthlyPrice { get; set; }
                public double YearlyPrice { get; set; }
            }
            public class UserProvisionModel
            {
                public string SubscriptionId { get; set; }
                public DateTime PurchasedDate { get; set; }
                public DateTime DueDate { get; set; }
                public Guid ProviderId { get; set; }
            }
            public class LanguageModel
            {
                public int Id { get; set; }
                public string Name { get; set; }
                public string Locale { get; set; }
                public int ShowSequence { get; set; }
            }
            public class HelpDataModel
            {
                public System.Guid Id { get; set; }
                public string Name { get; set; }
                public string Url { get; set; }
                public string Locale { get; set; }
                public string DeviceType { get; set; }
                public string ProviderId { get; set; }
                public string ProviderTypeId { get; set; }
                public string UserId { get; set; }
                public string OrgId { get; set; }
            }
            public class ContextioContactModel
            {
                public string email { get; set; }
                public int count { get; set; }
                public int sent_count { get; set; }
                public int received_count { get; set; }
                public int sent_from_account_count { get; set; }
                public string resource_url { get; set; }
                public int? last_sent { get; set; }
                public int? last_received { get; set; }
                public string name { get; set; }
            }
            //public class UserProviderModel
            //{
            //    public System.Guid ProviderId { get; set; }
            //    public string AuthToken { get; set; }
            //    public string RefreshToken { get; set; }
            //    public string Code { get; set; }
            //    public bool IsActive { get; set; }
            //    public string EmailAddress { get; set; }
            //    public string Alias { get; set; }
            //    public string MailboxGuid { get; set; }
            //    public string AccountId { get; set; }
            //    public bool IsProvisioned { get; set; }
            //    public bool IsPayed { get; set; }
            //    public bool IsFree { get; set; }
            //    public Guid? ProvisionId { get; set; }
            //    public string IDPProviderUrl { get; set; }
            //    public string IDPProviderInstance { get; set; }
            //    public string ReferenceId { get; set; }
            //    public string ProfileUrl { get; set; }
            //    public string DisplayName { get; set; }
            //    public DateTime TrialStart { get; set; }
            //    public DateTime TrialEnd { get; set; }
            //    public bool IsTrial { get; set; }
            //}
            public class UPIdentifierModel
            {
                public System.Guid UserProviderId { get; set; }
                public List<IdentifiersModel> IdentifiersModel { get; set; }
                public UPIdentifierModel()
                {
                    IdentifiersModel = new List<IdentifiersModel>();
                }
            }
            public class UserProviderModel
            {
                public System.Guid ProviderId { get; set; }
                public bool IsActive { get; set; }
                public string EmailAddress { get; set; }
                public string Alias { get; set; }
                public string AccountId { get; set; }
                public bool IsProvisioned { get; set; }
                public bool IsPayed { get; set; }
                public bool IsFree { get; set; }
                public bool IsTrial { get; set; }
                public bool IsClaimed { get; set; }
                public Guid? ProvisionId { get; set; }
                public string DisplayName { get; set; }
                public DateTime TrialStart { get; set; }
                public DateTime TrialEnd { get; set; }
                public List<UserProviderIdentifiersModel> IdentifiersModel { get; set; }
                public UserProviderModel()
                {
                    IdentifiersModel = new List<UserProviderIdentifiersModel>();
                }
            }
            public class UserProviderIdentifiersModel
            {
                public Guid IId { get; set; }
                public string Name { get; set; }
                public string Value { get; set; }
            }

            public class UserProviderSettingsModel
            {
                public Guid UPId { get; set; }
                public List<IdentifiersModel> IdentifiersModel { get; set; }
                public UserProviderSettingsModel()
                {
                    IdentifiersModel = new List<IdentifiersModel>();
                }
            }
            public class ProviderTypeModel
            {
                public System.Guid Id { get; set; }
                public string Name { get; set; }
                public string Details { get; set; }
                public bool IsActive { get; set; }
                public System.DateTime CreatedDate { get; set; }
                public System.DateTime ModifiedDate { get; set; }
                public int ShowSequence { get; set; }
            }
            public class DataProviderModel
            {
                public System.Guid Id { get; set; }
                public Guid ProvisionId { get; set; }
                public string Name { get; set; }
                public string RedirectUrl { get; set; }
                public string ResourceUrl { get; set; }
                public string RefreshTokenUrl { get; set; }
                public string ClientId { get; set; }
                public string AuthorizeUrl { get; set; }
                public string ClientSecret { get; set; }
                public bool IsRedeemed { get; set; }
                public bool IsProvisioned { get; set; }
                public string EmailId { get; set; }
                public string Scope { get; set; }
                public string AccessTokenUrl { get; set; }
                public string RequestTokenUrl { get; set; }
                public string MeUrl { get; set; }
                public List<CurrentUserRedeemedModel> currentUserCountList { get; set; }
                public DataProviderModel()
                {
                    currentUserCountList = new List<CurrentUserRedeemedModel>();
                }
            }
            public class CRMUserProviderModel
            {
                public Guid Id { get; set; }
                public Guid ProviderId { get; set; }
                public Guid ProviderTypeId { get; set; }
                public string ProviderName { get; set; }
                public string ProviderTypeName { get; set; }
                public string CEUserId { get; set; }
                public string CEOrgId { get; set; }
                public string CEElementId { get; set; }
                public string CEInstanceUrl { get; set; }
                public string WebIdUrl { get; set; }
                public string MobileIdUrl { get; set; }
                public string NewLead { get; set; }
                public string NewContact { get; set; }
            }
            public class LoggedInAccounts
            {
                public Guid Id { get; set; }
                public Guid ProviderId { get; set; }
                public string RefreshToken { get; set; }
                public string EmailAddress { get; set; }
                public bool IsProvisioned { get; set; }
                public bool IsPayed { get; set; }
                public bool IsFree { get; set; }
                public Guid? ProvisionId { get; set; }
                public string DisplayName { get; set; }
            }
            public class IDPListModel
            {
                public System.Guid ProviderId { get; set; }
                public string IDPName { get; set; }
            }
            public class OrgUserModel
            {
                public List<string> PhoneNumbers { get; set; }
                public List<string> Emails { get; set; }
                public string DisplayName { get; set; }
                public string Designation { get; set; }
                public string Company { get; set; }
                public Guid OrgId { get; set; }
                public OrgUserModel()
                {
                    PhoneNumbers = new List<string>();
                    Emails = new List<string>();
                }
            }

            //public class UserStatusModel
            //{
            //    public int LanguageId { get; set; }
            //    public bool Walkthrough { get; set; }
            //    public bool MobileVerification { get; set; }
            //    public bool OTP { get; set; }
            //    public bool Registration { get; set; }
            //    public bool AndroidContacts { get; set; }
            //    public bool iOSContacts { get; set; }
            //    public bool FacebookContacts { get; set; }
            //    public bool LinkedInContacts { get; set; }
            //    public bool TwitterContacts { get; set; }
            //    public bool FreeEmail { get; set; }
            //    public bool FreeStorage { get; set; }
            //    public bool FreeFacebook { get; set; }
            //    public bool FreeTwitter { get; set; }
            //    public bool FreeLinkedIn { get; set; }
            //    public bool PurchaseEmail { get; set; }
            //    public bool PurchaseStorage { get; set; }
            //    public bool PurchaseEnterpriseEmail { get; set; }
            //    public bool PurchaseEnterpriseStorage { get; set; }
            //    public bool PurchaseEnterpriseApp { get; set; }
            //    public bool ProvisionApp { get; set; }
            //    public bool IsDrakTheme { get; set; }
            //    public bool ShouldShowAds { get; set; }
            //}

            public class UserDetailModel
            {
                public Guid UserId { get; set; }
                public string Email { get; set; }
                public string Salutation { get; set; }
                public string FirstName { get; set; }
                public string MiddleName { get; set; }
                public string LastName { get; set; }
                public string Designation { get; set; }
                public string Company { get; set; }
                public int Gender { get; set; }
                public Guid AppId { get; set; }
            }
            public class PushSettingsModel
            {
                public Guid UserDeviceId { get; set; }
                public bool UnknownPush { get; set; }
                public bool ContactPush { get; set; }
                public bool CRMPush { get; set; }
                public bool DirectoryPush { get; set; }
                public bool CallNotifications { get; set; }
                public bool CalendarNotifications { get; set; }
            }
            public class RecieptModel
            {
                public Guid ProvisionId { get; set; }
                public string emailId { get; set; }
                public int Source { get; set; }
                public string Id { get; set; }
                public DateTime TransactionDateUtc { get; set; }
                public string ProductId { get; set; }
                public bool AutoRenewing { get; set; }
                public string PurchaseToken { get; set; }
                public int State { get; set; }
                public int ConsumptionState { get; set; }
                public string Payload { get; set; }
            }
            public class AttachmentDetails
            {
                public string id { get; set; }
                public string lastModifiedDateTime { get; set; }
                public string name { get; set; }
                public string contentType { get; set; }
                public int size { get; set; }
                public string contentBytes { get; set; }
            }

            public class AppSettingsModel
            {
                public string Name { get; set; }
                public string LanguagesToShow { get; set; }
                public bool ShowAds { get; set; }
                public bool ShowImportContacts { get; set; }
                public bool ShowSocialProviders { get; set; }
                public bool ShowNotifications { get; set; }
                public bool ShowRecentCalls { get; set; }
                public bool ShowUnknownCalls { get; set; }
                public bool ShowLanguage { get; set; }
                public bool ShowTheme { get; set; }
                public bool ShowPermissions { get; set; }
                public bool ShowPushNotifications { get; set; }
                public bool ShowRedeem { get; set; }
            }
            public class SocialTypeModel
            {
                public Guid Id { get; set; }
                public string Name { get; set; }
                public string Icon { get; set; }
                public string SearchUrl { get; set; }
                public string LookUpUrl { get; set; }
                public int ShowSequence { get; set; }
                public string SearchFor { get; set; }
            }
            public class ErrorMailModel
            {
                public string Url { get; set; }
                public string Payload { get; set; }
                public string ErrorCode { get; set; }
                public Guid UserProviderId { get; set; }
                public Guid ProvisionId { get; set; }
                public string ErrorMessage { get; set; }
                public bool IsMailSent { get; set; }
                public int Count { get; set; }
                public DateTime CreatedDate { get; set; }
                public string Email { get; set; }
            }
            public class UserDeviceModel
            {
                public System.Guid Id { get; set; }
                public string DeviceData { get; set; }
                public string DeviceType { get; set; }
            }
        }
        public class ContactDataModel
        {
            public class UserDeviceIdModel
            {
                public Guid UserDeviceId { get; set; }
                public Guid Token { get; set; }
            }

            public class ContactsByUserProviderModel
            {
                public Guid UserProviderId { get; set; }
                public Guid UserDeviceId { get; set; }
                public Guid Token { get; set; }
            }
            public class ErrorMailModel2
            {
                public string Url { get; set; }
                public string Payload { get; set; } = "";
                public string ErrorCode { get; set; }
                public Guid UserProviderId { get; set; }
                public Guid ProvisionId { get; set; }
                public string ErrorMessage { get; set; }
                public bool IsMailSent { get; set; }
                public int Count { get; set; }
                public DateTime CreatedDate { get; set; }
                public string EmailId { get; set; }
                public string Method { get; set; }
            }
            public class CreditEmailModel
            {
                public string OrgName { get; set; }
                public string Credits { get; set; }
            }
            public class CouponRedeemInfoModel
            {
                public string EmailId { get; set; }
                public string Coupon { get; set; }
            }
            public class UserLoginInfoModel
            {
                public UserLoginInfoModel()
                {
                    ToList = new List<string>();
                }
                public string EmailId { get; set; }
                public string Heading { get; set; }
                public string Message { get; set; }
                public List<string> ToList { get; set; }
                public string From { get; set; }
                public string Subject { get; set; }
            }
            public class TaskCreditModel
            {
                public string EmailId { get; set; }
                public string OtherUserEmailId { get; set; }
                public string Message { get; set; }
            }
            public class SFTokenModel
            {
                public string token_type { get; set; }
                public string scope { get; set; }
                public int expires_in { get; set; }
                public int ext_expires_in { get; set; }
                public string access_token { get; set; }
                public string refresh_token { get; set; }
                public string instance_url { get; set; }
            }
            public class HeaderModel
            {
                public Guid HeaderId { get; set; }
                public string Name { get; set; }
                public string Prefix { get; set; }
            }
            public class IdentifiersModel
            {
                public Guid IId { get; set; }
                public string Name { get; set; }
                public string Value { get; set; }
            }
            public class UPIdentifierModel
            {

                public Guid UserProviderId { get; set; }
                public List<IdentifiersModel> IdentifiersModel { get; set; }
                public UPIdentifierModel()
                {
                    IdentifiersModel = new List<IdentifiersModel>();
                }
            }
        }
        public class ContactLinkingModel
        {
            public class UserDeviceIdModel
            {
                public Guid UserDeviceId { get; set; }
                public Guid Token { get; set; }
            }

            public class ContactsByUserProviderModel
            {
                public Guid UserProviderId { get; set; }
                public Guid UserDeviceId { get; set; }
                public Guid Token { get; set; }
            }
            public class ErrorMailModel2
            {
                public string Url { get; set; }
                public string Payload { get; set; } = "";
                public string ErrorCode { get; set; }
                public Guid UserProviderId { get; set; }
                public Guid ProvisionId { get; set; }
                public string ErrorMessage { get; set; }
                public bool IsMailSent { get; set; }
                public int Count { get; set; }
                public DateTime CreatedDate { get; set; }
                public string EmailId { get; set; }
                public string Method { get; set; }
            }
            public class SFTokenModel
            {
                public string token_type { get; set; }
                public string scope { get; set; }
                public int expires_in { get; set; }
                public int ext_expires_in { get; set; }
                public string access_token { get; set; }
                public string refresh_token { get; set; }
                public string instance_url { get; set; }
            }
            public class HeaderModel
            {
                public Guid HeaderId { get; set; }
                public string Name { get; set; }
                public string Prefix { get; set; }
            }
            public class IdentifiersModel
            {
                public Guid IId { get; set; }
                public string Name { get; set; }
                public string Value { get; set; }
            }
            public class UPIdentifierModel
            {

                public Guid UserProviderId { get; set; }
                public List<IdentifiersModel> IdentifiersModel { get; set; }
                public UPIdentifierModel()
                {
                    IdentifiersModel = new List<IdentifiersModel>();
                }
            }
        }
    }

    public class UserSettingModel
    {
        public Guid SettingId { get; set; }
        public int DataType { get; set; }
        public string SettingValue { get; set; }
    }
    public class AutoTranslateModel
    {
        public string Locale { get; set; }
        public string OriginalString { get; set; }
        public int LanguageId { get; set; }
        public string Value { get; set; }
        public float Score { get; set; }
    }
    public class TranslateModel
    {
        public System.Guid Id { get; set; }
        public string OriginalString { get; set; }
        public int ContextId { get; set; }
        public int LanguageId { get; set; }
        public string ReferenceId { get; set; }
        public string Value { get; set; }
        public bool IsDefault { get; set; }
        public bool IsActive { get; set; }
        public int ShowSequence { get; set; }
        public bool IsAutoTranslated { get; set; }
        public bool IsEdited { get; set; }
    }


    public class TranslationResult
    {
        public DetectedLanguage DetectedLanguage { get; set; }
        public TextResult SourceText { get; set; }
        public Translation[] Translations { get; set; }
    }

    public class DetectedLanguage
    {
        public string Language { get; set; }
        public float Score { get; set; }
    }

    public class TextResult
    {
        public string Text { get; set; }
        public string Script { get; set; }
    }

    public class Translation
    {
        public string Text { get; set; }
        public TextResult Transliteration { get; set; }
        public string To { get; set; }
        public Alignment Alignment { get; set; }
        public SentenceLength SentLen { get; set; }
    }

    public class Alignment
    {
        public string Proj { get; set; }
    }

    public class SentenceLength
    {
        public int[] SrcSentLen { get; set; }
        public int[] TransSentLen { get; set; }
    }
    public class LanguageContextModel
    {
        public System.Guid Id { get; set; }
        public string OriginalString { get; set; }
        public int ContextId { get; set; }
        public int LanguageId { get; set; }
        public string ReferenceId { get; set; }
        public string Value { get; set; }
        public bool IsDefault { get; set; }
        public bool IsActive { get; set; }
        public int ShowSequence { get; set; }
        public bool IsAutoTranslated { get; set; }
    }
    public class ReferenceModel
    {
        public string Id { get; set; }
        public string Name { get; set; }
    }
    public class ContextModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }
    public class LanguageModel
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public bool IsActive { get; set; }
        public int ShowSequence { get; set; }
        public string Locale { get; set; }

    }
}
