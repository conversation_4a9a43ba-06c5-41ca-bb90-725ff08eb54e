﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
    public class OneDriveModels
    {
        #region DataModels

        public class Device
        {
            public string id { get; set; }
        }

        public class UserModel
        {
            public string displayName { get; set; }
            public string id { get; set; }
        }

        public class CreatedBy
        {
            public Device device { get; set; }
            public UserModel user { get; set; }
        }

        public class FileSystemInfo
        {
            public string createdDateTime { get; set; }
            public string lastModifiedDateTime { get; set; }
        }

        public class Hashes
        {
            public string sha1Hash { get; set; }
        }

        public class File
        {
            public Hashes hashes { get; set; }
            public string mimeType { get; set; }
        }

        public class User2
        {
            public string displayName { get; set; }
            public string id { get; set; }
        }

        public class LastModifiedBy
        {
            public User2 user { get; set; }
        }

        public class ParentReference
        {
            public string driveId { get; set; }
        }

        public class User3
        {
            public string displayName { get; set; }
            public string id { get; set; }
        }

        public class Owner
        {
            public User3 user { get; set; }

            public Owner()
            {
                user = new User3();
            }
        }

        public class Shared
        {
            public Owner owner { get; set; }
        }

        public class RemoteItem
        {
            public CreatedBy createdBy { get; set; }
            public FileSystemInfo fileSystemInfo { get; set; }
            public File file { get; set; }
            public string id { get; set; }
            public LastModifiedBy lastModifiedBy { get; set; }
            public string lastModifiedDateTime { get; set; }
            public string name { get; set; }
            public ParentReference parentReference { get; set; }
            public Shared shared { get; set; }
            public int size { get; set; }
            public string webUrl { get; set; }
        }

        public class Value
        {
            public string createdDateTime { get; set; }
            public string cTag { get; set; }
            public string eTag { get; set; }
            public string id { get; set; }
            public string lastModifiedDateTime { get; set; }
            public string name { get; set; }
            public string webUrl { get; set; }
            // public FolderModel FolderModel{ get; set;}
            public RemoteItem remoteItem { get; set; }
        }
        //public class FolderModel
        //{
        //    public int childCount { get; set; }
        //}
        public class OneDrive
        {
            public List<Value> value { get; set; }
        }


        public class SharedFileModel
        {
            public DateTime LastModifiedDateTime { get; set; }
            public Guid ProviderId { get; set; }
            public Guid UserProviderId { get; set; }
            public string id { get; set; }
            public string name { get; set; }
            public string webUrl { get; set; }
        }


        //models for get drives graph api

        public class Quota
        {
            public int deleted { get; set; }
            public long remaining { get; set; }
            public string state { get; set; }
            public long total { get; set; }
            public int used { get; set; }
        }

        public class DriveValueModel
        {
            public string id { get; set; }
            public string driveType { get; set; }
            public Owner owner { get; set; }
            public Quota quota { get; set; }

            public DriveValueModel()
            {
                owner = new Owner();
                quota = new Quota();

            }
        }

        public class DriveModel
        {
            public string odatacontext { get; set; }
            public List<DriveModel> value { get; set; }
        }
        #endregion
    }
}
