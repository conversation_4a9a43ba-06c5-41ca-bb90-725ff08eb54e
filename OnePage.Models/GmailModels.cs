﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
   public class GmailModels
    {
        #region ChatchAll Models
        public class GmailTokenModel
        {
            public string access_token { get; set; }
            public string token_type { get; set; }
            public int expires_in { get; set; }
            public string refresh_token { get; set; }
        }
        public class GmailUserDetailModel
        {
            public string emailAddress { get; set; }
            public int messagesTotal { get; set; }
            public int threadsTotal { get; set; }
            public string historyId { get; set; }
        }
        #endregion

        #region DataModel
        public class GmailAttachmentDetails
        {
            public int size { get; set; }
            public string data { get; set; }
        }

        #endregion
    }
}
