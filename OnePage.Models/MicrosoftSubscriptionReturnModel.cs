﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
    public class MicrosoftSubscriptionReturnModel
    {
      
            public string odatacontext { get; set; }
            public string id { get; set; }
            public string resource { get; set; }
            public string applicationId { get; set; }
            public string changeType { get; set; }
            public string clientState { get; set; }
            public string notificationUrl { get; set; }
            public object notificationQueryOptions { get; set; }
            public object lifecycleNotificationUrl { get; set; }
            public DateTime expirationDateTime { get; set; }
            public string creatorId { get; set; }
            public object includeResourceData { get; set; }
            public string latestSupportedTlsVersion { get; set; }
            public object encryptionCertificate { get; set; }
            public object encryptionCertificateId { get; set; }
            public object notificationUrlAppId { get; set; }
        }


    
}
