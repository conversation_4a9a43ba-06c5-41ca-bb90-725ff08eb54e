﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
   public class GenericMailModels
    {
        #region Generic Token Model 
        public class TokenModel
        {
            public string token_type { get; set; }
            public string scope { get; set; }
            public int expires_in { get; set; }
            public int ext_expires_in { get; set; }
            public string access_token { get; set; }
            public string refresh_token { get; set; }
        }
        public class Attachment1
        {
            public string id { get; set; }
            public string name { get; set; }
            public string lastModifiedDateTime { get; set; }
        }
        public class UserAttachments
        {
            public List<Attachment1> value { get; set; }
        }
        public class AttachmentGeneric
        {
            public string id { get; set; }
            public string name { get; set; }
            public string lastModifiedDateTime { get; set; }
        }
        #endregion
        public class DriveAttachmentModel
        {
            public string Id { get; set; }
            public Guid ProviderId { get; set; }
            public Guid UserProviderId { get; set; }
            public string FileName { get; set; }
            public string FolderId { get; set; }
            public string Link { get; set; }
            public string Path { get; set; }
            public DateTime Created { get; set; }
            public DateTime Modified { get; set; }
            public DateTime LastAccessed { get; set; }
            public bool IsFolder { get; set; }
            public bool IsOwned { get; set; }
            public bool IsShared { get; set; }
            public string Url { get; set; }
            public string DownloadUrl { get; set; }
            public bool IsDownloaded { get; set; }
            public string DriveId { get; set; }
            public double Size { get; set; }
            public bool IsTeam { get; set; }
            public string TeamId { get; set; }
        }
        public class GenericMailData
        {
            public string Id { get; set; }
            public string Link { get; set; }
            public Guid ProviderId { get; set; }
            public Guid UserProviderId { get; set; }
            public string Recieved { get; set; }
            public bool HasAttachment { get; set; }
            public string Subject { get; set; }
            public string Preview { get; set; }
            public string HtmlBody { get; set; }
            public string TextBody { get; set; }
            public string From { get; set; }
            public List<Email> To { get; set; }
            public List<Email> CC { get; set; }
            public List<Email> BCC { get; set; }
            public List<AttachmentModel> Attachments { get; set; }
            public string IMAPUniqueId { get; set; }
            public GenericMailData()
            {
                To = new List<Email>();
                CC = new List<Email>();
                BCC = new List<Email>();
                Attachments = new List<AttachmentModel>();
            }

        }

        public class Email
        {
            public string Name { get; set; }
            public string Address { get; set; }
        }
        public class AttachmentModel
        {
            public string Id { get; set; }
            public string Name { get; set; }
            public string Link { get; set; }
            public string LastModifiedDateTime { get; set; }
            public string contentType { get; set; }
        }
        public class AttachmentDataModel
        {
            public Guid ProviderId { get; set; }
            public Guid UserProviderId { get; set; }
            public string id { get; set; }
            public string MailId { get; set; }
            public string name { get; set; }
            public string receivedDateTime { get; set; }
            public string contentType { get; set; }
        }

        public class SharedFilesModel
        {
            public string Id { get; set; }
            public Guid ProviderId { get; set; }
            public Guid UserProviderId { get; set; }
            public string FileName { get; set; }
            public string FolderId { get; set; }
            public string Link { get; set; }
            public string Path { get; set; }
            public DateTime Created { get; set; }
            public DateTime Modified { get; set; }
            public DateTime LastAccessed { get; set; }
            public bool IsFolder { get; set; }
            public bool IsOwned { get; set; }
            public bool IsShared { get; set; }
            public string Url { get; set; }
            public string DownloadUrl { get; set; }
            public bool IsDownloaded { get; set; }
            public string DriveId { get; set; }
            public double Size { get; set; }
            public bool IsTeam { get; set; }
            public string TeamId { get; set; }
            public bool IsUserStorage { get; set; }
            public string StorageCardId { get; set; }
        }
    }
    }

