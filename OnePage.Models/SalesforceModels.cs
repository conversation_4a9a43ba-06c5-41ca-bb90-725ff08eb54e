﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
   public class SalesforceModels
    {
        #region CathAll
        #region Token Models
        public class TokenModel
        {
            public string access_token { get; set; }
            public string refresh_token { get; set; }
            public string signature { get; set; }
            public string scope { get; set; }
            public string id_token { get; set; }
            public string instance_url { get; set; }
            public string id { get; set; }
            public string token_type { get; set; }
            public string issued_at { get; set; }
        }
        #endregion

        #region SalesForce Me Api
        public class SalesForceUserModel
        {
            public string sub { get; set; }
            public string user_id { get; set; }
            public string organization_id { get; set; }
            public string preferred_username { get; set; }
            public string nickname { get; set; }
            public string name { get; set; }
            public string email { get; set; }
            public bool email_verified { get; set; }
            public string given_name { get; set; }
            public string family_name { get; set; }
            public string zoneinfo { get; set; }
            public Photos photos { get; set; }
            public string profile { get; set; }
            public string picture { get; set; }
            public Address address { get; set; }
            public Urls urls { get; set; }
            public bool active { get; set; }
            public string user_type { get; set; }
            public string language { get; set; }
            public string locale { get; set; }
            public int utcOffset { get; set; }
            public string updated_at { get; set; }
            public bool is_app_installed { get; set; }
        }
        public class Photos
        {
            public string picture { get; set; }
            public string thumbnail { get; set; }
        }

        public class Address
        {
        }

        public class Urls
        {
            public string enterprise { get; set; }
            public string metadata { get; set; }
            public string partner { get; set; }
            public string rest { get; set; }
            public string sobjects { get; set; }
            public string search { get; set; }
            public string query { get; set; }
            public string recent { get; set; }
            public string tooling_soap { get; set; }
            public string tooling_rest { get; set; }
            public string profile { get; set; }
            public string feeds { get; set; }
            public string groups { get; set; }
            public string users { get; set; }
            public string feed_items { get; set; }
            public string feed_elements { get; set; }
            public string custom_domain { get; set; }
        }
        #endregion

        #endregion
    }
}
