﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
    public class InAppPurchaseModel
    {
        public int Credits { get; set; }
        public string ReceiptId { get; set; }
        public System.DateTime? TransactionDateUTC { get; set; }
        public string ProductId { get; set; }
        public bool? AutoRenewing { get; set; }
        public string PurchaseToken { get; set; }
        public bool? IsAcknowledged { get; set; }
        public string Payload { get; set; }
        public byte? PurchaseState { get; set; }
        public byte? ConsumptionState { get; set; }
    }
    public class CreditsModel
    {
        public int? VanishingCredits { get; set; }
        public int? NonVanishingCredits { get; set; }
        public bool DailyCreditsRedeemed { get; set; }
    }
}
