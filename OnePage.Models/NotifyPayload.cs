﻿using System;

namespace OnePage.Models
{
    public class NotifyPayload
    {
        public string NotificationId { get; set; } = Guid.NewGuid().ToString();
        public string CallId { get; set; } = "";
        public string PhoneNumber { get; set; } = "";
        public int EventType { get; set; }
        public int UserType { get; set; }
        public string UserId { get; set; } = "";
        public string DeviceId { get; set; } = "";
        public string CallType { get; set; } = "";
        public string EmailId { get; set; } = "";
        public string Url { get; set; } = "";
        public string AuthUrl { get; set; } = "";
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public bool IsRegistered { get; set; }
        public string Token { get; set; } = "";
        public string ProviderTypeId { get; set; } = "";
    }
}
