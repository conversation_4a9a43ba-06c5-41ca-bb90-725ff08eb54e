﻿
namespace OnePage.Models
{
   public class IMAPModels
    {
        public class From1
        {
            public string email { get; set; }
            public string name { get; set; }
        }

        public class To
        {
            public string email { get; set; }
        }

        public class Bcc
        {
            public string email { get; set; }
        }

        public class Cc
        {
            public string email { get; set; }
        }

        public class Addresses
        {
            public From1 from { get; set; }
            public List<To> to { get; set; }
            public List<Bcc> bcc { get; set; }
            public List<Cc> cc { get; set; }
            public Addresses()
            {
                to = new List<To>();
                cc = new List<Cc>();
                bcc = new List<Bcc>();
            }
        }

        public class Source
        {
            public string label { get; set; }
            public string resource_url { get; set; }
        }

        public class IMAPBody
        {
            public string body_section { get; set; }
            public string type { get; set; }
            public string charset { get; set; }
            public string content { get; set; }
        }
        public class FileIMAP
        {
            public string type { get; set; }
            public int size { get; set; }
            public string file_name { get; set; }
            public List<List<string>> file_name_structure { get; set; }
            public string body_section { get; set; }
            public string content_disposition { get; set; }
            public string file_id { get; set; }
            public bool is_tnef_part { get; set; }
            public bool supports_preview { get; set; }
            public string main_file_name { get; set; }
            public bool is_embedded { get; set; }
            public string resource_url { get; set; }
        }

        public class Datum
        {
            public string email_message_id { get; set; }
            public Addresses addresses { get; set; }
            public string message_id { get; set; }
            public List<Source> sources { get; set; }
            public int thread_size { get; set; }
            public List<FileIMAP> files { get; set; }
            public int date { get; set; }
            public int date_indexed { get; set; }
            public int date_received { get; set; }
            public string subject { get; set; }
            public List<string> folders { get; set; }
            public string resource_url { get; set; }
            public List<IMAPBody> body { get; set; }
            public Datum()
            {
                body = new List<IMAPBody>();
                files = new List<FileIMAP>();
                sources = new List<Source>();
                addresses = new Addresses();
            }
        }
        public class IMAPMailModel
        {
            public List<Datum> data { get; set; }
            public bool status { get; set; }
            public IMAPMailModel()
            {
                data = new List<Datum>();

            }
        }
    }
}
