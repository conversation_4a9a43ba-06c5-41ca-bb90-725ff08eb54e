﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OnePage.Models
{
    public class ContactModels
    {
        public class ContactIModel
        {
            public Guid DTId { get; set; }//DeviceTypeId
            public Guid UPId { get; set; }//UserProviderId
            public Guid PId { get; set; }//ProviderId
            public string Id { get; set; }
            public string DisplayName { get; set; }
            public string Name { get; set; }
            public string Sal { get; set; }
            public string FN { get; set; }
            public string MN { get; set; }
            public string LN { get; set; }
            public string Desig { get; set; }
            public List<EIModel> Emails { get; set; }
            public List<PIModel> Phones { get; set; }
            public List<AIModel> Address { get; set; }
            public List<RIModel> Relationships { get; set; }
            public List<IMIModel> IM { get; set; }
            public List<string> Notes1 { get; set; }
            public string NickName { get; set; }
            public Nullable<bool> IsAggregate { get; set; }
            public string Prefix { get; set; }
            public string Suffix { get; set; }
            public string CompanyName { get; set; }
            public List<string> Websites { get; set; }
            public ContactIModel()
            {
                Emails = new List<EIModel>();
                Phones = new List<PIModel>();
                Address = new List<AIModel>();
                Relationships = new List<RIModel>();
                IM = new List<IMIModel>();
            }
        }
        public class AIModel
        {
            public Guid Id { get; set; }
            public Guid CId { get; set; }
            public bool IsActive { get; set; }
            public int? AddressType { get; set; }
            public string Label { get; set; }
            public string StreetAddress { get; set; }
            public string City { get; set; }
            public string Region { get; set; }
            public string CountryId { get; set; }
            public string PostalCode { get; set; }
        }
        public class RIModel
        {
            public Guid Id { get; set; }
            public Guid CId { get; set; }
            public bool IsActive { get; set; }
            public string Name { get; set; }
            public int? Type { get; set; }
        }
        public class IMIModel
        {
            public Guid Id { get; set; }
            public Guid CId { get; set; }
            public bool IsActive { get; set; }
            public Nullable<int> Service { get; set; }
            public string ServiceLabel { get; set; }
            public string Account { get; set; }
        }
        public class PIModel
        {
            public string Phone { get; set; }
            public int Type { get; set; }
        }
        public class EIModel
        {
            public string Email { get; set; }
            public int Type { get; set; }
        }
        #region ImportContact Filter
        public class NewContactModel
        {
            public Guid UserId { get; set; }
            public string ContactId { get; set; }
            public bool IsUnknown { get; set; }
            public bool IsContactFound { get; set; }
            public bool IsMergeRequired { get; set; }
            public List<EmailModel> NewEmails { get; set; }
            public List<PhoneModel> NewPhones { get; set; }
            public NewContactModel()
            {
                NewEmails = new List<EmailModel>();
                NewPhones = new List<PhoneModel>();
            }
            public class EmailModel
            {
                public string EmailAddress { get; set; }
                public bool IsFound { get; set; }
                public int Type { get; set; }
                public string ContactId { get; set; }
            }

            public class PhoneModel
            {
                public string Phone { get; set; }
                public string Sanitized { get; set; }
                public bool IsFound { get; set; }
                public int Type { get; set; }
                public string ContactId { get; set; }
            }
            #endregion
        }
    }
}
