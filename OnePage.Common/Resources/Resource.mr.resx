﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AuthenticatedMsg" xml:space="preserve">
    <value>तुम्ही प्रमाणित आहात. पुढे जाण्यासाठी बटणावर क्लिक करा!</value>
  </data>
  <data name="AuthenticationFailed" xml:space="preserve">
    <value>प्रमाणीकरण अपयशी ठरले. मदतीसाठी <EMAIL> संपर्क साधा</value>
  </data>
  <data name="LinkExpired" xml:space="preserve">
    <value>लिंक संपली आहे! कृपया नवीन विनंती करा.</value>
  </data>
  <data name="ProvisionedError" xml:space="preserve">
    <value>असे दिसते की आपण तरतूद केलेल्या ईमेलपेक्षा वेगळ्या ईमेलसह लॉग इन केले आहे. कृपया पुन्हा प्रयत्न करा.</value>
  </data>
  <data name="YouAreAuthenticated" xml:space="preserve">
    <value>तुम्ही प्रमाणित आहात. आपण ही खिडकी सुरक्षितपणे बंद करू शकता</value>
  </data>
</root>