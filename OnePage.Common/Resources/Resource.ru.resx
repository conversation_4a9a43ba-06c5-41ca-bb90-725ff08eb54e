﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AuthenticatedMsg" xml:space="preserve">
    <value>Вы аутентифицированы. Пожалуйста, нажмите на кнопку, чтобы продолжить!</value>
  </data>
  <data name="AuthenticationFailed" xml:space="preserve">
    <value>Проверка подлинности не удалась. Обратитесь <EMAIL> за помощью</value>
  </data>
  <data name="LinkExpired" xml:space="preserve">
    <value>Срок действия ссылки истек! Пожалуйста, запросите новый.</value>
  </data>
  <data name="ProvisionedError" xml:space="preserve">
    <value>Похоже, вы вошли в систему с другим адресом электронной почты, отличным от того, который был подготовлен. Повторите попытку.</value>
  </data>
  <data name="YouAreAuthenticated" xml:space="preserve">
    <value>Вы аутентифицированы. Вы можете спокойно закрыть это окно</value>
  </data>
</root>