﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AuthenticatedMsg" xml:space="preserve">
    <value>你已透過身份驗證。 請點擊按鈕繼續！</value>
  </data>
  <data name="AuthenticationFailed" xml:space="preserve">
    <value>身份驗證失敗。 聯繫 <EMAIL> 尋求幫助</value>
  </data>
  <data name="LinkExpired" xml:space="preserve">
    <value>連結已過期！ 請申請新嘅。</value>
  </data>
  <data name="ProvisionedError" xml:space="preserve">
    <value>你登錄嘅電子郵件似乎與預配嘅電子郵件不同。 請再試一次。</value>
  </data>
  <data name="YouAreAuthenticated" xml:space="preserve">
    <value>你已透過身份驗證。 你可以安全地關閉此窗口</value>
  </data>
</root>