﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AuthenticatedMsg" xml:space="preserve">
    <value>인증되었습니다. 버튼을 클릭하여 계속 진행하세요!</value>
  </data>
  <data name="AuthenticationFailed" xml:space="preserve">
    <value>인증에 실패했습니다. <EMAIL> 도움을 요청하세요.</value>
  </data>
  <data name="LinkExpired" xml:space="preserve">
    <value>링크가 만료되었습니다! 새로 요청하십시오.</value>
  </data>
  <data name="ProvisionedError" xml:space="preserve">
    <value>프로비저닝된 이메일과 다른 이메일로 로그인한 것 같습니다. 다시 시도하십시오.</value>
  </data>
  <data name="YouAreAuthenticated" xml:space="preserve">
    <value>인증되었습니다. 이 창을 안전하게 닫을 수 있습니다</value>
  </data>
</root>