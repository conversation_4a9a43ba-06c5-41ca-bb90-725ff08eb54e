﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AuthenticatedMsg" xml:space="preserve">
    <value>您已通过身份验证。请点击按钮继续！</value>
  </data>
  <data name="AuthenticationFailed" xml:space="preserve">
    <value>身份验证失败。联系 <EMAIL> 寻求帮助</value>
  </data>
  <data name="LinkExpired" xml:space="preserve">
    <value>链接已过期！请申请新的。</value>
  </data>
  <data name="ProvisionedError" xml:space="preserve">
    <value>您登录的电子邮件似乎与预配的电子邮件不同。请再试一次。</value>
  </data>
  <data name="YouAreAuthenticated" xml:space="preserve">
    <value>您已通过身份验证。您可以安全地关闭此窗口</value>
  </data>
</root>