﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AuthenticatedMsg" xml:space="preserve">
    <value>認証されます。ボタンをクリックして続行してください!</value>
  </data>
  <data name="AuthenticationFailed" xml:space="preserve">
    <value>認証に失敗しました。<EMAIL> に問い合わせる</value>
  </data>
  <data name="LinkExpired" xml:space="preserve">
    <value>リンクの有効期限が切れました!新しいものをリクエストしてください。</value>
  </data>
  <data name="ProvisionedError" xml:space="preserve">
    <value>プロビジョニングされたものとは異なるメールアドレスでログインしたようです。もう一度やり直してください。</value>
  </data>
  <data name="YouAreAuthenticated" xml:space="preserve">
    <value>認証されます。このウィンドウは安全に閉じることができます</value>
  </data>
</root>