using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using com.google.i18n.phonenumbers;
using libphonenumber;
using PhoneNumbers;
using RestSharp;
using static com.google.i18n.phonenumbers.PhoneNumberUtil;
using PhoneNumber = libphonenumber.PhoneNumber;
using PhoneNumberUtil = com.google.i18n.phonenumbers.PhoneNumberUtil;

namespace OnePage.Common;

public static class CommonData
{
    //Static DeviceTypeId used in project 
    public static Guid IOSDeviceTypeId = Guid.Parse("85BF95B2-2315-4C59-9EF9-47F33CFA65AC");
    public static Guid AndroidDeviceTypeId = Guid.Parse("1E49B16C-6CFE-427C-9A71-C91A0FD1FC69");

    public static Guid WebAppDeviceTypeId = Guid.Parse("8DFE90E8-6F2C-4A60-BE04-830261556F6B");

    //WE APP Id
    public static Guid WEAppId = Guid.Parse("b4f30f71-8e97-498a-aaa1-d8837ef55479");
    public static Guid OPAppId = Guid.Parse("bee2b05c-0b6e-4d8c-b9ae-960a78a3afc7");
    public static Guid MiofertaAppId = Guid.Parse("2d80f243-7b0b-4a1a-a24b-41e293dcd953");
    public static Guid AmperAppId = Guid.Parse("C1EF9960-2E93-47F7-8B40-BD323C17BF68");
    public static Guid WEAWSTestApp = Guid.Parse("da040d7a-a47d-481d-81cb-2f67a3aecec3");
    public static Guid NotificarAWSTestApp = Guid.Parse("1d5815d3-340e-45de-8bee-15b71b447753");
    public static Guid AmperAWSTestApp = Guid.Parse("a29cd3cb-3515-4c98-814d-75e3ea84e159");
    public static Guid GlobalAppId = Guid.Parse("92C4D496-63E2-4660-B38A-B1EBF1101A65");

    //Static OrgIds
    public static Guid WhatElseCustomerOrgId = Guid.Parse("97859466-D85E-47E7-ABB5-56CF76335318");

    public static Guid WhatElseOrgId = Guid.Parse("98ee7a4b-de3c-49e3-aaf3-dc6b7e2cc905");

    //Static CustomerId
    public static string WhatElseOrgCustomerId = "Hr5514ZQk5Q5C1NjO";
    //Static RoleIds
    //public static Guid AdminRoleId = Guid.Parse("a0702506-fbe2-4fd5-b0d5-c0f7238b6ee4");
    //public static Guid EnterpriseCustomerRoleId = Guid.Parse("a68de40f-c7e4-4a05-ab2e-4cafd31dbda7");

    public static Guid UserInviteOfferId = Guid.Parse("4CF56518-DC48-4F3A-B8C7-F6A2B0390572");

    //Static ProviderTypeId
    public static Guid socialNetworkProviderTypeId = Guid.Parse("D9AE7BF6-41F0-4CC0-95CC-32F4FF72F332");
    public static Guid CRMProviderTyepId = Guid.Parse("A044C04D-D433-4C56-9FB7-CE3C4F66B7AC");
    public static Guid DeviceContactsProviderTyepId = Guid.Parse("6c59ec93-0573-4809-929c-35e11cdd6045");
    public static string WebsiteSocialId = "ac45263f-dfd8-4482-9987-478537598d26";
    public static Guid PersonalEmailPTId = Guid.Parse("1DEBFF2D-57A7-4D65-9CB6-0C5B1B42087A");
    public static Guid PersonalStoragePTId = Guid.Parse("0E64E6C6-9943-4EC6-871B-AD4013174F6A");
    public static Guid Government = Guid.Parse("99DBFF0D-8175-4420-A6D9-669429710EDF");
    public static Guid AmperApps = Guid.Parse("85484934-58A2-4495-ACD1-4F0DD6CD7558");
    public static Guid EnterpriseStorageProivderTyId = Guid.Parse("BB443B73-996B-4516-AF71-00598BA7671D");
    public static Guid EnterpriseEmailProivderTyId = Guid.Parse("7FED8DC9-937C-42AA-98FF-670F3399F3C9");
    public static Guid VirtualNumbers = Guid.Parse("5938D26F-6791-4328-A152-FC285F0AB346");
    public static Guid CompanyInfoProviderTypeId = Guid.Parse("F1356586-0E09-4B9E-A8AE-672EC5CE6E9A");
    public static Guid SocialNetworkProviderTypeId = Guid.Parse("D9AE7BF6-41F0-4CC0-95CC-32F4FF72F332");

    public static readonly List<string> StorageProviderTypes = new List<string>
        { "bb443b73-996b-4516-af71-00598ba7671d", "0e64e6c6-9943-4ec6-871b-ad4013174f6a" };

    public static readonly List<string> EmailProviderTypes = new List<string>
        { "1debff2d-57a7-4d65-9cb6-0c5b1b42087a", "7fed8dc9-937c-42aa-98ff-670f3399f3c9" };

    public static readonly List<string> CRMProviderTypes = new List<string> { "a044c04d-d433-4c56-9fb7-ce3c4f66b7ac" };

    public static readonly List<string> SupportProviderTypes = new List<string>
        { "6afd623a-576f-42c3-a8ae-59ee5931e5ca" };

    //IdentifierId 
    public static Guid McMailIdentifierId = Guid.Parse("38FCFC81-C779-4B79-978A-1F9AA97E8ED7");
    public static Guid MsMailIdentifierId = Guid.Parse("C7B2B24E-E8B3-4E23-B209-33C34E322512");
    public static Guid OfMailIdentifierId = Guid.Parse("882F13CF-323D-4188-AEE9-409AE74F94EC");
    public static Guid ObMailIdentifierId = Guid.Parse("E1F58D8F-6A7E-4E20-A33C-32D910274486");
    public static Guid GcMailIdentifierId = Guid.Parse("08319777-4718-42E6-8BCA-AD9C40F4914C");
    public static Guid GmMailIdentifierId = Guid.Parse("7908DEE2-CEDB-4CDE-9FEA-18DFE494A523");
    public static Guid GdMailIdentifierId = Guid.Parse("507DD555-D174-489C-9741-2E684DBDBBF1");
    public static Guid GsMailIdentifierId = Guid.Parse("3500C859-D6E8-4629-825C-D699782A68A5");

    //Static ProviderId
    public static Guid IMAPProviderId = Guid.Parse("64254B84-0E2E-498A-BA7B-32FD21AB3A64");
    public static Guid BoxProviderId = Guid.Parse("EBB3714B-3F7C-4022-B304-936607A82437");
    public static Guid DropBoxProviderId = Guid.Parse("BB779183-E903-4F55-A099-3EF4373A6C25");
    public static Guid CalendlyProviderId = Guid.Parse("0E2CECB5-0CBB-405D-8787-81566138E1D9");
    public static Guid EvernoteProviderId = Guid.Parse("EE51A555-0930-4795-87CF-88E0541E2CE5");
    public static Guid IDPProviderId = Guid.Parse("3be06471-29d4-4aed-9bb8-66eda6ebd1bd");
    public static Guid MicrosoftSuiteProviderId = Guid.Parse("a2dbf10e-08c5-4aa5-b198-8bbd2a860a66");
    public static Guid GoogleSuiteProviderId = Guid.Parse("deaa4c8d-b0bb-4d81-bb86-055944e207d7");
    public static Guid office365ProviderId = Guid.Parse("1debff2d-57a7-4d65-9cb6-0c5b1b42087a");

    //public static Guid outlookProviderId = Guid.Parse("*************-4649-a65a-2616db545cd8");
    public static Guid MicrosoftProviderId = Guid.Parse("327915f0-677a-444c-99a8-1b4998a4623c");
    public static Guid MicrosoftContactProvideId = Guid.Parse("ECD27441-F9F9-4EDA-9432-CD4E86C9D027");
    public static Guid ManualProviderId = Guid.Parse("EE26B1CE-E1E8-477C-847D-79CBB663F310");
    public static Guid HubspotProviderId = Guid.Parse("************************************");
    public static Guid GDriveProviderId = Guid.Parse("1DDE92F9-1561-47DD-8993-59F55C5797AD");
    public static Guid GDriveForBusinessProviderId = Guid.Parse("03300FF7-4768-4BBC-8816-3577DC7B9F7A");
    public static Guid ZendeskProviderId = Guid.Parse("0D535674-70A3-4603-9D89-38223F8FE356");
    public static Guid MiOfertaProviderId = Guid.Parse("2209bb99-4dab-43f4-8c8d-44c7d83fb8fa");
    public static Guid DynamicCRMProviderId = Guid.Parse("10A5E65C-0967-4CF7-B959-CE4CB06C4DD7");
    public static Guid PipedriveProviderId = Guid.Parse("03A067AD-4BD3-4CF9-9D2E-73238A2F5A86");
    public static Guid PeopleDataProviderId = Guid.Parse("B8951A20-8CF4-47EE-9F18-1DDC4E7705AC");
    public static Guid PeopleDataLabSourceId = Guid.Parse("904D36E5-C08D-4DBE-A6CB-56200B92124D");
    public static Guid OneDriveProviderId = Guid.Parse("32ac5cd5-7024-4ab5-af81-46f03a3a7147");
    public static Guid TwitterProviderId = Guid.Parse("B76E4F1E-8FB0-46AA-8DD8-866EDDD84201");

    //public static Guid GmailPersonalProviderId = Guid.Parse("b75ce275-**************-f6476cfbc67a");
    public static Guid GmailBusinessProviderId = Guid.Parse("b91cbaee-d9d0-490e-8458-1b5a8e4e2aac");
    public static Guid GoogleCalendarProviderId = Guid.Parse("a45b6b98-9d24-4c33-b464-ac585f768752");
    public static Guid GoogleContactProvideId = Guid.Parse("35b45aa5-10f5-428c-a264-9dd5854bac26");
    public static Guid ZohoCRMProviderId = Guid.Parse("F9695DA4-AE0E-41A3-A0E4-FBEC795D2DE5");
    public static Guid SalesforceProviderId = Guid.Parse("CF2F8BCC-F9A7-4AEF-900C-32A615DE4AF1");
    public static Guid FreshdeskProviderId = Guid.Parse("2E2B4E02-BA58-4E9E-A70E-18942F099A06");
    public static Guid FreshsalesProviderId = Guid.Parse("fd4e5e65-3f71-4c48-a6f2-4b27f584a795");
    public static Guid ZohoRecruitProviderId = Guid.Parse("8fafc8c0-a202-4613-a809-102f29332583");
    public static Guid AmperProviderId = Guid.Parse("ec7db679-2c41-4d59-8dae-0ce4f3c026f1");
    public static Guid AgileCRMProviderId = Guid.Parse("1ADEBDC2-54CD-4234-BB96-FA0D4A631B93");
    public static Guid IntercomProviderId = Guid.Parse("3B6B56D9-E19A-4A5A-8E13-EC20C65BB502");
    public static Guid ExotelProviderId = Guid.Parse("a1511b36-06ef-4cb1-9825-86f781c26235");
    public static Guid OzoneTelIndiaProviderId = Guid.Parse("257EA371-ECD0-400B-A27C-202166C316A5");
    public static Guid OzoneTelUSProviderId = Guid.Parse("B8DF0E95-D0FC-4A00-AAAC-C296BBFF9265");
    public static Guid SymblProviderId = Guid.Parse("0f80746b-7d33-4348-97bf-8ecbc0b42337");
    public static Guid TwilioProviderId = Guid.Parse("934fa923-1511-4fb6-9927-7c9247301b5d");

    //const providerIds
    public const string ConstDynamicCRMProviderId = "10a5e65c-0967-4cf7-b959-ce4cb06c4dd7";
    public const string ConstPipedriveProviderId = "03a067ad-4bd3-4cf9-9d2e-73238a2f5a86";
    public const string ConstZendeskProviderId = "0d535674-70a3-4603-9d89-38223f8fe356";
    public const string ConstHubspotProviderId = "************************************";
    public const string ConstZohoCRMProviderId = "f9695da4-ae0e-41a3-a0e4-fbec795d2de5";
    public const string ConstSalesforceProviderId = "cf2f8bcc-f9a7-4aef-900c-32a615de4af1";
    public const string ConstSalesforceReviewProviderId = "d3bfe6cc-a874-4253-a059-89734ce01d75";
    public const string ConstFreshdeskProviderId = "2e2b4e02-ba58-4e9e-a70e-18942f099a06";
    public const string ConstFreshsalesProviderId = "fd4e5e65-3f71-4c48-a6f2-4b27f584a795";
    public const string ConstZohoRecruitProviderId = "8fafc8c0-a202-4613-a809-102f29332583";
    public const string ConstAgileCRMProviderId = "1adebdc2-54cd-4234-bb96-fa0d4a631b93";
    public const string ConstIntercomProviderId = "3b6b56d9-e19a-4a5a-8e13-ec20c65bb502";

    public const string ConstZohoContactsProviderId = "cd459cdc-4172-41fd-9dd9-8881067b70b2";

    // public const string ConstGmailProviderId = "b75ce275-**************-f6476cfbc67a";
    public const string ConstGsuiteProviderId = "b91cbaee-d9d0-490e-8458-1b5a8e4e2aac";
    public const string ConstGoogleCalendarProviderId = "a45b6b98-9d24-4c33-b464-ac585f768752";

    public const string ConstGoogleContactProviderId = "35b45aa5-10f5-428c-a264-9dd5854bac26";

    // public const string ConstOutlookProviderId = "*************-4649-a65a-2616db545cd8";
    public const string ConstO365ProviderId = "1debff2d-57a7-4d65-9cb6-0c5b1b42087a";
    public const string ConstMSProviderId = "327915f0-677a-444c-99a8-1b4998a4623c";
    public const string ConstMSContactProviderId = "ecd27441-f9f9-4eda-9432-cd4e86c9d027";
    public const string ZohoCalendarId = "10468ad5-5fd5-4fc4-bed0-e8f48a114df9";
    public const string ZohoMailId = "21900222-fe90-435f-ac93-9624ee38605b";

    //PhoneContacts ProviderId
    public static Guid ContactAddedByAdmin = Guid.Parse("1f7b38f4-bd9b-4deb-b928-2b122e9f37d2");
    public static Guid IOSContacts = Guid.Parse("963BDBA0-C791-4387-BE00-E25515E87041");
    public static Guid ContactAddedByUser = Guid.Parse("F9B2AE45-7C64-4DCF-A31F-BE52D55746BA");
    public static Guid WebContacts = Guid.Parse("B6960CEE-DD57-46FD-B88C-A2237DEC1D2F");
    public static Guid AndroidContacts = Guid.Parse("ADFD27E9-3ACD-47D8-BAA9-455B570EF2C4");

    public static StringBuilder sbnew = new StringBuilder();

    //URLs
    public static string ContextioContactsTemp =
        "http://wecontextiotemp.azurewebsites.net/index.php/api/Contextio/contacts?account_id=";

    public static string O365Contacts = "https://graph.microsoft.com/v1.0/me/contacts";

    public const string NeverBounceApi =
        "https://api.neverbounce.com/v4/single/check?key=private_8c48c7117b7b4f1a576091fde510ee12&email=";

    public const string azurefunctionURLCardsAndAction = "https://onepagegetcardsandactions.azurewebsites.net/api/OnePageGetCardsAndActions?code=WvcWAnSOiT1w-RcYf48LBqnIViPfRnRjO243ap5bzz4VAzFuJHpYeQ%3D%3D";
    
    //Chargebee planId and Name
    public static string freemiumPlanId = "freemium";
    public static string BasicPlanId = "EmailStorage";
    public static string PremiumPlanId = "premium";

    public static string WhiteLabelUserPlanId = "white-label-user";

    //Chargebee Coupon id and Name
    public static string FreeForverCouponId = "FREEFORVER";
    public static string OnlyCRMCouponId = "ONLYCRM";
    public static string OneTimeCouponId = "ONETIME";
    public static string SUPPERDISCOUNTCouponId = "SUPPERDISCOUNT";
    public static string SUPperCouponId = "SUPPER";


    //static UrlType Ids
    public static Guid AccessToken = Guid.Parse("7E71E9F0-9AB3-4491-A738-A2AD89C54E1F");
    public static Guid Authorization = Guid.Parse("BC0C4EB3-B800-4C18-82DE-04A140B3990F");
    public static Guid Me = Guid.Parse("D2055D76-B05F-4D14-886E-E21879069A12");

    public static Dictionary<string, string> DemoContacts { get; set; } = new Dictionary<string, string>
    {
        { "Lucian Frias", "<EMAIL>|https://www.linkedin.com/in/fogoros/" },
        { "Yuan Chin", "<EMAIL>|https://www.linkedin.com/in/wysie/" },
        { "Rahul Sharma", "<EMAIL>|https://in.linkedin.com/in/rahul-sharma-ba349914" },
        { "Cirila Granado", "<EMAIL>|https://www.linkedin.com/in/cirila-gutierrez-202537b4/" },
        { "Hui Tang", "<EMAIL>|https://www.linkedin.com/in/tanghui/" },
        { "Rosie Anderson", "<EMAIL>|https://www.linkedin.com/in/rosieanderson/" },
        { "Charles Carter", "<EMAIL>|https://www.linkedin.com/in/charleschuckcarter/" }
    };

    public static List<string> testUserDomains = new List<string> { "mrawesomeapps.com", "mapit4.me", "whatelse.io" };

    //sendgrid tmeplate names
    public enum SendgridTemplateNames
    {
        VerifyYourEmail = 1,
        EmailSuccessfullyVerified = 2,
        WeGotIt = 3,
        WelcomeToWhatElse = 4,
        YourNewPassword = 5,
        OtpEmail = 6,
        WelcomeToWhatElsePasswordLink = 7,
        ForgotPasswordLink = 8,
        OrgDailyReportAttachment = 9
    }

    public static string returnTime(TimeSpan Duration)
    {
        int seconds = (int)Duration.TotalSeconds;
        if (seconds <= 60)
        {
            return seconds == 60 ? "1 min" : string.Format("{0} sec", seconds);
        }
        else if (seconds > 60 && seconds <= 3600)
        {
            return seconds == 3600 ? "1 hour" : string.Format("{0} min {1} sec", seconds / 60, seconds % 60);
        }
        else
        {
            return string.Format("{0} hour {1} min {2} sec", seconds / 3600, seconds / 60, seconds % 60);
        }
    }

    // public static async Task<string> GenericMethod(string json, string url, int methodType, string access_token = "0")
    // {
    //     var client = new RestSharp.RestClient(url);
    //     var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
    //     request.AddHeader("content-type", "application/json;charset=UTF-8");
    //     if (access_token != "0")
    //     {
    //         request.AddHeader("authorization", "Bearer " + access_token);
    //     }
    //
    //     request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
    //     var response = await client.ExecuteAsync(request);
    //     return response.Content;
    // }

    public static async Task<string> GenericMethod(string json, string url, int methodType, string access_token = "0")
    {
        var client = new RestClient(url);
        var request = new RestRequest();
        request.AddHeader("content-type", "application/json;charset=UTF-8");

        if (access_token != "0")
        {
            request.AddHeader("authorization", "Bearer " + access_token);
        }
        request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
        var response = await (methodType == 1 ? client.PostAsync(request) : client.GetAsync(request));

        return response.Content;
    }


    // public static async Task<string> RealmMethod(string json, string url, int methodType, string userid = "")
    // {
    //     var client = new RestSharp.RestClient(url);
    //     var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
    //     request.AddHeader("content-type", "application/json;charset=UTF-8");
    //     if (!string.IsNullOrEmpty(userid)) { request.AddHeader("userid", userid); }
    //     if (methodType == 1)
    //     {
    //         request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
    //     }
    //     var response = await client.ExecuteAsync(request);
    //     return response.Content;
    // }

    public static async Task<string> RealmMethod(string json, string url, int methodType, string userid = "")
    {
        var client = new RestSharp.RestClient(url);
        var request = new RestRequest();
        request.AddHeader("content-type", "application/json;charset=UTF-8");
        if (!string.IsNullOrEmpty(userid))
        {
            request.AddHeader("userid", userid);
        }

        if (methodType == 1)
        {
            request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
        }

        var response = await (methodType == 1 ? client.PostAsync(request) : client.GetAsync(request));

        return response.Content;
    }


    // public static RestResponse GenericMethodReturnResponse(string json, string url, int methodType, string access_token = "0")
    // {
    //     var client = new RestSharp.RestClient(url);
    //     var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
    //     request.AddHeader("content-type", "application/json;charset=UTF-8");
    //     if (access_token != "0") { request.AddHeader("authorization", "Bearer " + access_token); }
    //     request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
    //     var response = client.Execute(request);
    //     return response;
    // }

    public static async Task<RestResponse> GenericMethodReturnResponse(string json, string url, int methodType,
        string access_token = "0")
    {
        var client = new RestClient(url);
        var request = new RestRequest();
        request.AddHeader("content-type", "application/json;charset=UTF-8");
        if (access_token != "0")
        {
            request.AddHeader("authorization", "Bearer " + access_token);
        }

        RestResponse response = null;
        response = methodType == 1 ? await client.GetAsync(request) : await client.PostAsync(request);

        return response;
    }


    // public static RestResponse IntercomRestApi(string json, string url, string access_token)
    // {
    //     var client = new RestClient(url);
    //     var request = new RestRequest(Method.POST);
    //     request.AddHeader("Content-Type", "application/json");
    //     request.AddHeader("Authorization", "Bearer " + access_token);
    //     request.AddHeader("Accept", "application/json");
    //     request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
    //     var response = client.Execute(request);
    //     return response;
    // }
    
    public static RestResponse IntercomRestApi(string json, string url, string access_token)
    {
        var client = new RestClient(url);
        var request = new RestRequest();
        request.AddHeader("Content-Type", "application/json");
        request.AddHeader("Authorization", "Bearer " + access_token);
        request.AddHeader("Accept", "application/json");
        request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
        var response = client.Post(request);
        return response;
    }

    // public static IRestResponse SendGridRestApi(string json, string url, string api_key)
    // {
    //     var client = new RestClient(url);
    //     var request = new RestRequest(Method.POST);
    //     request.AddHeader("Content-Type", "application/json");
    //     request.AddHeader("Authorization", "Bearer " + api_key);
    //     request.AddHeader("Accept", "application/json");
    //     request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
    //     var response = client.Execute(request);
    //     return response;
    // }
    public static RestResponse SendGridRestApi(string json, string url, string api_key)
    {
        var client = new RestClient(url);
        var request = new RestRequest();
        request.AddHeader("Content-Type", "application/json");
        request.AddHeader("Authorization", "Bearer " + api_key);
        request.AddHeader("Accept", "application/json");
        request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
        var response = client.Post(request);
        return response;
    }

    // public static RestResponse SendGridAddContactRestApi(string json, string url, string api_key)
    // {
    //     var client = new RestClient(url);
    //     var request = new RestRequest(Method.PUT);
    //     request.AddHeader("Content-Type", "application/json");
    //     request.AddHeader("Authorization", "Bearer " + api_key);
    //     request.AddHeader("Accept", "application/json");
    //     request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
    //     var response = client.Execute(request);
    //     return response;
    // }
    public static RestResponse SendGridAddContactRestApi(string json, string url, string api_key)
    {
        var client = new RestClient(url);
        var request = new RestRequest();
        request.AddHeader("Content-Type", "application/json");
        request.AddHeader("Authorization", "Bearer " + api_key);
        request.AddHeader("Accept", "application/json");
        request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
        var response = client.Put(request);
        return response;
    }

    // public static RestResponse GenericMethodToGetFromDB(string json, string url, int methodType,
    //     string access_token = "0")
    // {
    //     var client = new RestSharp.RestClient(url);
    //     var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
    //     request.AddHeader("content-type", "application/json;charset=UTF-8");
    //     if (access_token != "0")
    //     {
    //         request.AddHeader("authtoken", access_token);
    //     }
    //
    //     request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
    //     var response = client.Execute(request);
    //     return response;
    // }
    public static RestResponse GenericMethodToGetFromDB(string json, string url, int methodType,
        string access_token = "0")
    {
        var client = new RestSharp.RestClient(url);
        var request = new RestRequest();
        
        request.AddHeader("content-type", "application/json;charset=UTF-8");
        if (access_token != "0")
        {
            request.AddHeader("authtoken", access_token);
        }

        request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);

        var response = (methodType == 1) ? client.Post(request) : client.Get(request);
       
        return response;
    }


    // public static async Task<string> GenericMethod2(string json, string url, int methodType, string access_token = "0")
    // {
    //     var client = new RestSharp.RestClient(url);
    //     var request = (methodType == 1) ? new RestRequest(Method.POST) : new RestRequest(Method.GET);
    //     request.AddHeader("content-type", "application/json;charset=UTF-8");
    //     if (access_token != "0")
    //     {
    //         request.AddHeader("authorization", "Bearer " + access_token);
    //     }
    //
    //     request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
    //     var response = await client.ExecuteAsync(request);
    //     return response.Content;
    // }
    //
    public static async Task<string> GenericMethod2(string json, string url, int methodType, string access_token = "0")
    {
        var client = new RestSharp.RestClient(url);

        var request = new RestRequest();
        request.AddHeader("content-type", "application/json;charset=UTF-8");
        if (access_token != "0")
        {
            request.AddHeader("authorization", "Bearer " + access_token);
        }

        request.AddParameter("application/json;charset=UTF-8", json, ParameterType.RequestBody);
        var response = (methodType == 1) ? client.PostAsync(request) : client.GetAsync(request);
        return response.Result.Content;
    }

    //public static async Task<string> office365AccountToken(string url, string refreshToken, string client_id, string client_secret)
    //{
    //    var client = new RestClient(url);
    //    var request = new RestRequest(Method.POST);
    //    request.AddHeader("content-type", "application/x-www-form-urlencoded");
    //    request.AddParameter("application/x-www-form-urlencoded", "grant_type=refresh_token&" +
    //        "refresh_token=" + refreshToken +
    //        "&client_id=" + client_id +
    //        "&client_secret=" + client_secret
    //        , ParameterType.RequestBody);
    //    var response = await client.ExecuteTaskAsync(request);
    //    return response.Content;
    //}

    #region SanitizeNumber Methods

    static bool Contains(this string keyString, char c)
    {
        return keyString.IndexOf(c) >= 0;
    }

    public static string SanitizeNumber(string phoneNumber)
    {
        try
        {
            phoneNumber = phoneNumber.Replace("-", "").Replace("(", "").Replace(")", "").Replace(" ", "")
                .Replace(".", "").Replace(",", "");
            if (phoneNumber.StartsWith("0"))
            {
                if (phoneNumber.Length > 10)
                    phoneNumber = phoneNumber.Substring(1);
                phoneNumber = phoneNumber.TrimStart('0');
            }

            if (phoneNumber.StartsWith("00"))
            {
                phoneNumber = phoneNumber.Substring(2);
                phoneNumber = "+" + phoneNumber;
            }

            return phoneNumber;
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public static string SanitizeNumber(string phoneNumber, string userCountryId)
    {
        try
        {
            com.google.i18n.phonenumbers.PhoneNumberUtil phoneUtil =
                com.google.i18n.phonenumbers.PhoneNumberUtil.getInstance();
            string snumber = "";
            int PType = 0;
            phoneNumber = phoneNumber.Replace(" ", "");
            phoneNumber = (phoneNumber.StartsWith("+") ? "+" : "") + PhoneWord.ToNumber(phoneNumber);
            PhoneNumber numberProto;
            if (phoneNumber.StartsWith("+"))
            {
                numberProto = phoneUtil.parse(phoneNumber, "");
                string type = phoneUtil.getNumberType(numberProto).ToString();
                PType = (int)Enum.Parse(typeof(PhoneNumberUtil.PhoneNumberType), type);

                if (numberProto.IsPossibleNumber)
                {
                    snumber = phoneUtil.format(numberProto,
                        com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat.INTERNATIONAL);
                    return snumber + "|" + phoneUtil.getRegionCodeForNumber(numberProto) + "|" +
                           phoneUtil.format(numberProto,
                               com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat.E164) + "|" + PType;
                }
            }

            numberProto = phoneUtil.parse(phoneNumber, userCountryId);
            if (numberProto.IsPossibleNumber)
            {
                string type = phoneUtil.getNumberType(numberProto).ToString();
                PType = (int)Enum.Parse(typeof(PhoneNumberUtil.PhoneNumberType), type);

                snumber = phoneUtil.format(numberProto,
                    com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat.INTERNATIONAL);
                return snumber + "|" + phoneUtil.getRegionCodeForNumber(numberProto) + "|" +
                       phoneUtil.format(numberProto,
                           com.google.i18n.phonenumbers.PhoneNumberUtil.PhoneNumberFormat.E164) + "|" + PType;
            }
            else
            {
                return phoneNumber + "| IN | | ";
            }
        }
        catch (Exception)
        {
            return "";
        }
    }

    public static string SanitizeNumberE164(string phoneNumber, string countryPrefix)
    {
        var snumber = "";
        PhoneNumbers.PhoneNumberUtil util = PhoneNumbers.PhoneNumberUtil.GetInstance();
        try
        {
            PhoneNumbers.PhoneNumber number = util.Parse(phoneNumber, countryPrefix);
            if (util.IsValidNumber(number))
            {
                snumber = util.Format(number, PhoneNumbers.PhoneNumberFormat.E164);
                return snumber;
            }
            else
                return "";
        }
        catch (Exception ex)
        {
            return "";
        }
    }

    public static string SanitizeNumberForContactPhone(string phoneNumber, string userCountryId)
    {
        try
        {
            PhoneNumbers.PhoneNumberUtil phoneUtil = PhoneNumbers.PhoneNumberUtil.GetInstance();
            string snumber = "";
            int PType = 0;
            phoneNumber = phoneNumber.Replace(" ", "");
            phoneNumber = (phoneNumber.StartsWith("+") ? "+" : "") + PhoneWord.ToNumber(phoneNumber);
            PhoneNumbers.PhoneNumber numberProto;
            if (phoneNumber.StartsWith("+"))
            {
                numberProto = phoneUtil.ParseAndKeepRawInput(phoneNumber, "");
                string type = phoneUtil.GetNumberType(numberProto).ToString();
                PType = (int)Enum.Parse(typeof(PhoneNumbers.PhoneNumberType), type);

                if (phoneUtil.IsPossibleNumber(numberProto))
                {
                    snumber = phoneUtil.Format(numberProto, PhoneNumbers.PhoneNumberFormat.NATIONAL);
                    snumber = SanitizeNumber(snumber);
                    return snumber + "|" + phoneUtil.GetRegionCodeForNumber(numberProto) + "|" +
                           phoneUtil.Format(numberProto, PhoneNumbers.PhoneNumberFormat.E164) + "|" + PType;
                }
            }

            numberProto = phoneUtil.ParseAndKeepRawInput(phoneNumber, userCountryId);
            if (phoneUtil.IsPossibleNumber(numberProto))
            {
                PhoneNumbers.PhoneNumber altNumberProto = new PhoneNumbers.PhoneNumber();
                bool altInvalid = false;
                try
                {
                    altNumberProto = phoneUtil.ParseAndKeepRawInput("+" + phoneNumber, "");
                    altInvalid = !phoneUtil.IsPossibleNumber(altNumberProto);
                }
                catch (Exception)
                {
                    altInvalid = true;
                }

                if (!phoneUtil.IsValidNumber(numberProto) && !altInvalid)
                {
                    string type = phoneUtil.GetNumberType(altNumberProto).ToString();
                    PType = (int)Enum.Parse(typeof(PhoneNumberUtil.PhoneNumberType), type);

                    if (phoneUtil.IsPossibleNumber(altNumberProto))
                    {
                        snumber = phoneUtil.Format(altNumberProto, PhoneNumbers.PhoneNumberFormat.NATIONAL);
                        snumber = SanitizeNumber(snumber);
                        return snumber + "|" + phoneUtil.GetRegionCodeForNumber(altNumberProto) + "|" +
                               phoneUtil.Format(altNumberProto, PhoneNumbers.PhoneNumberFormat.E164) + "|" + PType;
                    }
                }
                else
                {
                    string type = phoneUtil.GetNumberType(numberProto).ToString();
                    PType = (int)Enum.Parse(typeof(PhoneNumberUtil.PhoneNumberType), type);

                    snumber = phoneUtil.Format(numberProto, PhoneNumbers.PhoneNumberFormat.NATIONAL);
                    snumber = SanitizeNumber(snumber);
                    return snumber + "|" + phoneUtil.GetRegionCodeForNumber(numberProto) + "|" +
                           phoneUtil.Format(numberProto, PhoneNumbers.PhoneNumberFormat.E164) + "|" + PType;
                }
            }

            return phoneNumber + "| IN | | ";
        }
        catch (Exception ex)
        {
            string op = ex.ToString();
            Console.WriteLine(op);
            return "";
        }
    }

    public class PhoneWord
    {
        public static string ToNumber(string raw)
        {
            if (string.IsNullOrWhiteSpace(raw))
                return "";
            else
                raw = raw.ToUpperInvariant();

            var newNumber = new StringBuilder();
            foreach (var c in raw)
            {
                if (" -0123456789".Contains(c))
                    newNumber.Append(c);
                else
                {
                    var result = TranslateToNumber(c);
                    if (result != null)
                        newNumber.Append(result);
                }
                // otherwise we've skipped a non-numeric char
            }

            return newNumber.ToString();
        }


        static int? TranslateToNumber(char c)
        {
            if ("ABC".Contains(c))
                return 2;
            else if ("DEF".Contains(c))
                return 3;
            else if ("GHI".Contains(c))
                return 4;
            else if ("JKL".Contains(c))
                return 5;
            else if ("MNO".Contains(c))
                return 6;
            else if ("PQRS".Contains(c))
                return 7;
            else if ("TUV".Contains(c))
                return 8;
            else if ("WXYZ".Contains(c))
                return 9;
            return null;
        }
    }

    #endregion

    public static class StopwordTool
    {
        /// <summary>
        /// Words we want to remove.
        /// </summary>
        static Dictionary<string, bool> _stops = new Dictionary<string, bool>
        {
            { "a", true },
            { "about", true },
            { "above", true },
            { "across", true },
            { "after", true },
            { "afterwards", true },
            { "again", true },
            { "against", true },
            { "all", true },
            { "almost", true },
            { "alone", true },
            { "along", true },
            { "already", true },
            { "also", true },
            { "although", true },
            { "always", true },
            { "am", true },
            { "among", true },
            { "amongst", true },
            { "amount", true },
            { "an", true },
            { "and", true },
            { "another", true },
            { "any", true },
            { "anyhow", true },
            { "anyone", true },
            { "anything", true },
            { "anyway", true },
            { "anywhere", true },
            { "are", true },
            { "around", true },
            { "as", true },
            { "at", true },
            { "back", true },
            { "be", true },
            { "became", true },
            { "because", true },
            { "become", true },
            { "becomes", true },
            { "becoming", true },
            { "been", true },
            { "before", true },
            { "beforehand", true },
            { "behind", true },
            { "being", true },
            { "below", true },
            { "beside", true },
            { "besides", true },
            { "between", true },
            { "beyond", true },
            { "bill", true },
            { "both", true },
            { "bottom", true },
            { "but", true },
            { "by", true },
            { "call", true },
            { "can", true },
            { "cannot", true },
            { "cant", true },
            { "co", true },
            { "computer", true },
            { "con", true },
            { "could", true },
            { "couldnt", true },
            { "cry", true },
            { "de", true },
            { "describe", true },
            { "detail", true },
            { "do", true },
            { "done", true },
            { "down", true },
            { "due", true },
            { "during", true },
            { "each", true },
            { "eg", true },
            { "eight", true },
            { "either", true },
            { "eleven", true },
            { "else", true },
            { "elsewhere", true },
            { "empty", true },
            { "enough", true },
            { "etc", true },
            { "even", true },
            { "ever", true },
            { "every", true },
            { "everyone", true },
            { "everything", true },
            { "everywhere", true },
            { "except", true },
            { "few", true },
            { "fifteen", true },
            { "fify", true },
            { "fill", true },
            { "find", true },
            { "fire", true },
            { "first", true },
            { "five", true },
            { "for", true },
            { "former", true },
            { "formerly", true },
            { "forty", true },
            { "found", true },
            { "four", true },
            { "from", true },
            { "front", true },
            { "full", true },
            { "further", true },
            { "get", true },
            { "give", true },
            { "go", true },
            { "had", true },
            { "has", true },
            { "have", true },
            { "he", true },
            { "hence", true },
            { "her", true },
            { "here", true },
            { "hereafter", true },
            { "hereby", true },
            { "herein", true },
            { "hereupon", true },
            { "hers", true },
            { "herself", true },
            { "him", true },
            { "himself", true },
            { "his", true },
            { "how", true },
            { "however", true },
            { "hundred", true },
            { "i", true },
            { "ie", true },
            { "if", true },
            { "in", true },
            { "inc", true },
            { "indeed", true },
            { "interest", true },
            { "into", true },
            { "is", true },
            { "it", true },
            { "its", true },
            { "itself", true },
            { "keep", true },
            { "last", true },
            { "latter", true },
            { "latterly", true },
            { "least", true },
            { "less", true },
            { "ltd", true },
            { "made", true },
            { "many", true },
            { "may", true },
            { "me", true },
            { "meanwhile", true },
            { "might", true },
            { "mill", true },
            { "mine", true },
            { "more", true },
            { "moreover", true },
            { "most", true },
            { "mostly", true },
            { "move", true },
            { "much", true },
            { "must", true },
            { "my", true },
            { "myself", true },
            { "name", true },
            { "namely", true },
            { "neither", true },
            { "never", true },
            { "nevertheless", true },
            { "next", true },
            { "nine", true },
            { "no", true },
            { "nobody", true },
            { "none", true },
            { "nor", true },
            { "not", true },
            { "nothing", true },
            { "now", true },
            { "nowhere", true },
            { "of", true },
            { "off", true },
            { "often", true },
            { "on", true },
            { "once", true },
            { "one", true },
            { "only", true },
            { "onto", true },
            { "or", true },
            { "other", true },
            { "others", true },
            { "otherwise", true },
            { "our", true },
            { "ours", true },
            { "ourselves", true },
            { "out", true },
            { "over", true },
            { "own", true },
            { "part", true },
            { "per", true },
            { "perhaps", true },
            { "please", true },
            { "put", true },
            { "rather", true },
            { "re", true },
            { "same", true },
            { "see", true },
            { "seem", true },
            { "seemed", true },
            { "seeming", true },
            { "seems", true },
            { "serious", true },
            { "several", true },
            { "she", true },
            { "should", true },
            { "show", true },
            { "side", true },
            { "since", true },
            { "sincere", true },
            { "six", true },
            { "sixty", true },
            { "so", true },
            { "some", true },
            { "somehow", true },
            { "someone", true },
            { "something", true },
            { "sometime", true },
            { "sometimes", true },
            { "somewhere", true },
            { "still", true },
            { "such", true },
            { "system", true },
            { "take", true },
            { "ten", true },
            { "than", true },
            { "that", true },
            { "the", true },
            { "their", true },
            { "them", true },
            { "themselves", true },
            { "then", true },
            { "thence", true },
            { "there", true },
            { "thereafter", true },
            { "thereby", true },
            { "therefore", true },
            { "therein", true },
            { "thereupon", true },
            { "these", true },
            { "they", true },
            { "thick", true },
            { "thin", true },
            { "third", true },
            { "this", true },
            { "those", true },
            { "though", true },
            { "three", true },
            { "through", true },
            { "throughout", true },
            { "thru", true },
            { "thus", true },
            { "to", true },
            { "together", true },
            { "too", true },
            { "top", true },
            { "toward", true },
            { "towards", true },
            { "twelve", true },
            { "twenty", true },
            { "two", true },
            { "un", true },
            { "under", true },
            { "until", true },
            { "up", true },
            { "upon", true },
            { "us", true },
            { "very", true },
            { "via", true },
            { "was", true },
            { "we", true },
            { "well", true },
            { "were", true },
            { "what", true },
            { "whatever", true },
            { "when", true },
            { "whence", true },
            { "whenever", true },
            { "where", true },
            { "whereafter", true },
            { "whereas", true },
            { "whereby", true },
            { "wherein", true },
            { "whereupon", true },
            { "wherever", true },
            { "whether", true },
            { "which", true },
            { "while", true },
            { "whither", true },
            { "who", true },
            { "whoever", true },
            { "whole", true },
            { "whom", true },
            { "whose", true },
            { "why", true },
            { "will", true },
            { "with", true },
            { "within", true },
            { "without", true },
            { "would", true },
            { "yet", true },
            { "you", true },
            { "your", true },
            { "yours", true },
            { "yourself", true },
            { "yourselves", true }
        };

        static char[] _delimiters = new char[]
        {
            ' ',
            ',',
            ';',
            '.'
        };

        public static List<string> RemoveStopwords(string input)
        {
            List<string> flattenList = new List<string>();
            // 1
            // Split parameter into words
            var words = input.Split(_delimiters,
                StringSplitOptions.RemoveEmptyEntries);
            // 2
            // Allocate new dictionary to store found words
            var found = new Dictionary<string, bool>();
            // 3
            // Store results in this StringBuilder
            StringBuilder builder = new StringBuilder();
            // 4
            // Loop through all words
            foreach (string currentWord in words)
            {
                // 5
                // Convert to lowercase
                string lowerWord = currentWord.ToLower();
                // 6
                // If this is a usable word, add it
                if (!_stops.ContainsKey(lowerWord) &&
                    !found.ContainsKey(lowerWord))
                {
                    builder.Append(currentWord).Append(' ');
                    flattenList.Add(lowerWord);
                    found.Add(lowerWord, true);
                }
            }


            // Return string with words removed
            return flattenList;
        }
    }

    public class FileExtensionToMimeType
    {
        private static IDictionary<string, string> _mappings =
            new Dictionary<string, string>(StringComparer.InvariantCultureIgnoreCase)
            {
                #region Big freaking list of mime types

                // combination of values from Windows 7 Registry and 
                // from C:\Windows\System32\inetsrv\config\applicationHost.config
                // some added, including .7z and .dat
                { ".323", "text/h323" },
                { ".3g2", "video/3gpp2" },
                { ".3gp", "video/3gpp" },
                { ".3gp2", "video/3gpp2" },
                { ".3gpp", "video/3gpp" },
                { ".7z", "application/x-7z-compressed" },
                { ".aa", "audio/audible" },
                { ".AAC", "audio/aac" },
                { ".aaf", "application/octet-stream" },
                { ".aax", "audio/vnd.audible.aax" },
                { ".ac3", "audio/ac3" },
                { ".aca", "application/octet-stream" },
                { ".accda", "application/msaccess.addin" },
                { ".accdb", "application/msaccess" },
                { ".accdc", "application/msaccess.cab" },
                { ".accde", "application/msaccess" },
                { ".accdr", "application/msaccess.runtime" },
                { ".accdt", "application/msaccess" },
                { ".accdw", "application/msaccess.webapplication" },
                { ".accft", "application/msaccess.ftemplate" },
                { ".acx", "application/internet-property-stream" },
                { ".AddIn", "text/xml" },
                { ".ade", "application/msaccess" },
                { ".adobebridge", "application/x-bridge-url" },
                { ".adp", "application/msaccess" },
                { ".ADT", "audio/vnd.dlna.adts" },
                { ".ADTS", "audio/aac" },
                { ".afm", "application/octet-stream" },
                { ".ai", "application/postscript" },
                { ".aif", "audio/x-aiff" },
                { ".aifc", "audio/aiff" },
                { ".aiff", "audio/aiff" },
                { ".air", "application/vnd.adobe.air-application-installer-package+zip" },
                { ".amc", "application/x-mpeg" },
                { ".application", "application/x-ms-application" },
                { ".art", "image/x-jg" },
                { ".asa", "application/xml" },
                { ".asax", "application/xml" },
                { ".ascx", "application/xml" },
                { ".asd", "application/octet-stream" },
                { ".asf", "video/x-ms-asf" },
                { ".ashx", "application/xml" },
                { ".asi", "application/octet-stream" },
                { ".asm", "text/plain" },
                { ".asmx", "application/xml" },
                { ".aspx", "application/xml" },
                { ".asr", "video/x-ms-asf" },
                { ".asx", "video/x-ms-asf" },
                { ".atom", "application/atom+xml" },
                { ".au", "audio/basic" },
                { ".avi", "video/x-msvideo" },
                { ".axs", "application/olescript" },
                { ".bas", "text/plain" },
                { ".bcpio", "application/x-bcpio" },
                { ".bin", "application/octet-stream" },
                { ".bmp", "image/bmp" },
                { ".c", "text/plain" },
                { ".cab", "application/octet-stream" },
                { ".caf", "audio/x-caf" },
                { ".calx", "application/vnd.ms-office.calx" },
                { ".cat", "application/vnd.ms-pki.seccat" },
                { ".cc", "text/plain" },
                { ".cd", "text/plain" },
                { ".cdda", "audio/aiff" },
                { ".cdf", "application/x-cdf" },
                { ".cer", "application/x-x509-ca-cert" },
                { ".chm", "application/octet-stream" },
                { ".class", "application/x-java-applet" },
                { ".clp", "application/x-msclip" },
                { ".cmx", "image/x-cmx" },
                { ".cnf", "text/plain" },
                { ".cod", "image/cis-cod" },
                { ".config", "application/xml" },
                { ".contact", "text/x-ms-contact" },
                { ".coverage", "application/xml" },
                { ".cpio", "application/x-cpio" },
                { ".cpp", "text/plain" },
                { ".crd", "application/x-mscardfile" },
                { ".crl", "application/pkix-crl" },
                { ".crt", "application/x-x509-ca-cert" },
                { ".cs", "text/plain" },
                { ".csdproj", "text/plain" },
                { ".csh", "application/x-csh" },
                { ".csproj", "text/plain" },
                { ".css", "text/css" },
                { ".csv", "text/csv" },
                { ".cur", "application/octet-stream" },
                { ".cxx", "text/plain" },
                { ".dat", "application/octet-stream" },
                { ".datasource", "application/xml" },
                { ".dbproj", "text/plain" },
                { ".dcr", "application/x-director" },
                { ".def", "text/plain" },
                { ".deploy", "application/octet-stream" },
                { ".der", "application/x-x509-ca-cert" },
                { ".dgml", "application/xml" },
                { ".dib", "image/bmp" },
                { ".dif", "video/x-dv" },
                { ".dir", "application/x-director" },
                { ".disco", "text/xml" },
                { ".dll", "application/x-msdownload" },
                { ".dll.config", "text/xml" },
                { ".dlm", "text/dlm" },
                { ".doc", "application/msword" },
                { ".docm", "application/vnd.ms-word.document.macroEnabled.12" },
                { ".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" },
                { ".dot", "application/msword" },
                { ".dotm", "application/vnd.ms-word.template.macroEnabled.12" },
                { ".dotx", "application/vnd.openxmlformats-officedocument.wordprocessingml.template" },
                { ".dsp", "application/octet-stream" },
                { ".dsw", "text/plain" },
                { ".dtd", "text/xml" },
                { ".dtsConfig", "text/xml" },
                { ".dv", "video/x-dv" },
                { ".dvi", "application/x-dvi" },
                { ".dwf", "drawing/x-dwf" },
                { ".dwp", "application/octet-stream" },
                { ".dxr", "application/x-director" },
                { ".eml", "message/rfc822" },
                { ".emz", "application/octet-stream" },
                { ".eot", "application/octet-stream" },
                { ".eps", "application/postscript" },
                { ".etl", "application/etl" },
                { ".etx", "text/x-setext" },
                { ".evy", "application/envoy" },
                { ".exe", "application/octet-stream" },
                { ".exe.config", "text/xml" },
                { ".fdf", "application/vnd.fdf" },
                { ".fif", "application/fractals" },
                { ".filters", "Application/xml" },
                { ".fla", "application/octet-stream" },
                { ".flr", "x-world/x-vrml" },
                { ".flv", "video/x-flv" },
                { ".fsscript", "application/fsharp-script" },
                { ".fsx", "application/fsharp-script" },
                { ".generictest", "application/xml" },
                { ".gif", "image/gif" },
                { ".group", "text/x-ms-group" },
                { ".gsm", "audio/x-gsm" },
                { ".gtar", "application/x-gtar" },
                { ".gz", "application/x-gzip" },
                { ".h", "text/plain" },
                { ".hdf", "application/x-hdf" },
                { ".hdml", "text/x-hdml" },
                { ".hhc", "application/x-oleobject" },
                { ".hhk", "application/octet-stream" },
                { ".hhp", "application/octet-stream" },
                { ".hlp", "application/winhlp" },
                { ".hpp", "text/plain" },
                { ".hqx", "application/mac-binhex40" },
                { ".hta", "application/hta" },
                { ".htc", "text/x-component" },
                { ".htm", "text/html" },
                { ".html", "text/html" },
                { ".htt", "text/webviewhtml" },
                { ".hxa", "application/xml" },
                { ".hxc", "application/xml" },
                { ".hxd", "application/octet-stream" },
                { ".hxe", "application/xml" },
                { ".hxf", "application/xml" },
                { ".hxh", "application/octet-stream" },
                { ".hxi", "application/octet-stream" },
                { ".hxk", "application/xml" },
                { ".hxq", "application/octet-stream" },
                { ".hxr", "application/octet-stream" },
                { ".hxs", "application/octet-stream" },
                { ".hxt", "text/html" },
                { ".hxv", "application/xml" },
                { ".hxw", "application/octet-stream" },
                { ".hxx", "text/plain" },
                { ".i", "text/plain" },
                { ".ico", "image/x-icon" },
                { ".ics", "application/octet-stream" },
                { ".idl", "text/plain" },
                { ".ief", "image/ief" },
                { ".iii", "application/x-iphone" },
                { ".inc", "text/plain" },
                { ".inf", "application/octet-stream" },
                { ".inl", "text/plain" },
                { ".ins", "application/x-internet-signup" },
                { ".ipa", "application/x-itunes-ipa" },
                { ".ipg", "application/x-itunes-ipg" },
                { ".ipproj", "text/plain" },
                { ".ipsw", "application/x-itunes-ipsw" },
                { ".iqy", "text/x-ms-iqy" },
                { ".isp", "application/x-internet-signup" },
                { ".ite", "application/x-itunes-ite" },
                { ".itlp", "application/x-itunes-itlp" },
                { ".itms", "application/x-itunes-itms" },
                { ".itpc", "application/x-itunes-itpc" },
                { ".IVF", "video/x-ivf" },
                { ".jar", "application/java-archive" },
                { ".java", "application/octet-stream" },
                { ".jck", "application/liquidmotion" },
                { ".jcz", "application/liquidmotion" },
                { ".jfif", "image/pjpeg" },
                { ".jnlp", "application/x-java-jnlp-file" },
                { ".jpb", "application/octet-stream" },
                { ".jpe", "image/jpeg" },
                { ".jpeg", "image/jpeg" },
                { ".jpg", "image/jpeg" },
                { ".js", "application/x-javascript" },
                { ".jsx", "text/jscript" },
                { ".jsxbin", "text/plain" },
                { ".latex", "application/x-latex" },
                { ".library-ms", "application/windows-library+xml" },
                { ".lit", "application/x-ms-reader" },
                { ".loadtest", "application/xml" },
                { ".lpk", "application/octet-stream" },
                { ".lsf", "video/x-la-asf" },
                { ".lst", "text/plain" },
                { ".lsx", "video/x-la-asf" },
                { ".lzh", "application/octet-stream" },
                { ".m13", "application/x-msmediaview" },
                { ".m14", "application/x-msmediaview" },
                { ".m1v", "video/mpeg" },
                { ".m2t", "video/vnd.dlna.mpeg-tts" },
                { ".m2ts", "video/vnd.dlna.mpeg-tts" },
                { ".m2v", "video/mpeg" },
                { ".m3u", "audio/x-mpegurl" },
                { ".m3u8", "audio/x-mpegurl" },
                { ".m4a", "audio/m4a" },
                { ".m4b", "audio/m4b" },
                { ".m4p", "audio/m4p" },
                { ".m4r", "audio/x-m4r" },
                { ".m4v", "video/x-m4v" },
                { ".mac", "image/x-macpaint" },
                { ".mak", "text/plain" },
                { ".man", "application/x-troff-man" },
                { ".manifest", "application/x-ms-manifest" },
                { ".map", "text/plain" },
                { ".master", "application/xml" },
                { ".mda", "application/msaccess" },
                { ".mdb", "application/x-msaccess" },
                { ".mde", "application/msaccess" },
                { ".mdp", "application/octet-stream" },
                { ".me", "application/x-troff-me" },
                { ".mfp", "application/x-shockwave-flash" },
                { ".mht", "message/rfc822" },
                { ".mhtml", "message/rfc822" },
                { ".mid", "audio/mid" },
                { ".midi", "audio/mid" },
                { ".mix", "application/octet-stream" },
                { ".mk", "text/plain" },
                { ".mmf", "application/x-smaf" },
                { ".mno", "text/xml" },
                { ".mny", "application/x-msmoney" },
                { ".mod", "video/mpeg" },
                { ".mov", "video/quicktime" },
                { ".movie", "video/x-sgi-movie" },
                { ".mp2", "video/mpeg" },
                { ".mp2v", "video/mpeg" },
                { ".mp3", "audio/mpeg" },
                { ".mp4", "video/mp4" },
                { ".mp4v", "video/mp4" },
                { ".mpa", "video/mpeg" },
                { ".mpe", "video/mpeg" },
                { ".mpeg", "video/mpeg" },
                { ".mpf", "application/vnd.ms-mediapackage" },
                { ".mpg", "video/mpeg" },
                { ".mpp", "application/vnd.ms-project" },
                { ".mpv2", "video/mpeg" },
                { ".mqv", "video/quicktime" },
                { ".ms", "application/x-troff-ms" },
                { ".msi", "application/octet-stream" },
                { ".mso", "application/octet-stream" },
                { ".mts", "video/vnd.dlna.mpeg-tts" },
                { ".mtx", "application/xml" },
                { ".mvb", "application/x-msmediaview" },
                { ".mvc", "application/x-miva-compiled" },
                { ".mxp", "application/x-mmxp" },
                { ".nc", "application/x-netcdf" },
                { ".nsc", "video/x-ms-asf" },
                { ".nws", "message/rfc822" },
                { ".ocx", "application/octet-stream" },
                { ".oda", "application/oda" },
                { ".odc", "text/x-ms-odc" },
                { ".odh", "text/plain" },
                { ".odl", "text/plain" },
                { ".odp", "application/vnd.oasis.opendocument.presentation" },
                { ".ods", "application/oleobject" },
                { ".odt", "application/vnd.oasis.opendocument.text" },
                { ".one", "application/onenote" },
                { ".onea", "application/onenote" },
                { ".onepkg", "application/onenote" },
                { ".onetmp", "application/onenote" },
                { ".onetoc", "application/onenote" },
                { ".onetoc2", "application/onenote" },
                { ".orderedtest", "application/xml" },
                { ".osdx", "application/opensearchdescription+xml" },
                { ".p10", "application/pkcs10" },
                { ".p12", "application/x-pkcs12" },
                { ".p7b", "application/x-pkcs7-certificates" },
                { ".p7c", "application/pkcs7-mime" },
                { ".p7m", "application/pkcs7-mime" },
                { ".p7r", "application/x-pkcs7-certreqresp" },
                { ".p7s", "application/pkcs7-signature" },
                { ".pbm", "image/x-portable-bitmap" },
                { ".pcast", "application/x-podcast" },
                { ".pct", "image/pict" },
                { ".pcx", "application/octet-stream" },
                { ".pcz", "application/octet-stream" },
                { ".pdf", "application/pdf" },
                { ".pfb", "application/octet-stream" },
                { ".pfm", "application/octet-stream" },
                { ".pfx", "application/x-pkcs12" },
                { ".pgm", "image/x-portable-graymap" },
                { ".pic", "image/pict" },
                { ".pict", "image/pict" },
                { ".pkgdef", "text/plain" },
                { ".pkgundef", "text/plain" },
                { ".pko", "application/vnd.ms-pki.pko" },
                { ".pls", "audio/scpls" },
                { ".pma", "application/x-perfmon" },
                { ".pmc", "application/x-perfmon" },
                { ".pml", "application/x-perfmon" },
                { ".pmr", "application/x-perfmon" },
                { ".pmw", "application/x-perfmon" },
                { ".png", "image/png" },
                { ".pnm", "image/x-portable-anymap" },
                { ".pnt", "image/x-macpaint" },
                { ".pntg", "image/x-macpaint" },
                { ".pnz", "image/png" },
                { ".pot", "application/vnd.ms-powerpoint" },
                { ".potm", "application/vnd.ms-powerpoint.template.macroEnabled.12" },
                { ".potx", "application/vnd.openxmlformats-officedocument.presentationml.template" },
                { ".ppa", "application/vnd.ms-powerpoint" },
                { ".ppam", "application/vnd.ms-powerpoint.addin.macroEnabled.12" },
                { ".ppm", "image/x-portable-pixmap" },
                { ".pps", "application/vnd.ms-powerpoint" },
                { ".ppsm", "application/vnd.ms-powerpoint.slideshow.macroEnabled.12" },
                { ".ppsx", "application/vnd.openxmlformats-officedocument.presentationml.slideshow" },
                { ".ppt", "application/vnd.ms-powerpoint" },
                { ".pptm", "application/vnd.ms-powerpoint.presentation.macroEnabled.12" },
                { ".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation" },
                { ".prf", "application/pics-rules" },
                { ".prm", "application/octet-stream" },
                { ".prx", "application/octet-stream" },
                { ".ps", "application/postscript" },
                { ".psc1", "application/PowerShell" },
                { ".psd", "application/octet-stream" },
                { ".psess", "application/xml" },
                { ".psm", "application/octet-stream" },
                { ".psp", "application/octet-stream" },
                { ".pub", "application/x-mspublisher" },
                { ".pwz", "application/vnd.ms-powerpoint" },
                { ".qht", "text/x-html-insertion" },
                { ".qhtm", "text/x-html-insertion" },
                { ".qt", "video/quicktime" },
                { ".qti", "image/x-quicktime" },
                { ".qtif", "image/x-quicktime" },
                { ".qtl", "application/x-quicktimeplayer" },
                { ".qxd", "application/octet-stream" },
                { ".ra", "audio/x-pn-realaudio" },
                { ".ram", "audio/x-pn-realaudio" },
                { ".rar", "application/octet-stream" },
                { ".ras", "image/x-cmu-raster" },
                { ".rat", "application/rat-file" },
                { ".rc", "text/plain" },
                { ".rc2", "text/plain" },
                { ".rct", "text/plain" },
                { ".rdlc", "application/xml" },
                { ".resx", "application/xml" },
                { ".rf", "image/vnd.rn-realflash" },
                { ".rgb", "image/x-rgb" },
                { ".rgs", "text/plain" },
                { ".rm", "application/vnd.rn-realmedia" },
                { ".rmi", "audio/mid" },
                { ".rmp", "application/vnd.rn-rn_music_package" },
                { ".roff", "application/x-troff" },
                { ".rpm", "audio/x-pn-realaudio-plugin" },
                { ".rqy", "text/x-ms-rqy" },
                { ".rtf", "application/rtf" },
                { ".rtx", "text/richtext" },
                { ".ruleset", "application/xml" },
                { ".s", "text/plain" },
                { ".safariextz", "application/x-safari-safariextz" },
                { ".scd", "application/x-msschedule" },
                { ".sct", "text/scriptlet" },
                { ".sd2", "audio/x-sd2" },
                { ".sdp", "application/sdp" },
                { ".sea", "application/octet-stream" },
                { ".searchConnector-ms", "application/windows-search-connector+xml" },
                { ".setpay", "application/set-payment-initiation" },
                { ".setreg", "application/set-registration-initiation" },
                { ".settings", "application/xml" },
                { ".sgimb", "application/x-sgimb" },
                { ".sgml", "text/sgml" },
                { ".sh", "application/x-sh" },
                { ".shar", "application/x-shar" },
                { ".shtml", "text/html" },
                { ".sit", "application/x-stuffit" },
                { ".sitemap", "application/xml" },
                { ".skin", "application/xml" },
                { ".sldm", "application/vnd.ms-powerpoint.slide.macroEnabled.12" },
                { ".sldx", "application/vnd.openxmlformats-officedocument.presentationml.slide" },
                { ".slk", "application/vnd.ms-excel" },
                { ".sln", "text/plain" },
                { ".slupkg-ms", "application/x-ms-license" },
                { ".smd", "audio/x-smd" },
                { ".smi", "application/octet-stream" },
                { ".smx", "audio/x-smd" },
                { ".smz", "audio/x-smd" },
                { ".snd", "audio/basic" },
                { ".snippet", "application/xml" },
                { ".snp", "application/octet-stream" },
                { ".sol", "text/plain" },
                { ".sor", "text/plain" },
                { ".spc", "application/x-pkcs7-certificates" },
                { ".spl", "application/futuresplash" },
                { ".src", "application/x-wais-source" },
                { ".srf", "text/plain" },
                { ".SSISDeploymentManifest", "text/xml" },
                { ".ssm", "application/streamingmedia" },
                { ".sst", "application/vnd.ms-pki.certstore" },
                { ".stl", "application/vnd.ms-pki.stl" },
                { ".sv4cpio", "application/x-sv4cpio" },
                { ".sv4crc", "application/x-sv4crc" },
                { ".svc", "application/xml" },
                { ".swf", "application/x-shockwave-flash" },
                { ".t", "application/x-troff" },
                { ".tar", "application/x-tar" },
                { ".tcl", "application/x-tcl" },
                { ".testrunconfig", "application/xml" },
                { ".testsettings", "application/xml" },
                { ".tex", "application/x-tex" },
                { ".texi", "application/x-texinfo" },
                { ".texinfo", "application/x-texinfo" },
                { ".tgz", "application/x-compressed" },
                { ".thmx", "application/vnd.ms-officetheme" },
                { ".thn", "application/octet-stream" },
                { ".tif", "image/tiff" },
                { ".tiff", "image/tiff" },
                { ".tlh", "text/plain" },
                { ".tli", "text/plain" },
                { ".toc", "application/octet-stream" },
                { ".tr", "application/x-troff" },
                { ".trm", "application/x-msterminal" },
                { ".trx", "application/xml" },
                { ".ts", "video/vnd.dlna.mpeg-tts" },
                { ".tsv", "text/tab-separated-values" },
                { ".ttf", "application/octet-stream" },
                { ".tts", "video/vnd.dlna.mpeg-tts" },
                { ".txt", "text/plain" },
                { ".u32", "application/octet-stream" },
                { ".uls", "text/iuls" },
                { ".user", "text/plain" },
                { ".ustar", "application/x-ustar" },
                { ".vb", "text/plain" },
                { ".vbdproj", "text/plain" },
                { ".vbk", "video/mpeg" },
                { ".vbproj", "text/plain" },
                { ".vbs", "text/vbscript" },
                { ".vcf", "text/x-vcard" },
                { ".vcproj", "Application/xml" },
                { ".vcs", "text/plain" },
                { ".vcxproj", "Application/xml" },
                { ".vddproj", "text/plain" },
                { ".vdp", "text/plain" },
                { ".vdproj", "text/plain" },
                { ".vdx", "application/vnd.ms-visio.viewer" },
                { ".vml", "text/xml" },
                { ".vscontent", "application/xml" },
                { ".vsct", "text/xml" },
                { ".vsd", "application/vnd.visio" },
                { ".vsi", "application/ms-vsi" },
                { ".vsix", "application/vsix" },
                { ".vsixlangpack", "text/xml" },
                { ".vsixmanifest", "text/xml" },
                { ".vsmdi", "application/xml" },
                { ".vspscc", "text/plain" },
                { ".vss", "application/vnd.visio" },
                { ".vsscc", "text/plain" },
                { ".vssettings", "text/xml" },
                { ".vssscc", "text/plain" },
                { ".vst", "application/vnd.visio" },
                { ".vstemplate", "text/xml" },
                { ".vsto", "application/x-ms-vsto" },
                { ".vsw", "application/vnd.visio" },
                { ".vsx", "application/vnd.visio" },
                { ".vtx", "application/vnd.visio" },
                { ".wav", "audio/wav" },
                { ".wave", "audio/wav" },
                { ".wax", "audio/x-ms-wax" },
                { ".wbk", "application/msword" },
                { ".wbmp", "image/vnd.wap.wbmp" },
                { ".wcm", "application/vnd.ms-works" },
                { ".wdb", "application/vnd.ms-works" },
                { ".wdp", "image/vnd.ms-photo" },
                { ".webarchive", "application/x-safari-webarchive" },
                { ".webtest", "application/xml" },
                { ".wiq", "application/xml" },
                { ".wiz", "application/msword" },
                { ".wks", "application/vnd.ms-works" },
                { ".WLMP", "application/wlmoviemaker" },
                { ".wlpginstall", "application/x-wlpg-detect" },
                { ".wlpginstall3", "application/x-wlpg3-detect" },
                { ".wm", "video/x-ms-wm" },
                { ".wma", "audio/x-ms-wma" },
                { ".wmd", "application/x-ms-wmd" },
                { ".wmf", "application/x-msmetafile" },
                { ".wml", "text/vnd.wap.wml" },
                { ".wmlc", "application/vnd.wap.wmlc" },
                { ".wmls", "text/vnd.wap.wmlscript" },
                { ".wmlsc", "application/vnd.wap.wmlscriptc" },
                { ".wmp", "video/x-ms-wmp" },
                { ".wmv", "video/x-ms-wmv" },
                { ".wmx", "video/x-ms-wmx" },
                { ".wmz", "application/x-ms-wmz" },
                { ".wpl", "application/vnd.ms-wpl" },
                { ".wps", "application/vnd.ms-works" },
                { ".wri", "application/x-mswrite" },
                { ".wrl", "x-world/x-vrml" },
                { ".wrz", "x-world/x-vrml" },
                { ".wsc", "text/scriptlet" },
                { ".wsdl", "text/xml" },
                { ".wvx", "video/x-ms-wvx" },
                { ".x", "application/directx" },
                { ".xaf", "x-world/x-vrml" },
                { ".xaml", "application/xaml+xml" },
                { ".xap", "application/x-silverlight-app" },
                { ".xbap", "application/x-ms-xbap" },
                { ".xbm", "image/x-xbitmap" },
                { ".xdr", "text/plain" },
                { ".xht", "application/xhtml+xml" },
                { ".xhtml", "application/xhtml+xml" },
                { ".xla", "application/vnd.ms-excel" },
                { ".xlam", "application/vnd.ms-excel.addin.macroEnabled.12" },
                { ".xlc", "application/vnd.ms-excel" },
                { ".xld", "application/vnd.ms-excel" },
                { ".xlk", "application/vnd.ms-excel" },
                { ".xll", "application/vnd.ms-excel" },
                { ".xlm", "application/vnd.ms-excel" },
                { ".xls", "application/vnd.ms-excel" },
                { ".xlsb", "application/vnd.ms-excel.sheet.binary.macroEnabled.12" },
                { ".xlsm", "application/vnd.ms-excel.sheet.macroEnabled.12" },
                { ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
                { ".xlt", "application/vnd.ms-excel" },
                { ".xltm", "application/vnd.ms-excel.template.macroEnabled.12" },
                { ".xltx", "application/vnd.openxmlformats-officedocument.spreadsheetml.template" },
                { ".xlw", "application/vnd.ms-excel" },
                { ".xml", "text/xml" },
                { ".xmta", "application/xml" },
                { ".xof", "x-world/x-vrml" },
                { ".XOML", "text/plain" },
                { ".xpm", "image/x-xpixmap" },
                { ".xps", "application/vnd.ms-xpsdocument" },
                { ".xrm-ms", "text/xml" },
                { ".xsc", "application/xml" },
                { ".xsd", "text/xml" },
                { ".xsf", "text/xml" },
                { ".xsl", "text/xml" },
                { ".xslt", "text/xml" },
                { ".xsn", "application/octet-stream" },
                { ".xss", "application/xml" },
                { ".xtp", "application/octet-stream" },
                { ".xwd", "image/x-xwindowdump" },
                { ".z", "application/x-compress" },
                { ".zip", "application/x-zip-compressed" },

                #endregion
            };

        public static string GetMimeType(string extension)
        {
            if (extension == null)
            {
                throw new ArgumentNullException("extension");
            }

            if (!extension.StartsWith("."))
            {
                extension = "." + extension;
            }


            string mime;
            if (extension.Equals(".mp3"))

            {
                mime = "audio/mpeg";
                return mime;
            }


            return _mappings.TryGetValue(extension, out mime) ? mime : "application/octet-stream";
        }
    }

    public static class DateTimeHelpers
    {
        private static readonly DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        //public static string ToRelativeDate(DateTime dateTime)

        //{

        //    var timeSpan = DateTime.Now - dateTime;


        //    // span is less than or equal to 60 seconds, measure in seconds.

        //    if (timeSpan <= TimeSpan.FromSeconds(60))

        //    {

        //        return timeSpan.Seconds + " " + Resource.secondsago;

        //    }

        //    // span is less than or equal to 60 minutes, measure in minutes.

        //    if (timeSpan <= TimeSpan.FromMinutes(60))

        //    {

        //        return timeSpan.Minutes > 1

        //            ? timeSpan.Minutes + " " + Resource.Minutesago : Resource.Aboutaminuteago;

        //    }

        //    // span is less than or equal to 24 hours, measure in hours.

        //    if (timeSpan <= TimeSpan.FromHours(24))

        //    {

        //        return timeSpan.Hours > 1

        //            ? timeSpan.Hours + " " + Resource.hoursago : Resource.Aboutanhourago;

        //    }

        //    // span is less than or equal to 30 days (1 month), measure in days.

        //    if (timeSpan <= TimeSpan.FromDays(30))

        //    {

        //        return timeSpan.Days > 1

        //            ? timeSpan.Days + " " + Resource.daysago : Resource.Aboutadayago;

        //    }

        //    // span is less than or equal to 365 days (1 year), measure in months.

        //    if (timeSpan <= TimeSpan.FromDays(365))

        //    {

        //        return timeSpan.Days > 30

        //            ? timeSpan.Days / 30 + " " + Resource.Monthsago : Resource.Aboutamonthago;

        //    }

        //    // span is greater than 365 days (1 year), measure in years.

        //    return timeSpan.Days > 365

        //        ? timeSpan.Days / 365 + " " + Resource.Yearsago : Resource.Aboutayearago;

        //}

        public static DateTime UnixTimeStampToDateTime(double unixTimeStamp)
        {
            // Unix timestamp is seconds past epoch
            DateTime dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
            dtDateTime = dtDateTime.AddSeconds(unixTimeStamp).ToLocalTime();
            return dtDateTime;
        }

        public static long ToEpoch(DateTime datetime)
        {
            return Convert.ToInt64((datetime - epoch).TotalSeconds);
        }
    }

    public static string passwordEncryption(String password)
    {
        try
        {
            byte[] encData_byte = new byte[password.Length];
            encData_byte = System.Text.Encoding.UTF8.GetBytes(password);
            string encodedData = Convert.ToBase64String(encData_byte);
            return encodedData;
        }
        catch (Exception e)
        {
            throw new Exception("Error in base64Encode" + e.Message);
        }
    }

    public static string passwordDecription(string sData)
    {
        try
        {
            System.Text.UTF8Encoding encoder = new System.Text.UTF8Encoding();
            System.Text.Decoder utf8Decode = encoder.GetDecoder();
            byte[] todecode_byte = Convert.FromBase64String(sData);
            int charCount = utf8Decode.GetCharCount(todecode_byte, 0, todecode_byte.Length);
            char[] decoded_char = new char[charCount];
            utf8Decode.GetChars(todecode_byte, 0, todecode_byte.Length, decoded_char, 0);
            string result = new String(decoded_char);
            return result;
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public static string AddToStringList(string dest, string source)
    {
        string output = dest;
        if (!string.IsNullOrEmpty(dest) && !string.IsNullOrEmpty(source) && !dest.ToLower().Contains(source.ToLower()))
        {
            dest = dest + "," + source;
            output = dest;
        }
        else if (string.IsNullOrEmpty(dest) && !string.IsNullOrEmpty(source))
        {
            output = source;
        }

        return output;
    }

    public static string mixpanel()
    {
        try
        {
            using (WebClient wc = new WebClient())
            {
                wc.Headers.Add("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.2; .NET CLR 1.0.3705;)");
                byte[] creds = UTF8Encoding.UTF8.GetBytes("1d44f7a8e29de241eaed86366cf72a45");
                wc.Headers.Add("Authorization", "Basic " + Convert.ToBase64String(creds));
                var reqparm = new System.Collections.Specialized.NameValueCollection();
                //reqparm.Add("script", "function main() { return People({\"user_selectors\":[{\"selector\":}]\"2018-07-01\",\"to_date\":\"2018-08-03\"}).reduce(mixpanel.reducer.count()); }");
                // reqparm.Add("script", "function main(){  return People({user_selectors:[{selector:'has_prefix(string(user[\"Call Started\"]), \"1533028555\")'}]};");
                reqparm.Add("script",
                    "function main() { return Events({\"from_date\":\"2018-07-01\",\"to_date\":\"2018-08-03\"}).reduce(mixpanel.reducer.count()); }");
                byte[] responsebytes = wc.UploadValues("https://mixpanel.com/api/2.0/jql", "POST", reqparm);
                string result = System.Text.Encoding.UTF8.GetString(responsebytes);
                return result;
            }
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public static string AddSpacesToSentence(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return "";
        StringBuilder newText = new StringBuilder(text.Length * 2);
        newText.Append(text[0]);
        for (int i = 1; i < text.Length; i++)
        {
            if (char.IsUpper(text[i]) && text[i - 1] != ' ')
                newText.Append(' ');
            newText.Append(text[i]);
        }

        return newText.ToString();
    }

    public static string UppercaseWords(string value)
    {
        char[] array = value.ToCharArray();
        if (array.Length >= 1)
        {
            if (char.IsLower(array[0]))
            {
                array[0] = char.ToUpper(array[0]);
            }
        }

        for (int i = 1; i < array.Length; i++)
        {
            if (array[i - 1] == ' ')
            {
                if (char.IsLower(array[i]))
                {
                    array[i] = char.ToUpper(array[i]);
                }
            }
        }

        return new string(array);
    }
}