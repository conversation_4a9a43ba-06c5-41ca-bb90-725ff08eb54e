using Newtonsoft.Json;
using SendGrid;
using SendGrid.Helpers.Mail;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web;
using OnePage.Common;
using OnePage.Models;

namespace OnePage.Services;

public class EmailServices
{
    public static class SendGridAppConstants
    {
        public static string latestSendgrid { get; set; } =
            "*********************************************************************"; //"*********************************************************************";

        public static string SendGridDefaultSenderEmail = "<EMAIL>";
        public static string NotificarSendGridDefaultSenderEmail = "<EMAIL>";

        //////sendgrid for OTP and Claim mails -- mapit4me2
        //public static string OtpSDKey = "*********************************************************************";
        //public static string OtpSDEmail = "<EMAIL>";
        //public static string EnglishOTPTemplateId = "6d5345a3-1859-4ef5-91de-b3d79f59ccd5";
        //public static string EnglishVerifyTemplateId = "9d355767-7a54-49e4-8b42-3f6e18d6d0f7";
        //public static string SpanishOTPTemplateId = "f6425863-9f1a-4b37-8ab6-0ac31c6afcdb";
        //public static string SpanishVerifyTemplateId = "7035dc9b-e623-4e6c-8033-40cbe6279003";

        //sendgrid for OTP and Claim mails
        public static string OtpSDKey { get; set; } =
            "*********************************************************************";

        public static string OtpSDEmail = "<EMAIL>";
        public static string MagicLinkOtpSDEmail = "<EMAIL>";
        public static string EnglishOTPTemplateId = "82dc40f5-9dd8-4b56-950e-8b9c1125d85c";
        public static string EnglishVerifyTemplateId = "04e19f79-31e0-471f-b84c-d96d928e17fb";
        public static string SpanishOTPTemplateId = "f6425863-9f1a-4b37-8ab6-0ac31c6afcdb";
        public static string SpanishVerifyTemplateId = "7035dc9b-e623-4e6c-8033-40cbe6279003";
        public static string EnglishMagicLinkTemplateId = "cfda26af-ddb8-4581-b4a2-0800ab496ba8";

        public static string SendGridApiKey { get; set; } =
            "*********************************************************************";
    }

    public static async Task<string> SendGridTestMail(string fname, string body, string emailId)
    {
        try
        {
            //var apiKey = Environment.GetEnvironmentVariable(SendGridAppConstants.SendGridKeyOld);
            //var client = new SendGridClient(SendGridAppConstants.SendGridKeyOld);
            //var msg = new SendGridMessage()
            //{
            //    From = new EmailAddress(SendGridAppConstants.SendGridDefaultSenderEmail, "<EMAIL>"),
            //    Subject = "Reciept Mail",
            //};
            //var content = new List<Content>();

            //var value = "Hi " + fname + ", Please verify your email address " + "<EMAIL>" + " by clicking on link below \n <a href= target=_nblank>Verify</a>";
            //content.Add(new Content() { Type = "text/plain", Value = body });
            //msg.Contents = content;//new Content() { Type = "text/plain", Value = "Hello, World!" }
            //var personalize = new List<Personalization>();

            //var tos = new List<EmailAddress>();
            //tos.Add(new EmailAddress() { Name = fname, Email = emailId });
            //Dictionary<string, string> substitutions = new Dictionary<string, string>();
            //substitutions.Add("-FirstName-", fname);
            //substitutions.Add("-emailId-", emailId);
            //personalize.Add(new Personalization() { Subject = "Reciept Mail", Tos = tos, Substitutions = substitutions });
            //msg.Personalizations = personalize;
            //var response = await client.SendEmailAsync(msg);
            return "";
        }
        catch (Exception ex)
        {
            throw;
        }
    }
    //public static async Task<string> SendGridOTPMail(string toEmail, string fname="", string verificationCode="", string url="", int type=0)
    //{
    //    try
    //    {

    //        var apiKey = Environment.GetEnvironmentVariable(SendGridAppConstants.OtpSDKey);
    //        var client = new SendGridClient(SendGridAppConstants.OtpSDKey);
    //        var msg = new SendGridMessage()
    //        {
    //            From = new EmailAddress(SendGridAppConstants.OtpSDEmail, SendGridAppConstants.OtpSDEmail),
    //            Subject = "Reciept Mail",
    //        };
    //        var content = new List<Content>();
    //        string bodyvalue = "";
    //        if (type == 0)
    //        {
    //             bodyvalue = @"<a href=""https://www.whatelse.io/""><img class=""max-width"" border=""0"" align=""right"" style=""display:block;color:#000000;text-decoration:none;font-family:Helvetica, arial, sans-serif;font-size:16px;max-width:15% !important;width:10%;height:auto !important;"" src=""https://marketing-image-production.s3.amazonaws.com/uploads/2f8cbc7b759af55f9fb956fd4e242c1acd36a1391398698c32b8a93f0c8f26dc5f0a1667153b72180002248e032293fe15bf0d5d11af503ac6559076c82826ce.png"" alt=""1Page"" width=""90""></a><br><br><br><div><span style=""font - family:verdana,geneva,sans - serif; ""><span style=""font - size:16px; "">Welcome!</span></span></div><br><div><span style=""font - family:verdana,geneva,sans - serif; ""><span style=""font - size:16px; "">&nbsp; &nbsp; &nbsp; &nbsp;<br><br><br>Either you or someone has requested OTP for creating account with WhatElse. <br><br>OTP for registration is  <strong> verificationCode </strong> <br><br>If you have not requested, please send  <NAME_EMAIL>, so that we can take action. <br></span></span></div><div>&nbsp;</div><br><br><div><span style = ""font-family:verdana,geneva,sans-serif;""><span style = ""font-size:16px;""> Thanks,<br> WhatElse </span></span></ div >";
    //            bodyvalue = bodyvalue.Replace("verificationCode", verificationCode);
    //        }
    //        else if (type == 1)
    //        {
    //             bodyvalue = @"<a href=""https://www.whatelse.io/""><img class=""max-width"" border=""0"" style=""display:block;color:#000000;text-decoration:none;font-family:Helvetica, arial, sans-serif;font-size:16px;max-width:15% !important;width:15%;height:auto !important;"" src=""https://marketing-image-production.s3.amazonaws.com/uploads/2f8cbc7b759af55f9fb956fd4e242c1acd36a1391398698c32b8a93f0c8f26dc5f0a1667153b72180002248e032293fe15bf0d5d11af503ac6559076c82826ce.png"" alt=""1Page"" width=""90""></a><div>&nbsp;</div><div><span style=""font-family:tahoma,geneva,sans-serif;"">Hi&nbsp;-FirstName-,</span></div><div>&nbsp;</div><div><span style=""font-family:tahoma,geneva,sans-serif;"">Thank you for signing up for WhatElse. Please click on the button below to verify your email. If you have received this by accident, please ignore this email.</span></div><div>&nbsp;</div><div>&nbsp;</div><div>&nbsp;</div><a style=""background-color:#7d06e7;border:1px solid #E7061D;border-color:#7d06e7;border-radius:6px;border-width:1px;color:#ffffff;display:inline-block;font-family:arial,helvetica,sans-serif;font-size:16px;font-weight:normal;letter-spacing:0px;line-height:16px;padding:12px 18px 12px 18px;text-align:center;text-decoration:none;width:40%"" href=""-accountConfirmationLink-"" target=""_blank"">Verify Your Email</a><div><span style=""font-family: tahoma, geneva, sans-serif; font-size: 14px; text-align: inherit; background-color: transparent;"">Thanks,</span></div><div><div style=""font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 300; font-family: verdana, geneva, sans-serif; font-size: 14px;""><span style=""font-family:tahoma,geneva,sans-serif;"">WhatElse Team</span></div></div>";
    //            //bodyvalue = bodyvalue.Replace("verificationCode", verificationCode);
    //        }

    //        //var value = "Hi " + fname + ", Please verify your email address " + "<EMAIL>" + " by clicking on link below \n <a href= target=_nblank>Verify</a>";
    //        content.Add(new Content() { Type = "text/plain", Value = bodyvalue });
    //        msg.Contents = content;//new Content() { Type = "text/plain", Value = "Hello, World!" }
    //        var personalize = new List<Personalization>();

    //        var tos = new List<EmailAddress>();
    //        tos.Add(new EmailAddress() { Name = fname, Email = toEmail });
    //        Dictionary<string, string> substitutions = new Dictionary<string, string>();
    //        substitutions.Add("-FirstName-", fname);
    //        substitutions.Add("-url-", url);
    //        personalize.Add(new Personalization() { Subject = "Reciept Mail", Tos = tos, Substitutions = substitutions });
    //        msg.Personalizations = personalize;
    //        var response = await client.SendEmailAsync(msg);
    //        return response.ToString();

    //    }
    //    catch (Exception ex)
    //    {
    //        throw;
    //    }
    //}
    //public static async Task<string> SendgridMagicLink(string link, string toEmailAddress, string deviceId, bool shouldCCCompliance = false)
    //{
    //    try
    //    {
    //        //string templateId = (isOtp == true) ?((languageId== "1") ?SendGridAppConstants.EnglishOTPTemplateId : SendGridAppConstants.SpanishOTPTemplateId) : ((languageId == "1") ? SendGridAppConstants.EnglishVerifyTemplateId: SendGridAppConstants.SpanishVerifyTemplateId);
    //        string templateId = "d-722a2445a2cb4bb68552c98215b37b2c";
    //        var url = link +"?d="+ deviceId;
    //        var apiKey = "*********************************************************************";
    //        var client = new SendGridClient(apiKey);
    //        var msg = new SendGridMessage()
    //        {
    //            From = new EmailAddress(SendGridAppConstants.OtpSDEmail, "1Page"),
    //            Subject = "Sending with SendGrid is Fun",
    //        };
    //        var content = new List<Content>();
    //        var personalize = new List<Personalization>();

    //        var tos = new List<EmailAddress>();
    //        tos.Add(new EmailAddress() { Name = fname, Email = toEmailAddress });
    //        var ccs = new List<EmailAddress>();
    //        if (shouldCCCompliance)
    //        {
    //            ccs.Add(new EmailAddress() { Name = fname, Email = "<EMAIL>" });
    //        }
    //        Dictionary<string, string> substitutions = new Dictionary<string, string>();
    //        substitutions.Add("-FirstName-", fname);
    //        substitutions.Add("-emailId-", toEmailAddress);
    //        substitutions.Add("-accountConfirmationLink-", url);
    //        substitutions.Add("-VerificationCode-", verificationCode);
    //        substitutions.Add("-customerlink-", customerlink);
    //        substitutions.Add("-customerCreateNewPasswordlink-", customerCreateNewPasswordlink);
    //        if (shouldCCCompliance)
    //            personalize.Add(new Personalization() { Subject = "Activation Email", Ccs = ccs, Tos = tos, Substitutions = substitutions });
    //        else
    //            personalize.Add(new Personalization() { Subject = "Activation Email", Tos = tos, Substitutions = substitutions });

    //        msg.Personalizations = personalize;
    //        msg.TemplateId = templateId;

    //        var response = await client.SendEmailAsync(msg);
    //        return response.ToString();
    //    }
    //    catch (Exception ex)
    //    {
    //        return ex.Message;
    //    }
    //}

    public static async Task<string> SendGridOTPMail(bool isOtp, string fname, string verificationCode,
        string userEmailId, string toEmailAddress, string type = "e", string provisionId = "", string providerId = "",
        bool shouldCCCompliance = false)
    {
        try
        {
            //string templateId = (isOtp == true) ?((languageId== "1") ?SendGridAppConstants.EnglishOTPTemplateId : SendGridAppConstants.SpanishOTPTemplateId) : ((languageId == "1") ? SendGridAppConstants.EnglishVerifyTemplateId: SendGridAppConstants.SpanishVerifyTemplateId);
            string templateId = (isOtp == true)
                ? SendGridAppConstants.EnglishOTPTemplateId
                : SendGridAppConstants.EnglishVerifyTemplateId;
            var url = "https://auth.get1page.com/?id=" + userEmailId + "&s=" + type + "&provisionId=" + provisionId +
                      "&providerId=" + providerId;
            var customerlink = "https://customer.whatelse.io/#/login";
            var customerCreateNewPasswordlink =
                "https://customer.whatelse.io/Account/GeneratePassword?Id=" + verificationCode;
            var apiKey = Environment.GetEnvironmentVariable(SendGridAppConstants.OtpSDKey);
            var client = new SendGridClient(SendGridAppConstants.OtpSDKey);
            var msg = new SendGridMessage()
            {
                From = new EmailAddress(SendGridAppConstants.OtpSDEmail, "1Page"),
                Subject = "Sending with SendGrid is Fun",
            };
            var content = new List<Content>();
            var personalize = new List<Personalization>();

            var tos = new List<EmailAddress>();
            tos.Add(new EmailAddress() { Name = fname, Email = toEmailAddress });
            var bccs = new List<EmailAddress>();
            if (shouldCCCompliance)
            {
                bccs.Add(new EmailAddress() { Name = fname, Email = "<EMAIL>" });
            }

            Dictionary<string, string> substitutions = new Dictionary<string, string>();
            substitutions.Add("-FirstName-", fname);
            substitutions.Add("-emailId-", toEmailAddress);
            substitutions.Add("-accountConfirmationLink-", url);
            substitutions.Add("-VerificationCode-", verificationCode);
            substitutions.Add("-customerlink-", customerlink);
            substitutions.Add("-customerCreateNewPasswordlink-", customerCreateNewPasswordlink);
            if (shouldCCCompliance)
                personalize.Add(new Personalization()
                    { Subject = "Activation Email", Bccs = bccs, Tos = tos, Substitutions = substitutions });
            else
                personalize.Add(new Personalization()
                    { Subject = "Activation Email", Tos = tos, Substitutions = substitutions });

            msg.Personalizations = personalize;
            msg.TemplateId = templateId;

            var response = await client.SendEmailAsync(msg);
            return response.ToString();
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public static async Task<string> SendMagicLinkOTPMail(bool isOtp, string fname, string toEmailAddress,
        bool shouldCCCompliance = false, string magicLinkUrl = "")
    {
        try
        {
            string templateId = (isOtp == true)
                ? SendGridAppConstants.EnglishMagicLinkTemplateId
                : SendGridAppConstants.EnglishVerifyTemplateId;
            //var url = "https://verify.whatelse.io/?id=" + userEmailId + "&s=" + type + "&provisionId=" + provisionId + "&providerId=" + providerId;
            //var customerlink = "https://customer.whatelse.io/#/login";
            //var customerCreateNewPasswordlink = "https://customer.whatelse.io/Account/GeneratePassword?Id=" + verificationCode;
            //var apiKey = Environment.GetEnvironmentVariable(SendGridAppConstants.OtpSDKey);
            var client = new SendGridClient(SendGridAppConstants.latestSendgrid);
            var msg = new SendGridMessage()
            {
                From = new EmailAddress(SendGridAppConstants.MagicLinkOtpSDEmail, "1Page"),
                Subject = "1Page",
            };
            var content = new List<Content>();
            var personalize = new List<Personalization>();

            var tos = new List<EmailAddress>();
            tos.Add(new EmailAddress() { Name = fname, Email = toEmailAddress });
            var ccs = new List<EmailAddress>();
            if (shouldCCCompliance)
            {
                ccs.Add(new EmailAddress() { Name = fname, Email = "<EMAIL>" });
            }

            Dictionary<string, string> substitutions = new Dictionary<string, string>();
            substitutions.Add("-FirstName-", fname);
            substitutions.Add("-emailId-", toEmailAddress);
            substitutions.Add("-accountConfirmationLink-", magicLinkUrl);
            //substitutions.Add("-VerificationCode-", verificationCode);
            //substitutions.Add("-customerlink-", customerlink);
            //substitutions.Add("-customerCreateNewPasswordlink-", customerCreateNewPasswordlink);
            if (shouldCCCompliance)
                personalize.Add(new Personalization()
                    { Subject = "Activation Email", Bccs = ccs, Tos = tos, Substitutions = substitutions });
            else
                personalize.Add(new Personalization()
                    { Subject = "Activation Email", Tos = tos, Substitutions = substitutions });

            msg.Personalizations = personalize;
            msg.TemplateId = templateId;

            var response = await client.SendEmailAsync(msg);
            return response.ToString();
        }
        catch (Exception ex)
        {
            return ex.Message;
        }
    }

    public static async Task<string> SendGridErrorMail(PropertyModels.DataModels.ErrorMailModel errorMailModel)
    {
        try
        {
            //var emailId = "<EMAIL>";
            //var emailId = "<EMAIL>";
            var apiKey = Environment.GetEnvironmentVariable(SendGridAppConstants.latestSendgrid);
            var client = new SendGridClient(SendGridAppConstants.latestSendgrid);
            var msg = new SendGridMessage()
            {
                From = new EmailAddress(SendGridAppConstants.SendGridDefaultSenderEmail, "<EMAIL>"),
                Subject = "Error Message - 1",
            };
            var content = new List<Content>();

            var value = "URL :" + errorMailModel.Url + "\n" + "ErrorCode" + errorMailModel.ErrorCode + "\n"
                        + "\n" + "UserProviderId" + errorMailModel.UserProviderId + "\n" +
                        "ErrorMessage" + errorMailModel.ErrorMessage + "\n" + "IsMailSent" +
                        errorMailModel.IsMailSent + "\n" + "CreatedDate" +
                        errorMailModel.CreatedDate + "\n" + "Count" +
                        errorMailModel.Count + "\n" + "Payload" +
                        errorMailModel.Payload + "\n" + "ProvisionId" +
                        errorMailModel.ProvisionId + "\n" + " Sent from server side";
            if (!string.IsNullOrEmpty(errorMailModel.Email))
                value += "\n" + "Email: " + errorMailModel.Email + "\n";
            content.Add(new Content() { Type = "text/plain", Value = value });

            msg.Contents = content; //new Content() { Type = "text/plain", Value = "Hello, World!" }
            var personalize = new List<Personalization>();

            var tos = new List<EmailAddress>();
            tos.Add(new EmailAddress() { Name = "Error Message", Email = "<EMAIL>" });
            tos.Add(new EmailAddress() { Name = "Error Message", Email = "<EMAIL>" });
            tos.Add(new EmailAddress() { Name = "Error Message", Email = "<EMAIL>" });
            Dictionary<string, string> substitutions = new Dictionary<string, string>();
            // substitutions.Add("-FirstName-", fname);
            //substitutions.Add("-emailId-", /*emailId*/);
            personalize.Add(new Personalization()
                { Subject = "Error Message", Tos = tos, Substitutions = substitutions });
            msg.Personalizations = personalize;
            var response = await client.SendEmailAsync(msg);
            return response.ToString();
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public static async Task<string> ExotelTexting(string ExotelData, string data)
    {
        try
        {
            var emailId = "<EMAIL>";
            var apiKey = Environment.GetEnvironmentVariable(SendGridAppConstants.latestSendgrid);
            var client = new SendGridClient(SendGridAppConstants.latestSendgrid);
            var msg = new SendGridMessage()
            {
                From = new EmailAddress(SendGridAppConstants.SendGridDefaultSenderEmail, "<EMAIL>"),
                Subject = "Exotel Data",
            };
            var content = new List<Content>();

            var value = ExotelData;
            content.Add(new Content() { Type = "text/plain", Value = value });

            msg.Contents = content; //new Content() { Type = "text/plain", Value = "Hello, World!" }
            var personalize = new List<Personalization>();

            var tos = new List<EmailAddress>();
            tos.Add(new EmailAddress() { Name = data, Email = emailId });
            Dictionary<string, string> substitutions = new Dictionary<string, string>();
            // substitutions.Add("-FirstName-", fname);
            substitutions.Add("-emailId-", emailId);
            personalize.Add(new Personalization() { Subject = data, Tos = tos, Substitutions = substitutions });
            msg.Personalizations = personalize;
            var response = await client.SendEmailAsync(msg);
            return response.ToString();
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public static async Task<string> SendGridMail(string SendGridKey, string templateId, string fname,
        string verificationCode, string userEmailId, string toEmailAddress, Guid appid, string type = "e",
        string provisionId = "", string providerId = "", bool shouldCCCompliance = false)
    {
        try
        {
            var apiKey = Environment.GetEnvironmentVariable(SendGridKey);
            var client = new SendGridClient(SendGridKey);
            var url = "";
            if (appid == CommonData.MiofertaAppId || appid == Common.CommonData.NotificarAWSTestApp)
            {
                url = "http://verificar.notificar.cl/?id=" + userEmailId + "&s=" + type + "&provisionId=" +
                      provisionId + "&providerId=" + providerId;
            }
            else if (appid == Common.CommonData.WEAppId)
            {
                url = "https://auth.get1page.com/?id=" + userEmailId + "&s=" + type + "&provisionId=" + provisionId +
                      "&providerId=" + providerId;
            }
            else
            {
                url = "https://auth.get1page.com/?id=" + userEmailId + "&s=" + type + "&provisionId=" + provisionId +
                      "&providerId=" + providerId;
            }

            ///new code end
            var customerlink = "https://customer.whatelse.io/#/login";
            var customerCreateNewPasswordlink =
                "https://customer.whatelse.io/Account/GeneratePassword?Id=" + verificationCode;
            var msg = new SendGridMessage()
            {
                From = new EmailAddress(SendGridAppConstants.SendGridDefaultSenderEmail, "1Page"),
                Subject = "1Page",
            };
            var content = new List<Content>();
            var personalize = new List<Personalization>();

            var tos = new List<EmailAddress>();
            tos.Add(new EmailAddress() { Name = fname, Email = toEmailAddress });
            var bccs = new List<EmailAddress>();
            if (shouldCCCompliance)
            {
                bccs.Add(new EmailAddress() { Name = "For Records", Email = "<EMAIL>" });
            }

            Dictionary<string, string> substitutions = new Dictionary<string, string>();
            substitutions.Add("-FirstName-", fname);
            substitutions.Add("-emailId-", toEmailAddress);
            substitutions.Add("-accountConfirmationLink-", url);
            substitutions.Add("-VerificationCode-", verificationCode);
            substitutions.Add("-customerlink-", customerlink);
            substitutions.Add("-customerCreateNewPasswordlink-", customerCreateNewPasswordlink);
            if (shouldCCCompliance)
                personalize.Add(new Personalization()
                    { Subject = "Activation Email", Bccs = bccs, Tos = tos, Substitutions = substitutions });
            else
                personalize.Add(new Personalization()
                    { Subject = "Activation Email", Tos = tos, Substitutions = substitutions });

            msg.Personalizations = personalize;
            msg.TemplateId = templateId;

            var response = await client.SendEmailAsync(msg);
            return response.ToString();
        }
        catch (Exception ex)
        {
            Debug.WriteLine(ex.StackTrace);
            return ex.Message;
        }
    }

    public static async Task<string> SendGridAttachmentMail(string fname, string body, string emailId,
        string fileLocation, string filename, byte[] byteArray, string subjectdata = "")
    {
        try
        {
            var apiKey = Environment.GetEnvironmentVariable(SendGridAppConstants.latestSendgrid);
            var client = new SendGridClient(SendGridAppConstants.latestSendgrid);

            var from = new EmailAddress(SendGridAppConstants.SendGridDefaultSenderEmail, "1Page");
            var subject = subjectdata;
            var to = new EmailAddress(emailId);
            var msg = MailHelper.CreateSingleEmail(from, to, subject, body, "");
            var bytes = byteArray;
            var file = Convert.ToBase64String(bytes);
            msg.AddAttachment(filename, file);
            var personalize = new List<Personalization>();
            var tos = new List<EmailAddress>();
            tos.Add(new EmailAddress() { Name = fname, Email = emailId });
            var bccs = new List<EmailAddress>();
            bccs.Add(new EmailAddress() { Name = "1Page Admin", Email = "<EMAIL>" }); //<EMAIL>
            personalize.Add(new Personalization() { Subject = subject, Tos = tos }); //, Bccs= bccs });
            msg.Personalizations = personalize;
            var response = await client.SendEmailAsync(msg);
            return response.ToString();
        }
        catch (Exception ex)
        {
            throw;
        }
    }

    public static async Task SMTPMailForOtp(string verificationCode, string toMailId)
    {
        try
        {
            await Task.Run(() =>
            {
                System.Net.Mail.MailMessage mail = new System.Net.Mail.MailMessage
                {
                    From = new System.Net.Mail.MailAddress("<EMAIL>", "1Page")
                };
                System.Net.Mail.SmtpClient client = new System.Net.Mail.SmtpClient
                {
                    Port = 587,
                    DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network,
                    UseDefaultCredentials = false,
                    Host = "smtp.zoho.com",
                    EnableSsl = true,
                    Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
                };
                mail.Subject = "WhatElse OTP to login";
                //string bodyvalue = @"Welcome!</n>Either you or someone has requested OTP for creating account with WhatElse.</n></n>OTP for registration is verificationCode</n></n>If you have not requested, please send <NAME_EMAIL>, so that we can take action.</n></n>Thanks,</n>WhatElse";
                string bodyvalue =
                    @"<a href=""https://www.whatelse.io/""><img class=""max-width"" border=""0"" align=""right"" style=""display:block;color:#000000;text-decoration:none;font-family:Helvetica, arial, sans-serif;font-size:16px;max-width:15% !important;width:10%;height:auto !important;"" src=""https://marketing-image-production.s3.amazonaws.com/uploads/2f8cbc7b759af55f9fb956fd4e242c1acd36a1391398698c32b8a93f0c8f26dc5f0a1667153b72180002248e032293fe15bf0d5d11af503ac6559076c82826ce.png"" alt=""1Page"" width=""90""></a><br><br><br><div><span style=""font - family:verdana,geneva,sans - serif; ""><span style=""font - size:16px; "">Welcome!</span></span></div><br><div><span style=""font - family:verdana,geneva,sans - serif; ""><span style=""font - size:16px; "">&nbsp; &nbsp; &nbsp; &nbsp;<br><br><br>Either you or someone has requested OTP for creating account with WhatElse. <br><br>OTP for registration is  <strong> verificationCode </strong> <br><br>If you have not requested, please send  <NAME_EMAIL>, so that we can take action. <br></span></span></div><div>&nbsp;</div><br><br><div><span style = ""font-family:verdana,geneva,sans-serif;""><span style = ""font-size:16px;""> Thanks,<br> WhatElse </span></span></ div >";
                bodyvalue = bodyvalue.Replace("verificationCode", verificationCode);
                mail.IsBodyHtml = true;
                mail.Body = bodyvalue;
                mail.To.Add(toMailId);
                //mail.To.Add("<EMAIL>");
                //mail.To.Add("<EMAIL>");
                //mail.To.Add("<EMAIL>");
                //mail.To.Add("<EMAIL>");
                client.Send(mail);
            });
        }
        catch (Exception ex)
        {
        }
    }

    public static async Task SMTPErrorEmail(PropertyModels.ContactLinkingModel.ErrorMailModel2 errorModel)
    {
        try
        {
            await Task.Run(() =>
            {
                System.Net.Mail.MailMessage mail = new System.Net.Mail.MailMessage
                {
                    From = new System.Net.Mail.MailAddress("<EMAIL>", "Server Errors")
                };
                System.Net.Mail.SmtpClient client = new System.Net.Mail.SmtpClient
                {
                    Port = 587,
                    DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network,
                    UseDefaultCredentials = false,
                    Host = "smtp.zoho.com",
                    EnableSsl = true,
                    Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
                };
                //  Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
                mail.Subject = "Api Error";
                string bodyvalue = "URL: " + errorModel.Url + "\n"
                                   + "ErrorCode: " + errorModel.ErrorCode + "\n"
                                   + "UserProviderId: " + errorModel.UserProviderId + "\n"
                                   + "ErrorMessage: " + errorModel.ErrorMessage + "\n"
                                   + "IsMailSent: " + errorModel.IsMailSent + "\n"
                                   + "CreatedDate: " + errorModel.CreatedDate + "\n"
                                   + "Count: " + errorModel.Count;
                if (!string.IsNullOrEmpty(errorModel.EmailId))
                    bodyvalue += "\n" + "Email: " + errorModel.EmailId + "\n";
                if (!string.IsNullOrEmpty(errorModel.Method))
                    bodyvalue += "Method: " + errorModel.Method + "\n";
                bodyvalue += "Sent from server\n";
                mail.Body = bodyvalue;
                mail.To.Add("<EMAIL>");
                mail.To.Add("<EMAIL>");
                mail.To.Add("<EMAIL>");
                //mail.To.Add("<EMAIL>");
                client.Send(mail);
            });
        }
        catch (Exception ex)
        {
        }
    }

    public static async Task SMTPErrorEmail(PropertyModels.ContactDataModel.ErrorMailModel2 errorModel)
    {
        try
        {
            await Task.Run(() =>
            {
                System.Net.Mail.MailMessage mail = new System.Net.Mail.MailMessage
                {
                    From = new System.Net.Mail.MailAddress("<EMAIL>", "Server Errors")
                };
                System.Net.Mail.SmtpClient client = new System.Net.Mail.SmtpClient
                {
                    Port = 587,
                    DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network,
                    UseDefaultCredentials = false,
                    Host = "smtp.zoho.com",
                    EnableSsl = true,
                    Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
                };
                //  Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
                mail.Subject = "Api Error";
                string bodyvalue = "URL: " + errorModel.Url + "\n"
                                   + "ErrorCode: " + errorModel.ErrorCode + "\n"
                                   + "UserProviderId: " + errorModel.UserProviderId + "\n"
                                   + "ErrorMessage: " + errorModel.ErrorMessage + "\n"
                                   + "IsMailSent: " + errorModel.IsMailSent + "\n"
                                   + "CreatedDate: " + errorModel.CreatedDate + "\n"
                                   + "Count: " + errorModel.Count;
                if (!string.IsNullOrEmpty(errorModel.EmailId))
                    bodyvalue += "\n" + "Email: " + errorModel.EmailId + "\n";
                if (!string.IsNullOrEmpty(errorModel.Method))
                    bodyvalue += "Method: " + errorModel.Method + "\n";
                bodyvalue += "Sent from server\n";
                mail.Body = bodyvalue;
                mail.To.Add("<EMAIL>");
                mail.To.Add("<EMAIL>");
                mail.To.Add("<EMAIL>");
                //mail.To.Add("<EMAIL>");
                client.Send(mail);
            });
        }
        catch (Exception ex)
        {
        }
    }

    public static async Task SMTPSendCreditsInfo(PropertyModels.ContactDataModel.CreditEmailModel creditEmailModel)
    {
        await Task.Run(() =>
        {
            System.Net.Mail.MailMessage mail = new System.Net.Mail.MailMessage
            {
                From = new System.Net.Mail.MailAddress("<EMAIL>", "Credits expiring soon!")
            };
            System.Net.Mail.SmtpClient client = new System.Net.Mail.SmtpClient
            {
                Port = 587,
                DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network,
                UseDefaultCredentials = false,
                Host = "smtp.zoho.com",
                EnableSsl = true,
                Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
            };
            //  Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
            mail.Subject = creditEmailModel.OrgName + " " + creditEmailModel.Credits;
            string bodyvalue = "Credits info in subject.";
            bodyvalue += "Sent from server\n";
            mail.Body = bodyvalue;
            mail.To.Add("<EMAIL>");
            client.Send(mail);
        });
    }

    public static async Task SMTPSendCouponRedeemInfo(
        PropertyModels.ContactDataModel.CouponRedeemInfoModel couponRedeemInfoModel)
    {
        await Task.Run(() =>
        {
            System.Net.Mail.MailMessage mail = new System.Net.Mail.MailMessage
            {
                From = new System.Net.Mail.MailAddress("<EMAIL>", "Appsumo code redeemed")
            };
            System.Net.Mail.SmtpClient client = new System.Net.Mail.SmtpClient
            {
                Port = 587,
                DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network,
                UseDefaultCredentials = false,
                Host = "smtp.zoho.com",
                EnableSsl = true,
                Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
            };
            //  Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
            mail.Subject = couponRedeemInfoModel.EmailId + " Redeemed " + couponRedeemInfoModel.Coupon;
            string bodyvalue = "Appsumo coupon redeemed";
            mail.Body = bodyvalue;
            mail.To.Add("<EMAIL>");
            client.Send(mail);
        });
    }

    public static async Task SMTPSendNewUserLoginInfo(
        PropertyModels.ContactDataModel.UserLoginInfoModel userLoginInfoModel)
    {
        await Task.Run(() =>
        {
            System.Net.Mail.MailMessage mail = new System.Net.Mail.MailMessage
            {
                From = new System.Net.Mail.MailAddress("<EMAIL>", "New Signup in the 1Page")
            };
            System.Net.Mail.SmtpClient client = new System.Net.Mail.SmtpClient
            {
                Port = 587,
                DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network,
                UseDefaultCredentials = false,
                Host = "smtp.zoho.com",
                EnableSsl = true,
                Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
            };
            //  Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
            mail.Subject = "New User Signup with " + userLoginInfoModel.EmailId;
            string bodyvalue = userLoginInfoModel.EmailId + "-- New SignUp";
            mail.Body = bodyvalue;
            mail.To.Add("<EMAIL>");
            client.Send(mail);
        });
    }

    public static async Task
        SMTPSendEmail(
            PropertyModels.ContactDataModel.UserLoginInfoModel userLoginInfoModel) // email invalid from neverbounce
    {
        try
        {
            await Task.Run(() =>
            {
                System.Net.Mail.MailMessage mail = new System.Net.Mail.MailMessage
                {
                    From = new System.Net.Mail.MailAddress(userLoginInfoModel.From, userLoginInfoModel.Heading)
                };
                System.Net.Mail.SmtpClient client = new System.Net.Mail.SmtpClient
                {
                    Port = 587,
                    DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network,
                    UseDefaultCredentials = false,
                    Host = "smtp.zoho.com",
                    EnableSsl = true,
                    Credentials = new NetworkCredential("<EMAIL>", "H3110$%$")
                };
                //  Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
                mail.Subject = userLoginInfoModel.Subject + " " + userLoginInfoModel.EmailId;
                string bodyvalue = userLoginInfoModel.EmailId + userLoginInfoModel.Message;
                mail.Body = bodyvalue;
                if (userLoginInfoModel.ToList.Count > 0)
                {
                    foreach (var email in userLoginInfoModel.ToList)
                    {
                        mail.To.Add(email);
                    }
                }

                client.Send(mail);
            });
        }
        catch (Exception ex)
        {
        }
    }

    public static async Task SMTPSendTaskCreditInfo(PropertyModels.ContactDataModel.TaskCreditModel taskCreditInfoModel)
    {
        await Task.Run(() =>
        {
            System.Net.Mail.MailMessage mail = new System.Net.Mail.MailMessage
            {
                From = new System.Net.Mail.MailAddress("<EMAIL>", "Credits Added!")
            };
            System.Net.Mail.SmtpClient client = new System.Net.Mail.SmtpClient
            {
                Port = 587,
                DeliveryMethod = System.Net.Mail.SmtpDeliveryMethod.Network,
                UseDefaultCredentials = false,
                Host = "smtp.zoho.com",
                EnableSsl = true,
                Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
            };
            //  Credentials = new NetworkCredential("<EMAIL>", "NammaB3ngaluru$%$")
            mail.Subject = "5 Credits added!";
            string bodyvalue = taskCreditInfoModel.Message;
            mail.Body = bodyvalue;
            mail.To.Add(taskCreditInfoModel.EmailId);
            client.Send(mail);
        });
    }

    public static async Task SendSingleEmail(string DesignId, object TemplateData, string SenderId, string GroupId)
    {
        try
        {
            SingleSendEmailModel singleSendEmailModel = new SingleSendEmailModel();
            singleSendEmailModel.name = "Created Single Send";
            EmailConfig emailConfig = new EmailConfig();
            emailConfig.design_id = DesignId;
            emailConfig.editor = "design";
            // emailConfig.sender_id = SenderId;
            // emailConfig.suppression_group_id = GroupId;

            singleSendEmailModel.email_config = emailConfig;
            singleSendEmailModel.send_to.all = true;

            var json = JsonConvert.SerializeObject(singleSendEmailModel);

            var sendGridApiUrl = "https://api.sendgrid.com/v3/marketing/singlesends";
            var ApiKey = SendGridAppConstants.SendGridApiKey;
            var res = CommonData.SendGridRestApi(json, sendGridApiUrl, ApiKey);
        }
        catch (Exception ex)
        {
        }
    }

    public static async Task SendTransactionalEmail(string TemplateId, DynamicTemplateData TemplateData,
        List<string> ToList, string From)
    {
        try
        {
            SendGridEmailModel sendGridEmailModel = new SendGridEmailModel();
            sendGridEmailModel.from.email = From;

            List<Personalization1> personalizations = new List<Personalization1>();
            Personalization1 personalization = new Personalization1();
            List<To> listTo = new List<To>();
            if (ToList.Count > 0)
            {
                foreach (var mailid in ToList)
                {
                    To to = new To();
                    to.email = mailid;
                    listTo.Add(to);
                }
            }

            personalization.to = listTo;
            personalization.dynamic_template_data = TemplateData;

            personalizations.Add(personalization);

            sendGridEmailModel.personalizations = personalizations;
            sendGridEmailModel.template_id = TemplateId;

            var json = JsonConvert.SerializeObject(sendGridEmailModel);

            var sendGridApiUrl = "https://api.sendgrid.com/v3/mail/send";
            var ApiKey = SendGridAppConstants.SendGridApiKey;
            var res = CommonData.SendGridRestApi(json, sendGridApiUrl, ApiKey);
        }
        catch (Exception ex)
        {
        }
    }
}