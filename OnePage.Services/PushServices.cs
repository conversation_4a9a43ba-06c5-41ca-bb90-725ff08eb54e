using Newtonsoft.Json;
using System;
using System.IO;
using System.Net;
using System.Text;
using System.Threading.Tasks;

using OnePage.Common;
using OnePage.Models;


namespace OnePage.Services
{
    public class PushServices
    {
        public static string WebPush(string appId, string apiKey, string callId, string channel, string messageToDisplay, string heading, string phoneNumber = "", string userId = "", int userType = 0, int eventType = 0, string pastNotificationId = "", string deviceid = "", int callType = 0, string url = "")
        {
            //dev one signal
            //string apikey = "632e82a3-3ff7-49b5-8b65-2005eb3bbb7e";//appid
            //string authorization = "MDY1Mzg3NzYtMzhhNC00ZjVjLWJjMjEtZTI5MWYzZGY2OTli"; //rest apikey

            //prod 
            string AppId = appId;// "8faa9773-3b95-4654-b194-17265b41c317";//appid
            string APIKey = apiKey;// "NTJkMDQwYTgtNDczZS00MDE4LWIxZDMtYzlhYzlkYzkxY2E4";
            var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;

            request.KeepAlive = true;
            request.Method = "POST";
            request.ContentType = "application/json";

            request.Headers.Add("authorization", "Basic " + APIKey);
            var payload = "{"
                          + "\"app_id\": \"" + AppId + "\","
                          + "\"included_segments\": \"All\","
                          + "\"ios_badgeType\": \"Increase\","
                          + "\"ios_badgeCount\": 1,"
                         + "\"data\": { \"callId\": \"" + callId + "\" ,  \"eventType\": " + eventType + " , \"userId\" : \"" + userId + "\" , \"phoneNumber\" : \"" + phoneNumber + "\", \"callType\" : \"" + callType + "\", \"url\" : \"" + url + "\", \"userType\" : " + userType + " },"
                          //+ "\"data\": { \"callId\": \"" + callId + "\" ,  \"eventType\": " + eventType + " , \"userId\" : \"" + userId + "\" , \"phoneNumber\" : \"" + phoneNumber + "\", \"callType\" : \"" + callType + "\", \"userType\" : " + userType + " },"
                          + "\"contents\": { \"en\": \"" + messageToDisplay + "\"},"
                          + "\"pastNotifications\": { \"en\": \"" + pastNotificationId + "\"},"
                          + "\"headings\": { \"en\": \"" + heading + "\"},"
                          + "\"tags\" : [{\"key\": \"" + channel + "\", \"relation\": \"=\", \"value\": \"1\"}],"
                           + "\"priority\": 10 }";

            byte[] byteArray = Encoding.UTF8.GetBytes(payload);
            string responseContent = null;

            try
            {
                using (var writer = request.GetRequestStream())
                {
                    writer.Write(byteArray, 0, byteArray.Length);
                }
                string content = "";
                using (var response = request.GetResponse() as HttpWebResponse)
                {
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        responseContent = reader.ReadToEnd();
                        //dynamic item = serializer.Deserialize<object>(responseContent.ToString());
                        //count = item["recipients"];
                        content = responseContent.ToString();
                    }
                }
                return content;

            }
            catch (WebException ex)
            {

                System.Diagnostics.Debug.WriteLine(ex.Message);
                System.Diagnostics.Debug.WriteLine(new StreamReader(ex.Response.GetResponseStream()).ReadToEnd());
                return ex.ToString();
            }

        }

        public static string IOSPush(string appId, string apiKey, string callId, string channel, string messageToDisplay, string heading, string phoneNumber = "", string userId = "", int userType = 0, int eventType = 0, string pastNotificationId = "", string deviceid = "", int callType = 0, string url = "")
        {
            //prod one signal
            string AppId = appId;// "8a825933-8e50-485e-b7a3-6e99b07977b1";//appid
            string APIKey = apiKey;// "MWJmODQyODUtMzdhNC00NzYzLTlhZjItZWNmZmZiYWQ4NWFi";


            //dev one signal
            //string apikey = "b0072814-c05c-4208-88db-616ecc4787ab";//appid
            //string authorization = "ZDYwNTBiOGYtMGRkOS00OGFhLTg1YWItMTBhYTNhZTZmNDI3"; //rest apikey
            var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;

            request.KeepAlive = true;
            request.Method = "POST";
            request.ContentType = "application/json";

            request.Headers.Add("authorization", "Basic " + APIKey);
            var payload = "{"
                          + "\"app_id\": \"" + AppId + "\","
                          + "\"mutable_content\": \"" + true + "\","
                          + "\"included_segments\": \"All\","
                          + "\"ios_sound\": \"onepage.wav\","
                          + "\"ios_badgeType\": \"Increase\","
                          + "\"ios_badgeCount\": 1,"
                          + "\"data\": { \"callId\": \"" + callId + "\" ,  \"eventType\": " + eventType + " , \"userId\" : \"" + userId + "\" , \"phoneNumber\" : \"" + phoneNumber + "\", \"callType\" : \"" + callType + "\", \"url\" : \"" + url + "\", \"userType\" : " + userType + " },"
                          //+ "\"data\": { \"callId\": \"" + callId + "\" ,  \"eventType\": " + eventType + " , \"userId\" : \"" + userId + "\" , \"phoneNumber\" : \"" + phoneNumber + "\", \"callType\" : \"" + callType + "\", \"userType\" : " + userType + " },"
                          + "\"contents\": { \"en\": \"" + messageToDisplay + "\"},"
                          + "\"pastNotifications\": { \"en\": \"" + pastNotificationId + "\"},"
                          + "\"headings\": { \"en\": \"" + heading + "\"},"
                          + "\"tags\" : [{\"key\": \"" + channel + "\", \"relation\": \"=\", \"value\": \"1\"}],"
                          + "\"priority\": 10 }";
            byte[] byteArray = Encoding.UTF8.GetBytes(payload);
            string responseContent = null;

            try
            {
                using (var writer = request.GetRequestStream())
                {
                    writer.Write(byteArray, 0, byteArray.Length);
                }
                string content = "";
                using (var response = request.GetResponse() as HttpWebResponse)
                {
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        responseContent = reader.ReadToEnd();
                        //dynamic item = serializer.Deserialize<object>(responseContent.ToString());
                        //count = item["recipients"];
                        content = responseContent.ToString();
                    }
                }
                return content;

            }
            catch (WebException ex)
            {

                System.Diagnostics.Debug.WriteLine(ex.Message);
                System.Diagnostics.Debug.WriteLine(new StreamReader(ex.Response.GetResponseStream()).ReadToEnd());
                return ex.ToString();
            }
        }

        public static string IOSPushWake(string appId, string apiKey, string callId, string channel, string messageToDisplay, string heading, string phoneNumber = "", string userId = "", int userType = 0, int eventType = 0, string pastNotificationId = "", string deviceid = "", int callType = 0, string url = "")
        {
            //prod one signal
            string AppId = appId;// "8a825933-8e50-485e-b7a3-6e99b07977b1";//appid
            string APIKey = apiKey;// "MWJmODQyODUtMzdhNC00NzYzLTlhZjItZWNmZmZiYWQ4NWFi";


            //dev one signal
            //string apikey = "b0072814-c05c-4208-88db-616ecc4787ab";//appid
            //string authorization = "ZDYwNTBiOGYtMGRkOS00OGFhLTg1YWItMTBhYTNhZTZmNDI3"; //rest apikey
            var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;

            request.KeepAlive = true;
            request.Method = "POST";
            request.ContentType = "application/json";

            request.Headers.Add("authorization", "Basic " + APIKey);
            var payload = "{"
                          + "\"app_id\": \"" + AppId + "\","
                          + "\"mutable_content\": \"" + true + "\","
                          + "\"content_available\": 1,"
                          + "\"included_segments\": \"All\","
                          + "\"ios_sound\": \"onepage.wav\","
                          + "\"ios_badgeType\": \"Increase\","
                          + "\"ios_badgeCount\": 1,"
                          + "\"data\": { \"callId\": \"" + callId + "\" ,  \"eventType\": " + eventType + " , \"userId\" : \"" + userId + "\" , \"phoneNumber\" : \"" + phoneNumber + "\", \"callType\" : \"" + callType + "\", \"url\" : \"" + url + "\", \"userType\" : " + userType + " },"
                          //+ "\"data\": { \"callId\": \"" + callId + "\" ,  \"eventType\": " + eventType + " , \"userId\" : \"" + userId + "\" , \"phoneNumber\" : \"" + phoneNumber + "\", \"callType\" : \"" + callType + "\", \"userType\" : " + userType + " },"
                          + "\"contents\": { \"en\": \"" + messageToDisplay + "\"},"
                          + "\"pastNotifications\": { \"en\": \"" + pastNotificationId + "\"},"
                          + "\"headings\": { \"en\": \"" + heading + "\"},"
                          + "\"tags\" : [{\"key\": \"" + channel + "\", \"relation\": \"=\", \"value\": \"1\"}],"
                          + "\"priority\": 10 }";
            byte[] byteArray = Encoding.UTF8.GetBytes(payload);
            string responseContent = null;

            try
            {
                using (var writer = request.GetRequestStream())
                {
                    writer.Write(byteArray, 0, byteArray.Length);
                }
                string content = "";
                using (var response = request.GetResponse() as HttpWebResponse)
                {
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        responseContent = reader.ReadToEnd();
                        //dynamic item = serializer.Deserialize<object>(responseContent.ToString());
                        //count = item["recipients"];
                        content = responseContent.ToString();
                    }
                }
                return content;

            }
            catch (WebException ex)
            {

                System.Diagnostics.Debug.WriteLine(ex.Message);
                System.Diagnostics.Debug.WriteLine(new StreamReader(ex.Response.GetResponseStream()).ReadToEnd());
                return ex.ToString();
            }
        }

        public static string AndroidPush(string appId, string apiKey, string callId, string channel, string messageToDisplay, string heading, string phoneNumber = "", string userId = "", int userType = 0, int eventType = 0, string pastNotificationId = "", string deviceid = "", int callType = 0, string url = "")
        {
            //prod one signal
            string AppId = appId;// "b8b36e3c-ca27-4bad-be04-de84fb1b019c";//appid
            string APIKey = apiKey;// "MmFmZjk4ODAtMmFkYy00N2Y2LThjOTEtM2Q3NzMwMjRkYzMz"; //rest apikey
            var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;

            request.KeepAlive = true;
            request.Method = "POST";
            request.ContentType = "application/json";

            request.Headers.Add("authorization", "Basic " + APIKey);

            var payload = "{"
                          + "\"app_id\": \"" + AppId + "\","
                          + "\"mutable_content\": \"" + true + "\","
                          + "\"included_segments\": \"All\","
                          + "\"ios_badgeType\": \"Increase\","
                          + "\"ios_badgeCount\": 1,"
                          + "\"data\": { \"callId\": \"" + callId + "\" ,  \"eventType\": " + eventType + " , \"userId\" : \"" + userId + "\" , \"phoneNumber\" : \"" + phoneNumber + "\", \"callType\" : \"" + callType + "\", \"url\" : \"" + url + "\", \"userType\" : " + userType + " },"
                          + "\"contents\": { \"en\": \"" + messageToDisplay + "\"},"
                          + "\"pastNotifications\": { \"en\": \"" + pastNotificationId + "\"},"
                          + "\"headings\": { \"en\": \"" + heading + "\"},"
                          + "\"existing_android_channel_id\": \"io.whatelse.mobile.calls\","
                          + "\"tags\" : [{\"key\": \"" + channel + "\", \"relation\": \"=\", \"value\": \"1\"}],"
                          + "\"priority\": 10 }";

            byte[] byteArray = Encoding.UTF8.GetBytes(payload);
            string responseContent = null;

            try
            {
                using (var writer = request.GetRequestStream())
                {
                    writer.Write(byteArray, 0, byteArray.Length);
                }
                string content = "";
                using (var response = request.GetResponse() as HttpWebResponse)
                {
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        responseContent = reader.ReadToEnd();
                        //dynamic item = serializer.Deserialize<object>(responseContent.ToString());
                        //count = item["recipients"];
                        content = responseContent.ToString();
                    }
                }

                return content;
            }
            catch (WebException ex)
            {

                System.Diagnostics.Debug.WriteLine(ex.Message);
                System.Diagnostics.Debug.WriteLine(new StreamReader(ex.Response.GetResponseStream()).ReadToEnd());
                return ex.ToString();
            }
        }

        public static string DevicePush(string appId, string apiKey, string callId, string channel, string messageToDisplay, string heading, string phoneNumber = "", string userId = "", int userType = 0, int eventType = 0, string pastNotificationId = "", string deviceid = "", int callType = 0, string url = "")
        {
            //prod one signal
            string AppId = appId;// "b8b36e3c-ca27-4bad-be04-de84fb1b019c";//appid
            string APIKey = apiKey;// "MmFmZjk4ODAtMmFkYy00N2Y2LThjOTEtM2Q3NzMwMjRkYzMz"; //rest apikey
            var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;

            request.KeepAlive = true;
            request.Method = "POST";
            request.ContentType = "application/json";

            request.Headers.Add("authorization", "Basic " + APIKey);

            var payload = "{"
                          + "\"app_id\": \"" + AppId + "\","
                          + "\"mutable_content\": \"" + true + "\","
                          + "\"included_segments\": \"All\","
                          + "\"ios_badgeType\": \"Increase\","
                          + "\"ios_badgeCount\": 1,"
                          + "\"data\": { \"callId\": \"" + callId + "\" ,  \"eventType\": " + eventType + " , \"userId\" : \"" + userId + "\" , \"phoneNumber\" : \"" + phoneNumber + "\", \"callType\" : \"" + callType + "\", \"url\" : \"" + url + "\", \"userType\" : " + userType + " },"
                          + "\"contents\": { \"en\": \"" + messageToDisplay + "\"},"
                          + "\"pastNotifications\": { \"en\": \"" + pastNotificationId + "\"},"
                          + "\"headings\": { \"en\": \"" + heading + "\"},"
                          + "\"tags\" : [{\"key\": \"" + channel + "\", \"relation\": \"=\", \"value\": \"1\"}],"
                          + "\"priority\": 10 }";

            byte[] byteArray = Encoding.UTF8.GetBytes(payload);
            string responseContent = null;

            try
            {
                using (var writer = request.GetRequestStream())
                {
                    writer.Write(byteArray, 0, byteArray.Length);
                }
                string content = "";
                using (var response = request.GetResponse() as HttpWebResponse)
                {
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        responseContent = reader.ReadToEnd();
                        //dynamic item = serializer.Deserialize<object>(responseContent.ToString());
                        //count = item["recipients"];
                        content = responseContent.ToString();
                    }
                }

                return content;
            }
            catch (WebException ex)
            {

                System.Diagnostics.Debug.WriteLine(ex.Message);
                System.Diagnostics.Debug.WriteLine(new StreamReader(ex.Response.GetResponseStream()).ReadToEnd());
                return ex.ToString();
            }
        }

        public static string DevicePushForLogin(string appId, string apiKey, string callId, string channel, string messageToDisplay, string heading, string phoneNumber = "", string userId = "", int userType = 0, int eventType = 0, string pastNotificationId = "", string deviceid = "", int callType = 0, string url = "", string tempAuthUrl = "", string emailId = "")
        {
            //prod one signal
            string AppId = appId;// "b8b36e3c-ca27-4bad-be04-de84fb1b019c";//appid
            string APIKey = apiKey;// "MmFmZjk4ODAtMmFkYy00N2Y2LThjOTEtM2Q3NzMwMjRkYzMz"; //rest apikey
            var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;

            request.KeepAlive = true;
            request.Method = "POST";
            request.ContentType = "application/json";

            request.Headers.Add("authorization", "Basic " + APIKey);

            var payload = "{"
                          + "\"app_id\": \"" + AppId + "\","
                          + "\"mutable_content\": \"" + true + "\","
                          + "\"included_segments\": \"All\","
                          + "\"ios_badgeType\": \"Increase\","
                          + "\"ios_badgeCount\": 1,"
                          + "\"data\": { \"callId\": \"" + callId + "\" ,  \"eventType\": " + eventType + " , \"userId\" : \"" + userId + "\" , \"phoneNumber\" : \"" + phoneNumber + "\", \"callType\" : \"" + callType + "\", \"url\" : \"" + url + "\", \"authUrl\" : \"" + tempAuthUrl + "\", \"emailId\" : \"" + emailId + "\", \"userType\" : " + userType + " },"
                          + "\"contents\": { \"en\": \"" + messageToDisplay + "\"},"
                          + "\"pastNotifications\": { \"en\": \"" + pastNotificationId + "\"},"
                          + "\"headings\": { \"en\": \"" + heading + "\"},"
                          + "\"tags\" : [{\"key\": \"" + channel + "\", \"relation\": \"=\", \"value\": \"1\"}],"
                          + "\"priority\": 10 }";

            byte[] byteArray = Encoding.UTF8.GetBytes(payload);
            string responseContent = null;

            try
            {
                using (var writer = request.GetRequestStream())
                {
                    writer.Write(byteArray, 0, byteArray.Length);
                }
                string content = "";
                using (var response = request.GetResponse() as HttpWebResponse)
                {
                    using (var reader = new StreamReader(response.GetResponseStream()))
                    {
                        responseContent = reader.ReadToEnd();
                        //dynamic item = serializer.Deserialize<object>(responseContent.ToString());
                        //count = item["recipients"];
                        content = responseContent.ToString();
                    }
                }

                return content;
            }
            catch (WebException ex)
            {

                System.Diagnostics.Debug.WriteLine(ex.Message);
                System.Diagnostics.Debug.WriteLine(new StreamReader(ex.Response.GetResponseStream()).ReadToEnd());
                return ex.ToString();
            }
        }

        public static string SignalPush(string appId, string apiKey, string channel, string messageToDisplay, string heading, NotifyPayload notifyPayload, string pastNotificationId = "")
        {
            string output = "";
            string payloadString = JsonConvert.SerializeObject(notifyPayload);
            //prod one signal
            try
            {
                string AppId = appId;// "b8b36e3c-ca27-4bad-be04-de84fb1b019c";//appid
                string APIKey = apiKey;// "MmFmZjk4ODAtMmFkYy00N2Y2LThjOTEtM2Q3NzMwMjRkYzMz"; //rest apikey
                var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;

                request.KeepAlive = true;
                request.Method = "POST";
                request.ContentType = "application/json";

                request.Headers.Add("authorization", "Basic " + APIKey);

                var payload = "{"
                              + "\"app_id\": \"" + AppId + "\","
                              + "\"mutable_content\": \"" + true + "\","
                              + "\"included_segments\": \"All\","
                              + "\"ios_badgeType\": \"Increase\","
                              + "\"ios_badgeCount\": 1,"
                              + "\"data\": " + payloadString + ","
                              + "\"contents\": { \"en\": \"" + messageToDisplay + "\"},"
                              + "\"pastNotifications\": { \"en\": \"" + pastNotificationId + "\"},"
                              + "\"headings\": { \"en\": \"" + heading + "\"},"
                              + "\"tags\" : [{\"key\": \"" + channel + "\", \"relation\": \"=\", \"value\": \"1\"}],"
                              + "\"priority\": 10 }";

                byte[] byteArray = Encoding.UTF8.GetBytes(payload);
                string responseContent = null;

                try
                {
                    using (var writer = request.GetRequestStream())
                    {
                        writer.Write(byteArray, 0, byteArray.Length);
                    }
                    string content = "";
                    using (var response = request.GetResponse() as HttpWebResponse)
                    {
                        using (var reader = new StreamReader(response.GetResponseStream()))
                        {
                            responseContent = reader.ReadToEnd();
                            //dynamic item = serializer.Deserialize<object>(responseContent.ToString());
                            //count = item["recipients"];
                            content = responseContent.ToString();
                        }
                    }

                    output = content;
                }
                catch (WebException ex)
                {
                    System.Diagnostics.Debug.WriteLine(ex.Message);
                    System.Diagnostics.Debug.WriteLine(new StreamReader(ex.Response.GetResponseStream()).ReadToEnd());
                    output = ex.ToString();
                }
            }
            catch (Exception) { }

            string signalUrl = "https://wesignalr.azurewebsites.net/api/sendmessage?groupname=" + channel;
            CommonData.GenericMethodReturnResponse(payloadString, signalUrl, 1);

            return output;
        }
    }
}